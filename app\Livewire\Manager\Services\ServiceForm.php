<?php

namespace App\Livewire\Manager\Services;

use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;
use Illuminate\Support\Str;

#[Layout('components.layouts.manager')]
class ServiceForm extends Component
{
    use WithFileUploads;

    public $serviceId;
    public $isEditMode = false;

    // Service Fields
    public $category_id;
    public $name;
    public $description;
    public $price;
    public $discount_price;
    public $duration; // minutes
    public $buffer_time = 0; // minutes
    public $image;
    public $oldImage;
    public $gallery = []; // new uploads
    public $oldGallery = []; // existing gallery
    public $loyalty_points = 0;

    // Toggles
    public $is_active = true;
    public $is_featured = false;
    public $is_available_online = true;

    // Relationships
    public $selectedStaff = [];

    protected function rules()
    {
        return [
            'category_id' => 'required|exists:service_categories,id',
            'name' => 'required|min:3|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'duration' => 'required|integer|min:5',
            'buffer_time' => 'required|integer|min:0',
            'image' => $this->isEditMode ? 'nullable|image|max:2048' : 'required|image|max:2048',
            'gallery.*' => 'nullable|image|max:2048',
            'loyalty_points' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_available_online' => 'boolean',
            'selectedStaff' => 'array',
        ];
    }

    public function mount($id = null)
    {
        if ($id) {
            $this->isEditMode = true;
            $this->serviceId = $id;
            $this->loadService($id);
        } else {
            // Pre-select some staff if available? No, let user choose.
        }
    }

    public function loadService($id)
    {
        $service = Service::with('staff')->findOrFail($id);

        $this->category_id = $service->category_id;
        $this->name = $service->name;
        $this->description = $service->description;
        $this->price = $service->price;
        $this->discount_price = $service->discount_price;
        $this->duration = $service->duration;
        $this->buffer_time = $service->buffer_time;
        $this->oldImage = $service->image;
        $this->oldGallery = $service->gallery ?? [];
        $this->loyalty_points = $service->loyalty_points;
        $this->is_active = $service->is_active;
        $this->is_featured = $service->is_featured;
        $this->is_available_online = $service->is_available_online;

        $this->selectedStaff = $service->staff->pluck('id')->map(fn($id) => (string) $id)->toArray();
    }

    public function save()
    {
        $this->validate();

        $data = [
            'category_id' => $this->category_id,
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'description' => $this->description,
            'price' => $this->price,
            'discount_price' => $this->discount_price ?: null,
            'duration' => $this->duration,
            'buffer_time' => $this->buffer_time,
            'loyalty_points' => $this->loyalty_points,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
            'is_available_online' => $this->is_available_online,
        ];

        // Handle Main Image
        if ($this->image) {
            $data['image'] = $this->image->store('services', 'public');
            // Cleanup old image if edit mode...
        }

        // Handle Gallery
        $galleryPaths = $this->oldGallery;
        foreach ($this->gallery as $photo) {
            $galleryPaths[] = $photo->store('services/gallery', 'public');
        }
        $data['gallery'] = $galleryPaths;

        if ($this->isEditMode) {
            $service = Service::findOrFail($this->serviceId);
            $service->update($data);
            $message = 'Service updated successfully.';
        } else {
            $service = Service::create($data);
            $message = 'Service created successfully.';
        }

        // Sync Staff
        $service->staff()->sync($this->selectedStaff);

        session()->flash('message', $message);
        return redirect()->route('manager.services.index');
    }

    public function removeGalleryImage($index)
    {
        if (isset($this->oldGallery[$index])) {
            array_splice($this->oldGallery, $index, 1);
        }
    }

    public function render()
    {
        $categories = ServiceCategory::where('is_active', true)->orderBy('name')->get();
        $staffMembers = User::where('role', 'staff')->where('is_active', true)->orderBy('name')->get();

        return view('livewire.manager.services.service-form', [
            'categories' => $categories,
            'staffMembers' => $staffMembers,
        ]);
    }
}
