<?php

use Illuminate\Support\Facades\Route;
use Laravel\Fortify\Features;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('landing');
})->name('home');

// Google OAuth Routes
Route::get('/auth/google/redirect', [App\Http\Controllers\GoogleAuthController::class, 'redirect'])->name('google.redirect');
Route::get('/auth/google/callback', [App\Http\Controllers\GoogleAuthController::class, 'callback'])->name('google.callback');


Route::get('dashboard', function () {
    if (auth()->user()->role === 'manager') {
        return redirect()->route('manager.dashboard');
    }
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::view('manager/dashboard', 'manager.dashboard')
    ->middleware(['auth', 'verified', 'role:manager'])
    ->name('manager.dashboard');

// Manager Feature Routes
Route::middleware(['auth', 'verified', 'role:manager'])->prefix('manager')->group(function () {
    Route::get('appointments/calendar', App\Livewire\Manager\Appointments\Calendar::class)->name('manager.appointments.calendar');
    Route::get('appointments', App\Livewire\Manager\Appointments\Index::class)->name('manager.appointments.index');
    Route::get('team', App\Livewire\Manager\Team\Index::class)->name('manager.team.index');

    // Services Management
    Route::get('services', App\Livewire\Manager\Services\Index::class)->name('manager.services.index');
    Route::get('services/categories', App\Livewire\Manager\Services\CategoryManager::class)->name('manager.services.categories');
    Route::get('services/create', App\Livewire\Manager\Services\ServiceForm::class)->name('manager.services.create');
    Route::get('services/{id}/edit', App\Livewire\Manager\Services\ServiceForm::class)->name('manager.services.edit');



    Route::get('clients', [App\Http\Controllers\Manager\ClientController::class, 'index'])->name('manager.clients.index');
    Route::post('clients', [App\Http\Controllers\Manager\ClientController::class, 'store'])->name('manager.clients.store');
    Route::put('clients/{id}', [App\Http\Controllers\Manager\ClientController::class, 'update'])->name('manager.clients.update');
    Route::post('clients/{id}/toggle-active', [App\Http\Controllers\Manager\ClientController::class, 'toggleActive'])->name('manager.clients.toggle-active');
    Route::delete('clients/{id}', [App\Http\Controllers\Manager\ClientController::class, 'destroy'])->name('manager.clients.destroy');
});

// Manager Settings Routes
Route::middleware(['auth', 'role:manager'])->prefix('manager')->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'manager.settings.profile')->name('manager.profile.edit');
    Volt::route('settings/password', 'manager.settings.password')->name('manager.user-password.edit');
    Volt::route('settings/appearance', 'manager.settings.appearance')->name('manager.appearance.edit');

    Volt::route('settings/two-factor', 'manager.settings.two-factor')
        ->middleware(
            when(
                Features::canManageTwoFactorAuthentication()
                && Features::optionEnabled(Features::twoFactorAuthentication(), 'confirmPassword'),
                ['password.confirm'],
                [],
            ),
        )
        ->name('manager.two-factor.show');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('profile.edit');
    Volt::route('settings/password', 'settings.password')->name('user-password.edit');
    Volt::route('settings/appearance', 'settings.appearance')->name('appearance.edit');

    Volt::route('settings/two-factor', 'settings.two-factor')
        ->middleware(
            when(
                Features::canManageTwoFactorAuthentication()
                && Features::optionEnabled(Features::twoFactorAuthentication(), 'confirmPassword'),
                ['password.confirm'],
                [],
            ),
        )
        ->name('two-factor.show');
});