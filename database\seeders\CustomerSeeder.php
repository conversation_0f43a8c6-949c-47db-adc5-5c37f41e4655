<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample customers
        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+255 712 345 678',
                'total_visits' => 24,
                'total_spent' => 2340000, // 2,340,000 Tsh
                'loyalty_points' => 450,
                'is_vip' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+255 713 456 789',
                'total_visits' => 18,
                'total_spent' => 1680000, // 1,680,000 Tsh
                'loyalty_points' => 280,
                'is_vip' => false,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+255 714 567 890',
                'total_visits' => 32,
                'total_spent' => 3120000, // 3,120,000 Tsh
                'loyalty_points' => 620,
                'is_vip' => true,
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+255 715 678 901',
                'total_visits' => 15,
                'total_spent' => 1250000, // 1,250,000 Tsh
                'loyalty_points' => 150,
                'is_vip' => false,
            ],
            [
                'name' => 'Sophie <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+255 716 789 012',
                'total_visits' => 28,
                'total_spent' => 2890000, // 2,890,000 Tsh
                'loyalty_points' => 520,
                'is_vip' => true,
            ],
            [
                'name' => 'Michael Brown',
                'email' => '<EMAIL>',
                'phone' => '+255 717 890 123',
                'total_visits' => 9,
                'total_spent' => 890000, // 890,000 Tsh
                'loyalty_points' => 90,
                'is_vip' => false,
            ],
            [
                'name' => 'Olivia Parker',
                'email' => '<EMAIL>',
                'phone' => '+255 718 901 234',
                'total_visits' => 21,
                'total_spent' => 1980000, // 1,980,000 Tsh
                'loyalty_points' => 380,
                'is_vip' => true,
            ],
            [
                'name' => 'James Miller',
                'email' => '<EMAIL>',
                'phone' => '+255 719 012 345',
                'total_visits' => 12,
                'total_spent' => 1120000, // 1,120,000 Tsh
                'loyalty_points' => 120,
                'is_vip' => false,
            ],
        ];

        foreach ($customers as $customerData) {
            User::create(array_merge($customerData, [
                'role' => 'customer',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]));
        }
    }
}