<?php
/**
 * Patient Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is medical admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    $search = sanitize($_GET['search'] ?? '');
    $filters = ['search' => $search];
    exportCustomersCSV($filters);
    exit;
}

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $customerId = $_POST['customer_id'] ?? '';

    if ($action === 'update_points' && $customerId) {
        $pointsAction = $_POST['points_action'] ?? '';
        $points = (int)($_POST['points'] ?? 0);

        $result = updateCustomerPoints($customerId, $pointsAction, $points);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient loyalty points updated successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    } elseif ($action === 'create') {
        $result = createCustomer($_POST);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient created successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    } elseif ($action === 'update' && $customerId) {
        $result = updateCustomer($customerId, $_POST);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient updated successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    } elseif ($action === 'delete' && $customerId) {
        $result = deleteCustomer($customerId);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient record deleted successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    }
}

// Get patients with pagination and filters
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$sortBy = sanitize($_GET['sort'] ?? 'created_at');
$sortOrder = sanitize($_GET['order'] ?? 'DESC');

$whereClause = "WHERE role = 'CUSTOMER'";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$allowedSorts = ['name', 'email', 'created_at', 'points', 'total_bookings', 'total_spent'];
if (!in_array($sortBy, $allowedSorts)) {
    $sortBy = 'created_at';
}

$allowedOrders = ['ASC', 'DESC'];
if (!in_array($sortOrder, $allowedOrders)) {
    $sortOrder = 'DESC';
}

$patients = $database->fetchAll(
    "SELECT u.id, u.name, u.email, u.email_verified_at, u.image, u.password, u.phone,
            u.date_of_birth, u.role, u.points, u.referral_code, u.referred_by,
            u.created_at, u.updated_at,
            COUNT(DISTINCT b.id) as total_appointments,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent,
            MAX(b.date) as last_appointment_date
     FROM users u
     LEFT JOIN bookings b ON u.id = b.user_id
     $whereClause
     GROUP BY u.id, u.name, u.email, u.email_verified_at, u.image, u.password, u.phone,
              u.date_of_birth, u.role, u.points, u.referral_code, u.referred_by,
              u.created_at, u.updated_at
     ORDER BY $sortBy $sortOrder
     LIMIT $limit OFFSET $offset",
    $params
);

$totalPatients = $database->fetch(
    "SELECT COUNT(*) as count FROM users $whereClause",
    $params
)['count'];

$totalPages = ceil($totalPatients / $limit);

// Get patient statistics
$stats = getCustomerStats();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Patient Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Patient Management CSS -->
<style>
/* Medical Patient Management Specific Styles */
.medical-patient-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-patient-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-patient-card:hover::before {
    left: 100%;
}

.medical-patient-card:hover {
    transform: translateY(-4px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.medical-stats-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-filter-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-header-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.patient-avatar {
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
}

.medical-btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.medical-btn-primary:hover::before {
    left: 100%;
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-btn-secondary {
    background: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.medical-table {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.medical-table-header {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    border-bottom: 2px solid rgba(73, 167, 92, 0.1);
}

.medical-table-row {
    border-bottom: 1px solid rgba(73, 167, 92, 0.05);
    transition: all 0.3s ease;
}

.medical-table-row:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.02), rgba(88, 148, 210, 0.02));
}

.medical-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.medical-action-view {
    background: linear-gradient(135deg, var(--primary-blue), #3b82f6);
    color: white;
}

.medical-action-view:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(88, 148, 210, 0.3);
}

.medical-action-points {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.medical-action-points:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.medical-action-message {
    background: linear-gradient(135deg, var(--primary-green), #10b981);
    color: white;
}

.medical-action-message:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(73, 167, 92, 0.3);
}

.medical-action-edit {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.medical-action-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
}

.medical-action-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-action-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-pagination {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 1));
    backdrop-filter: blur(25px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    transform: scale(0.95);
    opacity: 0;
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
                opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
                box-shadow 0.3s ease;
    will-change: transform, opacity;
}

.medical-modal.show {
    transform: scale(1);
    opacity: 1;
}

/* Ensure modal content is fully opaque when shown - without breaking transitions */
.medical-modal.show * {
    opacity: 1;
}

/* Smooth transitions for modal content */
.medical-modal * {
    transition: opacity 0.2s ease;
}

.medical-modal input,
.medical-modal textarea,
.medical-modal select {
    background-color: rgba(255, 255, 255, 1);
    color: rgba(26, 35, 50, 1); /* redolence-navy */
    transition: all 0.2s ease;
}

.medical-modal label,
.medical-modal h1,
.medical-modal h2,
.medical-modal h3,
.medical-modal p,
.medical-modal div {
    color: rgba(26, 35, 50, 1); /* redolence-navy */
}

.medical-modal .medical-btn-primary,
.medical-modal .medical-btn-secondary {
    opacity: 1;
    transition: all 0.2s ease;
}

/* Modal backdrop animations */
.modal-backdrop {
    background-color: transparent;
    transition: background-color 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: background-color;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.modal-backdrop.show {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Ensure smooth modal container transitions */
.modal-backdrop.hidden {
    visibility: hidden;
    pointer-events: none;
}

.modal-backdrop:not(.hidden) {
    visibility: visible;
    pointer-events: auto;
}

/* Prevent layout shifts during animations */
.medical-modal {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
}

/* Smooth scrolling for modal content */
.medical-modal {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced form styling for modals */
.modal-form-input {
    background: rgba(255, 255, 255, 1);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modal-form-input:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.modal-form-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Modal content sections */
.modal-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 1));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
}

.modal-section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
}

.modal-section-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    background: var(--gradient-primary);
    color: white;
}

/* Loading states */
.modal-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(73, 167, 92, 0.1);
    border-top: 3px solid var(--primary-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal responsive design */
@media (max-width: 768px) {
    .medical-modal {
        margin: 1rem;
        border-radius: 20px;
        max-height: calc(100vh - 2rem);
    }

    .modal-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .modal-form-input {
        padding: 0.75rem;
        border-radius: 10px;
    }
}

.loyalty-points {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05));
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.total-spent {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

@media (max-width: 768px) {
    .medical-patient-card {
        border-radius: 16px;
        padding: 1rem;
    }
    
    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }
    
    .medical-table {
        border-radius: 16px;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                
                    <!-- Medical Header -->
                    <div class="medical-header-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Patient
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Manage patient records, loyalty points, and treatment history</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $totalPatients ?> Total Patients
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                        Medical Records
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <button onclick="exportPatients()" class="medical-btn-secondary inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export CSV
                                </button>
                                <button onclick="createPatientModal()" class="medical-btn-primary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Add Patient
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-4 rounded-xl border-2 <?= $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <?php if ($messageType === 'success'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php else: ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php endif; ?>
                                </svg>
                                <?= htmlspecialchars($message) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-blue/20 to-blue-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Patients</dt>
                                        <dd class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">New This Month</dt>
                                        <dd class="text-2xl font-bold text-green-600"><?= number_format($stats['new_this_month']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Lifetime Value</dt>
                                        <dd class="text-2xl font-bold text-redolence-green"><?= formatCurrency($stats['avg_lifetime_value']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-400/20 to-violet-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Loyalty Points</dt>
                                        <dd class="text-2xl font-bold text-purple-600"><?= number_format($stats['total_points']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Search and Filters -->
                    <div class="medical-filter-card p-6 mb-8">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                           placeholder="Search by patient name, email, or phone..." 
                                           class="w-full pl-10 pr-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                            </div>
                            <div>
                                <select name="sort" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="created_at" <?= $sortBy === 'created_at' ? 'selected' : '' ?>>Date Joined</option>
                                    <option value="name" <?= $sortBy === 'name' ? 'selected' : '' ?>>Name</option>
                                    <option value="total_spent" <?= $sortBy === 'total_spent' ? 'selected' : '' ?>>Total Spent</option>
                                    <option value="total_bookings" <?= $sortBy === 'total_bookings' ? 'selected' : '' ?>>Total Appointments</option>
                                    <option value="points" <?= $sortBy === 'points' ? 'selected' : '' ?>>Loyalty Points</option>
                                </select>
                            </div>
                            <div>
                                <select name="order" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="DESC" <?= $sortOrder === 'DESC' ? 'selected' : '' ?>>Descending</option>
                                    <option value="ASC" <?= $sortOrder === 'ASC' ? 'selected' : '' ?>>Ascending</option>
                                </select>
                            </div>
                            <button type="submit" class="medical-btn-primary">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter
                            </button>
                            <?php if ($search || $sortBy !== 'created_at' || $sortOrder !== 'DESC'): ?>
                                <a href="<?= getBasePath() ?>/admin/customers" class="medical-btn-secondary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Reset
                                </a>
                            <?php endif; ?>
                        </form>
                    </div>

                    <!-- Medical Patients Table -->
                    <div class="medical-table">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="medical-table-header">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Patient</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Contact</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Appointments</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Total Spent</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Loyalty Points</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Last Visit</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <?php foreach ($patients as $patient): ?>
                                        <tr class="medical-table-row">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="patient-avatar h-10 w-10">
                                                            <?= strtoupper(substr($patient['name'], 0, 2)) ?>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-semibold text-redolence-navy"><?= htmlspecialchars($patient['name']) ?></div>
                                                        <div class="text-sm text-gray-500">
                                                            Joined <?= date('M j, Y', strtotime($patient['created_at'])) ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-redolence-navy font-medium"><?= htmlspecialchars($patient['email']) ?></div>
                                                <?php if ($patient['phone']): ?>
                                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($patient['phone']) ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-redolence-navy"><?= number_format($patient['total_appointments']) ?></div>
                                                <div class="text-sm text-gray-500">appointments</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-bold total-spent"><?= formatCurrency($patient['total_spent']) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="loyalty-points px-3 py-1 rounded-full text-xs font-semibold">
                                                    <?= number_format($patient['points']) ?> points
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($patient['last_appointment_date']): ?>
                                                    <div class="text-sm text-redolence-navy font-medium"><?= date('M j, Y', strtotime($patient['last_appointment_date'])) ?></div>
                                                <?php else: ?>
                                                    <div class="text-sm text-gray-400">No appointments</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="flex gap-1 flex-wrap">
                                                    <!-- CRUD Actions -->
                                                    <button onclick="viewPatientModal('<?= $patient['id'] ?>')"
                                                            class="medical-action-btn medical-action-view text-xs"
                                                            title="View Patient Details">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                        View
                                                    </button>
                                                    <button onclick="editPatientModal('<?= $patient['id'] ?>')"
                                                            class="medical-action-btn medical-action-edit text-xs"
                                                            title="Edit Patient Information">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                        Edit
                                                    </button>

                                                    <!-- Additional Actions -->
                                                    <button onclick="editPoints('<?= $patient['id'] ?>', '<?= htmlspecialchars($patient['name']) ?>', <?= $patient['points'] ?>)"
                                                            class="medical-action-btn medical-action-points text-xs"
                                                            title="Manage Loyalty Points">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                                        </svg>
                                                        Points
                                                    </button>
                                                    <button onclick="sendMessage('<?= $patient['id'] ?>')"
                                                            class="medical-action-btn medical-action-message text-xs"
                                                            title="Send Message">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                        </svg>
                                                        Message
                                                    </button>
                                                    <button onclick="viewProgressiveReport('<?= $patient['id'] ?>', '<?= urlencode($patient['email']) ?>')"
                                                            class="medical-action-btn medical-action-view text-xs"
                                                            title="View Progress Report">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                                        </svg>
                                                        Report
                                                    </button>

                                                    <!-- Delete Action (conditional) -->
                                                    <?php if ((int)$patient['total_appointments'] == 0 || !$patient['last_appointment_date'] || strtotime($patient['last_appointment_date']) < strtotime('-6 months')): ?>
                                                        <button onclick="deletePatientModal('<?= $patient['id'] ?>', '<?= htmlspecialchars($patient['name']) ?>', <?= $patient['total_appointments'] ?>, '<?= $patient['last_appointment_date'] ?>')"
                                                                class="medical-action-btn medical-action-delete text-xs"
                                                                title="Delete Patient Record">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                            </svg>
                                                            Delete
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Medical Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="medical-pagination px-6 py-4 flex items-center justify-between border-t border-gray-100">
                                <div class="flex-1 flex justify-between sm:hidden">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                           class="medical-btn-secondary">
                                            Previous
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                           class="medical-btn-secondary">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p class="text-sm text-gray-600">
                                            Showing <span class="font-semibold text-redolence-green"><?= $offset + 1 ?></span> to 
                                            <span class="font-semibold text-redolence-green"><?= min($offset + $limit, $totalPatients) ?></span> of 
                                            <span class="font-semibold text-redolence-green"><?= $totalPatients ?></span> patients
                                        </p>
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
                                            <?php for ($i = 1; $i <= min($totalPages, 10); $i++): ?>
                                                <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                                   class="relative inline-flex items-center px-4 py-2 border text-sm font-semibold transition-all <?= $i === $page ? 'z-10 bg-redolence-green border-redolence-green text-white' : 'bg-white border-gray-300 text-gray-700 hover:bg-redolence-green/10 hover:border-redolence-green hover:text-redolence-green' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Loyalty Points Edit Modal -->
<div id="pointsModal" class="hidden fixed inset-0 flex items-center justify-center z-[9999] modal-backdrop">
    <div class="medical-modal p-8 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-redolence-navy">Edit Patient Loyalty Points</h2>
            <button onclick="closePointsModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="pointsForm" method="POST">
            <input type="hidden" name="customer_id" id="pointsCustomerId">
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Patient</label>
                <div id="pointsCustomerName" class="text-redolence-navy font-semibold text-lg"></div>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Current Loyalty Points</label>
                <div id="currentPoints" class="text-purple-600 font-bold text-2xl"></div>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Action</label>
                <select name="action" id="pointsAction" required 
                        class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <option value="add">Add Points</option>
                    <option value="subtract">Subtract Points</option>
                    <option value="set">Set Points</option>
                </select>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Points Amount</label>
                <input type="number" name="points" id="pointsAmount" min="0" required 
                       class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
            </div>
            
            <div class="mb-8">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Reason (Optional)</label>
                <textarea name="reason" id="pointsReason" rows="3" 
                          class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                          placeholder="Reason for loyalty points adjustment..."></textarea>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Update Points
                </button>
                <button type="button" onclick="closePointsModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Customer View Modal -->
<div id="viewModal" class="hidden fixed inset-0 flex items-center justify-center z-[9999] modal-backdrop">
    <div class="medical-modal p-8 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-redolence-navy">Patient Details</h2>
            <button onclick="closeViewModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div id="viewModalContent" class="space-y-6">
            <!-- Content will be loaded dynamically -->
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-redolence-green"></div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Create Modal -->
<div id="createModal" class="hidden fixed inset-0 flex items-center justify-center z-[9999] modal-backdrop">
    <div class="medical-modal p-8 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-redolence-navy">Add New Patient</h2>
            <button onclick="closeCreateModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="createForm" method="POST">
            <input type="hidden" name="action" value="create">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Full Name *</label>
                    <input type="text" name="name" id="createName" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Enter patient's full name">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Email Address *</label>
                    <input type="email" name="email" id="createEmail" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Enter email address">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Phone Number</label>
                    <input type="tel" name="phone" id="createPhone"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Enter phone number">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Date of Birth</label>
                    <input type="date" name="date_of_birth" id="createDateOfBirth"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Password *</label>
                    <input type="password" name="password" id="createPassword" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Enter password (min 6 characters)">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Confirm Password *</label>
                    <input type="password" name="confirm_password" id="createConfirmPassword" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Confirm password">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Initial Loyalty Points</label>
                    <input type="number" name="points" id="createPoints" min="0" value="0"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                           placeholder="Enter initial loyalty points">
                </div>
            </div>

            <div class="mt-8 flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Create Patient
                </button>
                <button type="button" onclick="closeCreateModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Customer Edit Modal -->
<div id="editModal" class="hidden fixed inset-0 flex items-center justify-center z-[9999] modal-backdrop">
    <div class="medical-modal p-8 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-redolence-navy">Edit Patient Information</h2>
            <button onclick="closeEditModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="editForm" method="POST">
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="customer_id" id="editCustomerId">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Full Name *</label>
                    <input type="text" name="name" id="editName" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Email Address *</label>
                    <input type="email" name="email" id="editEmail" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Phone Number</label>
                    <input type="tel" name="phone" id="editPhone"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Date of Birth</label>
                    <input type="date" name="date_of_birth" id="editDateOfBirth"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Loyalty Points</label>
                    <input type="number" name="points" id="editPoints" min="0"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>
            </div>

            <div class="mt-8 flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Update Patient
                </button>
                <button type="button" onclick="closeEditModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Customer Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 flex items-center justify-center z-[9999] modal-backdrop">
    <div class="medical-modal p-8 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-red-600">Delete Patient Record</h2>
            <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Are you sure?</h3>
            <p class="text-gray-600 mb-4">You are about to delete the patient record for:</p>
            <p class="font-semibold text-redolence-navy text-lg" id="deletePatientName"></p>
            <div class="mt-4 text-sm text-gray-500" id="deletePatientInfo"></div>
        </div>

        <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="text-sm text-red-700">
                    <p class="font-semibold">Warning: This action cannot be undone!</p>
                    <p class="mt-1">All patient data, including booking history and loyalty points, will be permanently deleted.</p>
                </div>
            </div>
        </div>

        <form id="deleteForm" method="POST">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="customer_id" id="deleteCustomerId">

            <div class="flex gap-4">
                <button type="button" onclick="closeDeleteModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-xl transition-all">
                    Delete Patient
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// CRUD Modal Functions

// View Patient Modal
function viewPatientModal(customerId) {
    const modal = document.getElementById('viewModal');
    const content = document.getElementById('viewModalContent');

    // Show modal with loading state
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        modal.querySelector('.medical-modal').classList.add('show');
    }, 10);

    // Load patient data
    fetch(`<?= getBasePath() ?>/api/admin/customers/get.php?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPatientDetails(data.customer);
            } else {
                content.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-600 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Patient</h3>
                        <p class="text-gray-600">${data.error || 'Failed to load patient information'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            content.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-600 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Connection Error</h3>
                    <p class="text-gray-600">Failed to connect to server</p>
                </div>
            `;
        });
}

function displayPatientDetails(customer) {
    const content = document.getElementById('viewModalContent');
    const joinDate = new Date(customer.created_at).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    });
    const lastVisit = customer.last_booking_date ?
        new Date(customer.last_booking_date).toLocaleDateString('en-US', {
            year: 'numeric', month: 'long', day: 'numeric'
        }) : 'No appointments yet';

    content.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div class="modal-section">
                <div class="modal-section-header">
                    <div class="modal-section-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-redolence-navy">Personal Information</h3>
                </div>
                <div class="space-y-3">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Full Name</label>
                        <p class="text-redolence-navy font-semibold">${customer.name}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Email Address</label>
                        <p class="text-redolence-navy">${customer.email}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Phone Number</label>
                        <p class="text-redolence-navy">${customer.phone || 'Not provided'}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Date of Birth</label>
                        <p class="text-redolence-navy">${customer.date_of_birth || 'Not provided'}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Member Since</label>
                        <p class="text-redolence-navy">${joinDate}</p>
                    </div>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="modal-section">
                <div class="modal-section-header">
                    <div class="modal-section-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-redolence-navy">Account Statistics</h3>
                </div>
                <div class="space-y-3">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Total Appointments</label>
                        <p class="text-redolence-navy font-semibold">${customer.total_bookings || 0}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Total Spent</label>
                        <p class="text-redolence-green font-bold text-lg">$${parseFloat(customer.total_spent || 0).toFixed(2)}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Loyalty Points</label>
                        <p class="text-purple-600 font-semibold">${customer.points || 0} points</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Last Visit</label>
                        <p class="text-redolence-navy">${lastVisit}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3 mt-6 pt-6 border-t border-gray-200">
            <button onclick="editPatientModal('${customer.id}')" class="medical-btn-primary flex-1">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Patient
            </button>
            <button onclick="editPoints('${customer.id}', '${customer.name}', ${customer.points})" class="medical-btn-secondary flex-1">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                </svg>
                Manage Points
            </button>
        </div>
    `;
}

// Create Patient Modal
function createPatientModal() {
    const modal = document.getElementById('createModal');
    const form = document.getElementById('createForm');

    // Reset form
    form.reset();
    clearFormErrors();

    // Show modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        modal.querySelector('.medical-modal').classList.add('show');
    }, 10);
}

// Edit Patient Modal
function editPatientModal(customerId) {
    const modal = document.getElementById('editModal');

    // Show modal with loading state
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        modal.querySelector('.medical-modal').classList.add('show');
    }, 10);

    // Load patient data
    fetch(`<?= getBasePath() ?>/api/admin/customers/get.php?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateEditForm(data.customer);
            } else {
                showNotification('Error loading patient data: ' + (data.error || 'Unknown error'), 'error');
                closeEditModal();
            }
        })
        .catch(error => {
            showNotification('Failed to load patient data', 'error');
            closeEditModal();
        });
}

function populateEditForm(customer) {
    document.getElementById('editCustomerId').value = customer.id;
    document.getElementById('editName').value = customer.name || '';
    document.getElementById('editEmail').value = customer.email || '';
    document.getElementById('editPhone').value = customer.phone || '';
    document.getElementById('editDateOfBirth').value = customer.date_of_birth || '';
    document.getElementById('editPoints').value = customer.points || 0;
    clearFormErrors();
}

// Delete Patient Modal
function deletePatientModal(customerId, customerName, totalAppointments, lastAppointmentDate) {
    const modal = document.getElementById('deleteModal');

    document.getElementById('deleteCustomerId').value = customerId;
    document.getElementById('deletePatientName').textContent = customerName;

    const infoDiv = document.getElementById('deletePatientInfo');
    infoDiv.innerHTML = `
        <div class="text-center">
            <p><strong>Total Appointments:</strong> ${totalAppointments}</p>
            <p><strong>Last Visit:</strong> ${lastAppointmentDate ? new Date(lastAppointmentDate).toLocaleDateString() : 'Never'}</p>
        </div>
    `;

    // Show modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        modal.querySelector('.medical-modal').classList.add('show');
    }, 10);
}

// Modal Close Functions
function closeViewModal() {
    const modal = document.getElementById('viewModal');
    modal.classList.remove('show');
    modal.querySelector('.medical-modal').classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function closeCreateModal() {
    const modal = document.getElementById('createModal');
    modal.classList.remove('show');
    modal.querySelector('.medical-modal').classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function closeEditModal() {
    const modal = document.getElementById('editModal');
    modal.classList.remove('show');
    modal.querySelector('.medical-modal').classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('show');
    modal.querySelector('.medical-modal').classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function editPoints(customerId, customerName, currentPoints) {
    const modal = document.getElementById('pointsModal');
    const modalContent = modal.querySelector('.medical-modal');

    // Populate form data
    document.getElementById('pointsCustomerId').value = customerId;
    document.getElementById('pointsCustomerName').textContent = customerName;
    document.getElementById('currentPoints').textContent = currentPoints.toLocaleString() + ' points';
    document.getElementById('pointsAmount').value = '';
    document.getElementById('pointsReason').value = '';

    // Show modal with animation (same as other modals)
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        modalContent.classList.add('show');
    }, 10);
}

function closePointsModal() {
    const modal = document.getElementById('pointsModal');
    modal.classList.remove('show');
    modal.querySelector('.medical-modal').classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function sendMessage(customerId) {
    window.location.href = `<?= getBasePath() ?>/admin/customers/message.php?id=${customerId}`;
}

function viewProgressiveReport(customerId, customerEmail) {
    window.location.href = `<?= getBasePath() ?>/admin/progressive-reports?search=${encodeURIComponent(customerEmail)}`;
}

function exportPatients() {
    window.location.href = `<?= getBasePath() ?>/admin/customers/?export=csv&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>`;
}

// Handle points form submission
document.getElementById('pointsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('action', 'update_points');
    formData.append('points_action', document.getElementById('pointsAction').value);
    formData.append('points', document.getElementById('pointsAmount').value);

    // Submit to current page
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePointsModal();
    }
});

// Form Validation and Submission
function validateForm(formId) {
    const form = document.getElementById(formId);
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            field.classList.add('error');
            isValid = false;
        }
    });

    // Password confirmation for create form
    if (formId === 'createForm') {
        const password = document.getElementById('createPassword');
        const confirmPassword = document.getElementById('createConfirmPassword');

        if (password.value !== confirmPassword.value) {
            confirmPassword.classList.add('error');
            isValid = false;
            showNotification('Passwords do not match', 'error');
        }

        if (password.value && password.value.length < 6) {
            password.classList.add('error');
            isValid = false;
            showNotification('Password must be at least 6 characters', 'error');
        }
    }

    return isValid;
}

function clearFormErrors() {
    document.querySelectorAll('.error').forEach(field => {
        field.classList.remove('error');
    });
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl border-2 max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;

    if (type === 'success') {
        notification.classList.add('bg-green-50', 'border-green-200', 'text-green-800');
    } else if (type === 'error') {
        notification.classList.add('bg-red-50', 'border-red-200', 'text-red-800');
    } else {
        notification.classList.add('bg-blue-50', 'border-blue-200', 'text-blue-800');
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                ${type === 'success' ?
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>' :
                    type === 'error' ?
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>' :
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
                }
            </svg>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Form Submission Handlers
document.getElementById('createForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!validateForm('createForm')) {
        return;
    }

    const formData = new FormData(this);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Check if the response contains success or error messages
        if (data.includes('Patient created successfully') || data.includes('success')) {
            showNotification('Patient created successfully!', 'success');
            closeCreateModal();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            // Extract error message from response if possible
            const errorMatch = data.match(/error['"]\s*:\s*['"]([^'"]+)['"]/i);
            const errorMessage = errorMatch ? errorMatch[1] : 'Failed to create patient';
            showNotification(errorMessage, 'error');
        }
    })
    .catch(error => {
        showNotification('Network error occurred', 'error');
    });
});

document.getElementById('editForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!validateForm('editForm')) {
        return;
    }

    const formData = new FormData(this);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('Patient updated successfully') || data.includes('success')) {
            showNotification('Patient updated successfully!', 'success');
            closeEditModal();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            const errorMatch = data.match(/error['"]\s*:\s*['"]([^'"]+)['"]/i);
            const errorMessage = errorMatch ? errorMatch[1] : 'Failed to update patient';
            showNotification(errorMessage, 'error');
        }
    })
    .catch(error => {
        showNotification('Network error occurred', 'error');
    });
});

document.getElementById('deleteForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('Patient record deleted successfully') || data.includes('success')) {
            showNotification('Patient deleted successfully!', 'success');
            closeDeleteModal();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            const errorMatch = data.match(/error['"]\s*:\s*['"]([^'"]+)['"]/i);
            const errorMessage = errorMatch ? errorMatch[1] : 'Failed to delete patient';
            showNotification(errorMessage, 'error');
        }
    })
    .catch(error => {
        showNotification('Network error occurred', 'error');
    });
});

// Close modals on backdrop click
['pointsModal', 'viewModal', 'createModal', 'editModal', 'deleteModal'].forEach(modalId => {
    document.getElementById(modalId).addEventListener('click', function(e) {
        if (e.target === this) {
            const closeFunction = modalId.replace('Modal', '') === 'points' ? closePointsModal :
                                modalId.replace('Modal', '') === 'view' ? closeViewModal :
                                modalId.replace('Modal', '') === 'create' ? closeCreateModal :
                                modalId.replace('Modal', '') === 'edit' ? closeEditModal :
                                closeDeleteModal;
            closeFunction();
        }
    });
});

// Close modals on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePointsModal();
        closeViewModal();
        closeCreateModal();
        closeEditModal();
        closeDeleteModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>