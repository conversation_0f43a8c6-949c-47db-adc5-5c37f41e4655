<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Fortify\TwoFactorAuthenticatable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'role',
        'password',
        'google_id',
        'google_token',
        'google_refresh_token',
        'phone',
        'total_visits',
        'total_spent',
        'loyalty_points',
        'is_vip',
        'is_active',
        // Staff fields
        'position',
        'department',
        'bio',
        'avatar',
        'hire_date',
        'hourly_rate',
        'commission_rate',
        'employment_type',
        'skills',
        'certifications',
        'experience_years',
        'working_hours',
        'available_for_booking',
        'rating',
        'total_appointments',
        'total_revenue',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'remember_token',
        'google_token',
        'google_refresh_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'total_spent' => 'decimal:2',
            'is_vip' => 'boolean',
            'is_active' => 'boolean',
            'hire_date' => 'date',
            'hourly_rate' => 'decimal:2',
            'commission_rate' => 'decimal:2',
            'skills' => 'array',
            'certifications' => 'array',
            'working_hours' => 'array',
            'available_for_booking' => 'boolean',
            'rating' => 'decimal:2',
            'total_revenue' => 'decimal:2',
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * Get appointments where user is the client.
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'client_id');
    }

    /**
     * Get appointments where user is the staff member.
     */
    public function staffAppointments()
    {
        return $this->hasMany(Appointment::class, 'staff_id');
    }
}