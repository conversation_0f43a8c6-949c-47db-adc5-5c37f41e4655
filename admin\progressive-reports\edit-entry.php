<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get entry ID
$entryId = $_GET['id'] ?? '';
if (!$entryId) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get entry details
$entry = $progressiveReportEntry->getById($entryId);
if (!$entry) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Get report details
$report = $progressiveReport->getById($entry['report_id']);
if (!$report) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Get appointments for this client
$appointments = $database->fetchAll("
    SELECT b.id, b.date, b.start_time, s.name as service_name, staff.name as staff_name
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN users staff ON b.staff_id = staff.id
    WHERE b.user_id = ? AND b.status = 'COMPLETED'
    ORDER BY b.date DESC
", [$report['client_id']]);

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $appointmentId = $_POST['appointment_id'] ?: null;
    $entryDate = $_POST['entry_date'] ?? '';
    $treatment = $_POST['treatment'] ?? '';
    $description = $_POST['description'] ?? '';
    $notes = $_POST['notes'] ?? '';
    
    if (empty($entryDate) || empty($treatment) || empty($description)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } else {
        $updateData = [
            'appointment_id' => $appointmentId,
            'entry_date' => $entryDate,
            'treatment' => $treatment,
            'description' => $description,
            'notes' => $notes
        ];
        
        $result = $progressiveReportEntry->update($entryId, $updateData);
        
        if ($result) {
            $message = 'Treatment entry updated successfully.';
            $messageType = 'success';
            // Refresh entry data
            $entry = $progressiveReportEntry->getById($entryId);
        } else {
            $message = 'Failed to update treatment entry. Please try again.';
            $messageType = 'error';
        }
    }
}

$pageTitle = "Edit Entry - " . $report['title'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Progressive Reports CSS -->
<style>
.medical-edit-entry-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.08);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-edit-entry-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.medical-form-section {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.medical-form-section:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-input-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
}

.medical-input-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-select-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    cursor: pointer;
}

.medical-select-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-textarea-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    resize: vertical;
    min-height: 120px;
}

.medical-textarea-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-label-enhanced {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

.medical-help-text-enhanced {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

.medical-btn-primary-enhanced {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary-enhanced {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 1rem 2rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.medical-btn-secondary-enhanced:hover {
    background: rgba(73, 167, 92, 0.1);
    border-color: #49A75C;
    transform: translateY(-1px);
}

.medical-btn-danger-enhanced {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.medical-btn-danger-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.medical-icon-enhanced {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.medical-alert-enhanced {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
}

.medical-alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}

.medical-alert-success {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
    border-color: #16a34a;
}

.entry-info-card {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(46, 139, 87, 0.05));
    border: 1px solid rgba(73, 167, 92, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.patient-avatar {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1rem;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Back Navigation -->
                    <div class="mb-6">
                        <a href="<?= getBasePath() ?>/admin/progressive-reports/view.php?id=<?= $report['id'] ?>" 
                           class="inline-flex items-center text-sm font-medium text-redolence-blue hover:text-blue-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Back to Report
                        </a>
                    </div>

                    <!-- Page Header -->
                    <div class="medical-edit-entry-container p-8 mb-8">
                        <div class="flex items-center mb-6">
                            <div class="medical-icon-enhanced">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">Edit Treatment Entry</h1>
                                <p class="mt-2 text-gray-600">Update treatment documentation and clinical notes</p>
                            </div>
                        </div>
                        
                        <!-- Entry Info Card -->
                        <div class="entry-info-card">
                            <div class="flex items-center">
                                <div class="patient-avatar">
                                    <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-redolence-navy"><?= htmlspecialchars($report['title']) ?></h3>
                                    <p class="text-redolence-green font-medium"><?= htmlspecialchars($report['client_name']) ?></p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>Entry Date: <?= date('M j, Y', strtotime($entry['entry_date'])) ?></span>
                                        <span>•</span>
                                        <span>Treatment: <?= htmlspecialchars($entry['treatment']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                        <div class="mb-6">
                            <div class="medical-alert-enhanced medical-alert-<?= $messageType ?>">
                                <div class="flex items-center">
                                    <?php if ($messageType === 'error'): ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php endif; ?>
                                    <?= htmlspecialchars($message) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Edit Form -->
                    <div class="medical-edit-entry-container p-8">
                        <form method="POST" class="space-y-8" id="editEntryForm">
                            <!-- Appointment Selection Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    Appointment Link
                                </h3>
                                <div>
                                    <label for="appointment_id" class="medical-label-enhanced">
                                        Related Appointment (Optional)
                                    </label>
                                    <select name="appointment_id" id="appointment_id" class="medical-select-enhanced">
                                        <option value="">Select an appointment to link this entry...</option>
                                        <?php foreach ($appointments as $appointment): ?>
                                            <option value="<?= $appointment['id'] ?>"
                                                    data-date="<?= $appointment['date'] ?>"
                                                    data-service="<?= htmlspecialchars($appointment['service_name'] ?? 'Service') ?>"
                                                    <?= ($entry['appointment_id'] === $appointment['id']) ? 'selected' : '' ?>>
                                                <?= date('M j, Y', strtotime($appointment['date'])) ?> -
                                                <?= htmlspecialchars($appointment['service_name'] ?? 'Service') ?>
                                                (<?= htmlspecialchars($appointment['staff_name'] ?? 'Staff') ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="medical-help-text-enhanced">Link this entry to a specific completed appointment for better tracking</p>
                                </div>
                            </div>

                            <!-- Treatment Details Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                    </svg>
                                    Treatment Information
                                </h3>

                                <!-- Entry Date -->
                                <div class="mb-6">
                                    <label for="entry_date" class="medical-label-enhanced">
                                        Treatment Date <span class="text-red-500">*</span>
                                    </label>
                                    <input type="date"
                                           name="entry_date"
                                           id="entry_date"
                                           required
                                           value="<?= htmlspecialchars($entry['entry_date']) ?>"
                                           max="<?= date('Y-m-d') ?>"
                                           class="medical-input-enhanced">
                                    <p class="medical-help-text-enhanced">Date when the treatment or observation was performed</p>
                                </div>

                                <!-- Treatment -->
                                <div class="mb-6">
                                    <label for="treatment" class="medical-label-enhanced">
                                        Treatment/Procedure <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           name="treatment"
                                           id="treatment"
                                           required
                                           value="<?= htmlspecialchars($entry['treatment']) ?>"
                                           class="medical-input-enhanced"
                                           placeholder="e.g., Botox injection, Dermal filler, Laser treatment, Consultation">
                                    <p class="medical-help-text-enhanced">Specify the exact treatment or procedure performed</p>
                                </div>
                            </div>

                            <!-- Treatment Documentation Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Treatment Documentation
                                </h3>

                                <!-- Description -->
                                <div class="mb-6">
                                    <label for="description" class="medical-label-enhanced">
                                        Treatment Description <span class="text-red-500">*</span>
                                    </label>
                                    <textarea name="description"
                                              id="description"
                                              required
                                              rows="6"
                                              class="medical-textarea-enhanced"
                                              placeholder="Provide comprehensive details about the treatment performed, patient response, areas treated, techniques used, immediate results observed..."><?= htmlspecialchars($entry['description']) ?></textarea>
                                    <p class="medical-help-text-enhanced">Detailed clinical documentation of the treatment and patient response</p>
                                </div>

                                <!-- Additional Notes -->
                                <div class="mb-6">
                                    <label for="notes" class="medical-label-enhanced">
                                        Clinical Notes & Recommendations
                                    </label>
                                    <textarea name="notes"
                                              id="notes"
                                              rows="4"
                                              class="medical-textarea-enhanced"
                                              placeholder="Document any follow-up recommendations, side effects, special observations, patient feedback..."><?= htmlspecialchars($entry['notes'] ?? '') ?></textarea>
                                    <p class="medical-help-text-enhanced">Optional clinical notes, recommendations, and follow-up instructions</p>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="medical-form-section">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Changes will be saved to the patient's treatment timeline
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <a href="<?= getBasePath() ?>/admin/progressive-reports/view.php?id=<?= $report['id'] ?>"
                                           class="medical-btn-secondary-enhanced">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                            Cancel
                                        </a>
                                        <button type="button" onclick="deleteEntry()" class="medical-btn-danger-enhanced">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                            Delete Entry
                                        </button>
                                        <button type="submit" class="medical-btn-primary-enhanced" id="updateEntryBtn">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                            </svg>
                                            <span id="btnText">Update Entry</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Enhanced Edit Entry Form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editEntryForm');
    const updateBtn = document.getElementById('updateEntryBtn');
    const btnText = document.getElementById('btnText');

    // Form submission handler
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading state
            updateBtn.disabled = true;
            btnText.textContent = 'Updating...';
            updateBtn.style.opacity = '0.7';

            // Add spinner
            const spinner = document.createElement('div');
            spinner.className = 'inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin';
            btnText.parentNode.insertBefore(spinner, btnText);

            // Validate required fields
            const requiredFields = ['entry_date', 'treatment', 'description'];
            let hasError = false;

            requiredFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (!field.value.trim()) {
                    hasError = true;
                    field.style.borderColor = '#dc2626';
                    field.focus();
                }
            });

            if (hasError) {
                e.preventDefault();

                // Reset button
                updateBtn.disabled = false;
                btnText.textContent = 'Update Entry';
                updateBtn.style.opacity = '1';
                spinner.remove();

                return false;
            }
        });
    }

    // Input animations
    const inputs = document.querySelectorAll('.medical-input-enhanced, .medical-select-enhanced, .medical-textarea-enhanced');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 8px 25px rgba(73, 167, 92, 0.15)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';

            // Reset error styling
            if (this.value.trim()) {
                this.style.borderColor = '';
            }
        });
    });

    // Auto-resize textareas
    const textareas = document.querySelectorAll('.medical-textarea-enhanced');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    });
});

function deleteEntry() {
    if (confirm('Are you sure you want to delete this treatment entry? This action cannot be undone.')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= getBasePath() ?>/admin/progressive-reports/delete-entry.php';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'entry_id';
        idInput.value = '<?= $entryId ?>';

        const reportIdInput = document.createElement('input');
        reportIdInput.type = 'hidden';
        reportIdInput.name = 'report_id';
        reportIdInput.value = '<?= $report['id'] ?>';

        form.appendChild(idInput);
        form.appendChild(reportIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
