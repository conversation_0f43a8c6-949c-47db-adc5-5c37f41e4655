<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable();
            $table->integer('total_visits')->default(0);
            $table->decimal('total_spent', 15, 2)->default(0);
            $table->integer('loyalty_points')->default(0);
            $table->boolean('is_vip')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['phone', 'total_visits', 'total_spent', 'loyalty_points', 'is_vip']);
        });
    }
};