<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Staff Information
            $table->string('position')->nullable()->after('role');
            $table->string('department')->nullable()->after('position');
            $table->text('bio')->nullable()->after('department');
            $table->string('avatar')->nullable()->after('bio');
            
            // Employment Details
            $table->date('hire_date')->nullable()->after('avatar');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('hire_date');
            $table->decimal('commission_rate', 5, 2)->default(0)->after('hourly_rate');
            $table->string('employment_type')->default('full-time')->after('commission_rate'); // full-time, part-time, contractor
            
            // Skills and Specializations
            $table->json('skills')->nullable()->after('employment_type');
            $table->json('certifications')->nullable()->after('skills');
            $table->integer('experience_years')->default(0)->after('certifications');
            
            // Working Schedule
            $table->json('working_hours')->nullable()->after('experience_years');
            $table->boolean('available_for_booking')->default(true)->after('working_hours');
            
            // Performance Metrics
            $table->decimal('rating', 3, 2)->default(0)->after('available_for_booking');
            $table->integer('total_appointments')->default(0)->after('rating');
            $table->decimal('total_revenue', 10, 2)->default(0)->after('total_appointments');
            
            // Contact and Emergency
            $table->string('emergency_contact_name')->nullable()->after('total_revenue');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->string('address')->nullable()->after('emergency_contact_phone');
            
            // Status and Notes
            $table->string('status')->default('active')->after('address'); // active, on-leave, terminated
            $table->text('notes')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'position',
                'department',
                'bio',
                'avatar',
                'hire_date',
                'hourly_rate',
                'commission_rate',
                'employment_type',
                'skills',
                'certifications',
                'experience_years',
                'working_hours',
                'available_for_booking',
                'rating',
                'total_appointments',
                'total_revenue',
                'emergency_contact_name',
                'emergency_contact_phone',
                'address',
                'status',
                'notes',
            ]);
        });
    }
};
