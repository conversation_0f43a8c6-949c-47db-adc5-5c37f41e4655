<?php if (isset($component)) { $__componentOriginal7a52060a93cef17096fd090ab42bb46b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a52060a93cef17096fd090ab42bb46b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.manager','data' => ['title' => __('Clients')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.manager'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Clients'))]); ?>
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if (isset($component)) { $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.page-header','data' => ['title' => __('Clients'),'subtitle' => __('Manage client profiles and track their salon journey')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Clients')),'subtitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Manage client profiles and track their salon journey'))]); ?>
             <?php $__env->slot('actions', null, []); ?> 
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'outline','icon' => 'arrow-down-tray','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outline','icon' => 'arrow-down-tray','size' => 'md']); ?>
                    Export
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                <!-- Add Client Button triggers modal -->
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'primary','icon' => 'plus','size' => 'md','onclick' => 'showModal()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','icon' => 'plus','size' => 'md','onclick' => 'showModal()']); ?>
                    Add Client
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $attributes = $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $component = $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>

        
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'users','label' => __('Total Clients'),'value' => $totalClients ?? 0,'trend' => '+'.($newThisMonth ?? 0).' this month','trendUp' => true,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'users','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Total Clients')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalClients ?? 0),'trend' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('+'.($newThisMonth ?? 0).' this month'),'trendUp' => true,'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'user-plus','label' => __('New This Month'),'value' => $newThisMonth ?? 0,'iconColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'user-plus','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('New This Month')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($newThisMonth ?? 0),'iconColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'currency-dollar','label' => __('Avg. Lifetime Value'),'value' => number_format($avgLifetimeValue ?? 0, 0, '.', ','),'iconColor' => 'beige']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'currency-dollar','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Avg. Lifetime Value')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($avgLifetimeValue ?? 0, 0, '.', ',')),'iconColor' => 'beige']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <!-- Following project specification: Removed VIP clients metric -->
        </div>

        
        <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        <input 
                            type="text" 
                            placeholder="Search by name, email, or phone..."
                            class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                        />
                    </div>
                </div>
                <div class="flex gap-3">
                    <select class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                        <option>Sort by: Recent</option>
                        <option>Name (A-Z)</option>
                        <option>Name (Z-A)</option>
                        <option>Visit Count</option>
                    </select>
                    <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'primary','icon' => 'funnel','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','icon' => 'funnel','size' => 'md']); ?>
                        Filter
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                </div>
            </div>
        </div>

        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__empty_1 = true; $__currentLoopData = $clients ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm hover:shadow-md hover:border-[#E98CA5]/30 transition-all">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-bold text-lg">
                            <?php echo e(strtoupper(substr($client->name, 0, 2))); ?>

                        </div>
                        <div>
                            <h3 class="font-body font-bold text-[#2C2C34]"><?php echo e($client->name); ?></h3>
                            <p class="font-body text-xs text-[#8B5D66]">
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($client->is_active): ?>
                                    <span class="text-green-600">● Active</span>
                                <?php else: ?>
                                    <span class="text-red-600">● Inactive</span>
                                <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="relative">
                        <button onclick="toggleMenu(<?php echo e($client->id); ?>)" class="p-1.5 text-[#8B5D66] hover:bg-[#F7E9E6] rounded-lg transition-colors">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'ellipsis-vertical','class' => 'w-5 h-5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'ellipsis-vertical','class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        </button>
                        <div id="menu-<?php echo e($client->id); ?>" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#EFEFEF] z-10">
                            <form method="POST" action="<?php echo e(route('manager.clients.toggle-active', $client->id)); ?>" class="block">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-[#4A4A52] hover:bg-[#F7E9E6] flex items-center gap-2">
                                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => ''.e($client->is_active ? 'x-circle' : 'check-circle').'','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => ''.e($client->is_active ? 'x-circle' : 'check-circle').'','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                    <?php echo e($client->is_active ? 'Deactivate' : 'Activate'); ?>

                                </button>
                            </form>
                            <button type="button" onclick="showDeleteConfirm(<?php echo e($client->id); ?>, '<?php echo e(addslashes($client->name)); ?>')" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2">
                                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'trash','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'trash','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                Delete
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-3 mb-4">
                    <div class="flex items-center gap-2 text-sm">
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'envelope','class' => 'w-4 h-4 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'envelope','class' => 'w-4 h-4 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        <span class="font-body text-[#4A4A52]"><?php echo e($client->email); ?></span>
                    </div>
                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($client->phone): ?>
                    <div class="flex items-center gap-2 text-sm">
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'phone','class' => 'w-4 h-4 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'phone','class' => 'w-4 h-4 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        <span class="font-body text-[#4A4A52]"><?php echo e($client->phone); ?></span>
                    </div>
                    <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    <div class="flex items-center gap-2 text-sm">
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'calendar','class' => 'w-4 h-4 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','class' => 'w-4 h-4 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        <span class="font-body text-[#4A4A52]">Last visit: <?php echo e($client->updated_at->diffForHumans()); ?></span>
                    </div>
                </div>

                <div class="flex items-center justify-center py-3 px-4 bg-[#F7E9E6] rounded-lg mb-4">
                    <div class="text-center">
                        <div class="font-body text-xs text-[#8B5D66] mb-0.5">Total Visits</div>
                        <div class="font-body text-lg font-bold text-[#2C2C34]"><?php echo e($client->total_visits); ?></div>
                    </div>
                </div>

                <div class="flex gap-2">
                    <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'outline','size' => 'sm','class' => 'flex-1','onclick' => 'showEditModal('.e($client->id).', \''.e(addslashes($client->name)).'\', \''.e($client->email).'\', \''.e($client->phone ?? '').'\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outline','size' => 'sm','class' => 'flex-1','onclick' => 'showEditModal('.e($client->id).', \''.e(addslashes($client->name)).'\', \''.e($client->email).'\', \''.e($client->phone ?? '').'\')']); ?>
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'eye','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'eye','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        View
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'primary','size' => 'sm','class' => 'flex-1']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'sm','class' => 'flex-1']); ?>
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'calendar','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        Book
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-3 text-center py-12">
                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'users','class' => 'w-16 h-16 mx-auto text-[#8B5D66] mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'users','class' => 'w-16 h-16 mx-auto text-[#8B5D66] mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                <h3 class="font-body text-xl font-bold text-[#2C2C34] mb-2">No clients found</h3>
                <p class="font-body text-[#8B5D66] mb-6">Get started by adding your first client</p>
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'primary','icon' => 'plus','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','icon' => 'plus','size' => 'md']); ?>
                    Add Client
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
            </div>
            <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
        </div>

        
        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if(isset($clients) && $clients->hasPages()): ?>
        <div class="mt-8 flex justify-center">
            <?php echo e($clients->links()); ?>

        </div>
        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $attributes = $__attributesOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $component = $__componentOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__componentOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?>


<div id="add-client-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <form method="POST" action="<?php echo e(route('manager.clients.store')); ?>">
                <?php echo csrf_field(); ?>
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-[#E98CA5]/10 sm:mx-0 sm:h-10 sm:w-10">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'user-plus','class' => 'h-6 w-6 text-[#E98CA5]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'user-plus','class' => 'h-6 w-6 text-[#E98CA5]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-[#2C2C34]" id="modal-title">
                                Add New Client
                            </h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-[#4A4A52]">Full Name</label>
                                    <div class="mt-1">
                                        <input type="text" name="name" id="name" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-[#4A4A52]">Email Address</label>
                                    <div class="mt-1">
                                        <input type="email" name="email" id="email" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-[#4A4A52]">Phone Number</label>
                                    <div class="mt-1">
                                        <input type="text" name="phone" id="phone"
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="password" class="block text-sm font-medium text-[#4A4A52]">Password</label>
                                    <div class="mt-1 relative">
                                        <input type="password" name="password" id="password" required
                                            class="py-2.5 px-4 pr-12 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                        <button type="button" onclick="togglePassword()" 
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-[#8B5D66] hover:text-[#E98CA5]">
                                            <svg id="eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <svg id="eye-slash-icon" class="h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-1 text-xs text-[#8B5D66]">Client will use this password to log in (min. 8 characters)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:ml-3 sm:w-auto sm:text-sm">
                        Add Client
                    </button>
                    <button type="button" onclick="hideModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<div id="edit-client-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideEditModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <form method="POST" id="edit-client-form">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-[#E98CA5]/10 sm:mx-0 sm:h-10 sm:w-10">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'user','class' => 'h-6 w-6 text-[#E98CA5]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'user','class' => 'h-6 w-6 text-[#E98CA5]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-[#2C2C34]" id="edit-modal-title">
                                Edit Client Details
                            </h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label for="edit-name" class="block text-sm font-medium text-[#4A4A52]">Full Name</label>
                                    <div class="mt-1">
                                        <input type="text" name="name" id="edit-name" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-email" class="block text-sm font-medium text-[#4A4A52]">Email Address</label>
                                    <div class="mt-1">
                                        <input type="email" name="email" id="edit-email" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-phone" class="block text-sm font-medium text-[#4A4A52]">Phone Number</label>
                                    <div class="mt-1">
                                        <input type="text" name="phone" id="edit-phone"
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-password" class="block text-sm font-medium text-[#4A4A52]">New Password</label>
                                    <div class="mt-1 relative">
                                        <input type="password" name="password" id="edit-password"
                                            class="py-2.5 px-4 pr-12 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                        <button type="button" onclick="toggleEditPassword()" 
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-[#8B5D66] hover:text-[#E98CA5]">
                                            <svg id="edit-eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <svg id="edit-eye-slash-icon" class="h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-1 text-xs text-[#8B5D66]">Leave blank to keep current password (min. 8 characters if changing)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:ml-3 sm:w-auto sm:text-sm">
                        Save Changes
                    </button>
                    <button type="button" onclick="hideEditModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<div id="delete-confirm-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideDeleteConfirm()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-[#2C2C34]">
                            Delete Client
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-[#8B5D66]">
                                Are you sure you want to delete <span id="delete-client-name" class="font-semibold text-[#2C2C34]"></span>? This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form method="POST" id="delete-client-form" class="inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete
                    </button>
                </form>
                <button type="button" onclick="hideDeleteConfirm()"
                    class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>


<div id="success-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideSuccessModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-[#2C2C34]">
                            Success
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-[#8B5D66]" id="success-message">
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="hideSuccessModal()"
                    class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:w-auto sm:text-sm">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show delete confirmation modal
    function showDeleteConfirm(clientId, clientName) {
        const modal = document.getElementById('delete-confirm-modal');
        const form = document.getElementById('delete-client-form');
        const nameSpan = document.getElementById('delete-client-name');
        
        // Set form action and client name
        form.action = '/manager/clients/' + clientId;
        nameSpan.textContent = clientName;
        
        // Show modal
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    // Hide delete confirmation modal
    function hideDeleteConfirm() {
        const modal = document.getElementById('delete-confirm-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Show success notification modal
    function showSuccessModal(message) {
        const modal = document.getElementById('success-modal');
        const messageEl = document.getElementById('success-message');
        
        messageEl.textContent = message;
        
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            hideSuccessModal();
        }, 3000);
    }

    // Hide success notification modal
    function hideSuccessModal() {
        const modal = document.getElementById('success-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Toggle dropdown menu
    function toggleMenu(clientId) {
        const menu = document.getElementById('menu-' + clientId);
        // Close all other menus
        document.querySelectorAll('[id^="menu-"]').forEach(m => {
            if (m.id !== 'menu-' + clientId) {
                m.classList.add('hidden');
            }
        });
        menu.classList.toggle('hidden');
    }

    // Close menus when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick^="toggleMenu"]') && !event.target.closest('[id^="menu-"]')) {
            document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Show edit modal
    function showEditModal(id, name, email, phone) {
        const modal = document.getElementById('edit-client-modal');
        const form = document.getElementById('edit-client-form');
        
        // Set form action
        form.action = '/manager/clients/' + id;
        
        // Populate form fields
        document.getElementById('edit-name').value = name;
        document.getElementById('edit-email').value = email;
        document.getElementById('edit-phone').value = phone;
        
        // Show modal
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    // Hide edit modal
    function hideEditModal() {
        const modal = document.getElementById('edit-client-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eye-icon');
        const eyeSlashIcon = document.getElementById('eye-slash-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.add('hidden');
            eyeSlashIcon.classList.remove('hidden');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('hidden');
            eyeSlashIcon.classList.add('hidden');
        }
    }

    // Toggle edit password visibility
    function toggleEditPassword() {
        const passwordInput = document.getElementById('edit-password');
        const eyeIcon = document.getElementById('edit-eye-icon');
        const eyeSlashIcon = document.getElementById('edit-eye-slash-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.add('hidden');
            eyeSlashIcon.classList.remove('hidden');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('hidden');
            eyeSlashIcon.classList.add('hidden');
        }
    }
    
    // Simple modal show/hide functions
    function showModal() {
        const modal = document.getElementById('add-client-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }
    
    function hideModal() {
        const modal = document.getElementById('add-client-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
            // Reset password visibility when modal closes
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            const eyeSlashIcon = document.getElementById('eye-slash-icon');
            if (passwordInput && passwordInput.type === 'text') {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('hidden');
                eyeSlashIcon.classList.add('hidden');
            }
        }, 300);
    }
    
    // Close modal when clicking outside
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('add-client-modal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideModal();
                }
            });
        }
        
        // Show success modal if there's a success message
        <?php if(session('success')): ?>
            showSuccessModal('<?php echo e(session('success')); ?>');
        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
    });
</script>
<?php /**PATH C:\laragon\www\Instyle\resources\views/manager/clients/index.blade.php ENDPATH**/ ?>