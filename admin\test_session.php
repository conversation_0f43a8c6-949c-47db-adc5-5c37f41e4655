<?php
/**
 * Admin Session Test Page
 * Use this to test admin authentication
 */

require_once __DIR__ . '/../config/app.php';

// Check if auth object exists
if (!isset($auth)) {
    die("Error: Authentication system not found. Please check config/app.php");
}

// Get debug information
$debugInfo = $auth->getSessionDebugInfo();
$isAuthenticated = $auth->isAuthenticated();
$currentUser = $auth->getCurrentUser();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Session Test - Redolence</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin Session Test</h1>
        
        <div class="status <?= $isAuthenticated ? 'success' : 'error' ?>">
            <strong>Authentication Status:</strong> <?= $isAuthenticated ? 'AUTHENTICATED' : 'NOT AUTHENTICATED' ?>
        </div>

        <?php if ($currentUser): ?>
            <div class="status success">
                <strong>Current User:</strong> <?= htmlspecialchars($currentUser['name']) ?> (<?= htmlspecialchars($currentUser['role']) ?>)
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>Current User:</strong> None
            </div>
        <?php endif; ?>

        <h2>Session Debug Information</h2>
        <pre><?= htmlspecialchars(json_encode($debugInfo, JSON_PRETTY_PRINT)) ?></pre>

        <?php if (isset($_SESSION['user_id']) && isset($_SESSION['session_token'])): ?>
            <h2>Database Session Check</h2>
            <?php
            try {
                $session = $database->fetch(
                    "SELECT * FROM sessions WHERE session_token = ?",
                    [$_SESSION['session_token']]
                );
                
                if ($session) {
                    $isExpired = strtotime($session['expires']) < time();
                    echo '<div class="status ' . ($isExpired ? 'error' : 'success') . '">';
                    echo '<strong>Database Session:</strong> ' . ($isExpired ? 'EXPIRED' : 'VALID');
                    echo '</div>';
                    echo '<pre>' . htmlspecialchars(json_encode($session, JSON_PRETTY_PRINT)) . '</pre>';
                } else {
                    echo '<div class="status error"><strong>Database Session:</strong> NOT FOUND</div>';
                }
            } catch (Exception $e) {
                echo '<div class="status error"><strong>Database Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        <?php endif; ?>

        <h2>Actions</h2>
        <a href="<?= getBasePath() ?>/admin" class="btn">Go to Admin Dashboard</a>
        <a href="<?= getBasePath() ?>/auth/login.php" class="btn">Go to Login</a>
        <a href="<?= getBasePath() ?>/debug_auth.php" class="btn">JSON Debug Info</a>
        <a href="javascript:location.reload()" class="btn">Refresh Page</a>

        <h2>Test Navigation</h2>
        <p>Try navigating to different admin pages to test if authentication persists:</p>
        <ul>
            <li><a href="<?= getBasePath() ?>/admin/bookings">Bookings</a></li>
            <li><a href="<?= getBasePath() ?>/admin/customers">Customers</a></li>
            <li><a href="<?= getBasePath() ?>/admin/services">Services</a></li>
            <li><a href="<?= getBasePath() ?>/admin/staff">Staff</a></li>
        </ul>
    </div>
</body>
</html>
