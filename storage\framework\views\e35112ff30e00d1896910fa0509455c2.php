<?php if (isset($component)) { $__componentOriginal7a52060a93cef17096fd090ab42bb46b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a52060a93cef17096fd090ab42bb46b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.manager','data' => ['title' => __('Manager Dashboard')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.manager'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Manager Dashboard'))]); ?>
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if (isset($component)) { $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.page-header','data' => ['title' => __('Dashboard'),'subtitle' => __('Welcome back! Here\'s what\'s happening with your salon today.')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Dashboard')),'subtitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Welcome back! Here\'s what\'s happening with your salon today.'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $attributes = $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $component = $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>

        
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'calendar','label' => __('Today\'s Appointments'),'value' => '12','trend' => '+8% vs yesterday','trendUp' => true,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Today\'s Appointments')),'value' => '12','trend' => '+8% vs yesterday','trendUp' => true,'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'users','label' => __('Total Clients'),'value' => '248','trend' => '+12 this month','trendUp' => true,'iconColor' => 'beige']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'users','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Total Clients')),'value' => '248','trend' => '+12 this month','trendUp' => true,'iconColor' => 'beige']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'currency-dollar','label' => __('Revenue Today'),'value' => '$1,245','trend' => '+15% vs yesterday','trendUp' => true,'iconColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'currency-dollar','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Revenue Today')),'value' => '$1,245','trend' => '+15% vs yesterday','trendUp' => true,'iconColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'user-group','label' => __('Team Members'),'value' => '8','iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'user-group','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Team Members')),'value' => '8','iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
        </div>

        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="font-body text-xl font-bold text-[#2C2C34]">Today's Schedule</h2>
                        <p class="font-body text-sm text-[#8B5D66] mt-1"><?php echo e(now()->format('l, F j, Y')); ?></p>
                    </div>
                    <a href="#" class="font-body text-sm font-medium text-[#E98CA5] hover:text-[#C85E78] transition-colors">
                        View All
                    </a>
                </div>

                <div class="space-y-3">
                    
                    <div class="relative pl-4 pr-4 py-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="absolute left-0 top-0 bottom-0 w-1 bg-emerald-500 rounded-l-lg"></div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3">
                                    <span class="font-body font-semibold text-[#2C2C34]">9:00 AM</span>
                                    <span class="px-2 py-0.5 text-xs font-medium text-emerald-700 bg-emerald-50 border border-emerald-200 rounded-full">Confirmed</span>
                                </div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mt-1">Hair Styling</h3>
                                <p class="font-body text-sm text-[#4A4A52]">Sarah Johnson</p>
                                <p class="font-body text-xs text-[#8B5D66] mt-1">with Emma Wilson • 90 min</p>
                            </div>
                        </div>
                    </div>

                    
                    <div class="relative pl-4 pr-4 py-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="absolute left-0 top-0 bottom-0 w-1 bg-[#FA2964] rounded-l-lg"></div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3">
                                    <span class="font-body font-semibold text-[#2C2C34]">11:00 AM</span>
                                    <span class="px-2 py-0.5 text-xs font-medium text-[#FA2964] bg-pink-50 border border-pink-200 rounded-full">Pending</span>
                                </div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mt-1">Hair Coloring</h3>
                                <p class="font-body text-sm text-[#4A4A52]">Maria Rodriguez</p>
                                <p class="font-body text-xs text-[#8B5D66] mt-1">with Sophia Chen • 120 min</p>
                            </div>
                        </div>
                    </div>

                    
                    <div class="relative pl-4 pr-4 py-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="absolute left-0 top-0 bottom-0 w-1 bg-emerald-500 rounded-l-lg"></div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3">
                                    <span class="font-body font-semibold text-[#2C2C34]">2:00 PM</span>
                                    <span class="px-2 py-0.5 text-xs font-medium text-emerald-700 bg-emerald-50 border border-emerald-200 rounded-full">Confirmed</span>
                                </div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mt-1">Hydration Treatment</h3>
                                <p class="font-body text-sm text-[#4A4A52]">Emily Chen</p>
                                <p class="font-body text-xs text-[#8B5D66] mt-1">with Emma Wilson • 60 min</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="font-body text-xl font-bold text-[#2C2C34]">Recent Clients</h2>
                    <a href="#" class="font-body text-sm font-medium text-[#E98CA5] hover:text-[#C85E78] transition-colors">
                        View All
                    </a>
                </div>

                <div class="space-y-3">
                    
                    <div class="flex items-center justify-between p-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold text-sm">
                                SJ
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34]">Sarah Johnson</h3>
                                <p class="font-body text-xs text-[#8B5D66]">Last visit: 2 days ago</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs font-medium text-[#E98CA5] bg-[#F7E9E6] border border-[#E98CA5]/20 rounded-full">24 visits</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-[#DCC7A1]/10 flex items-center justify-center text-[#DCC7A1] font-semibold text-sm">
                                MR
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34]">Maria Rodriguez</h3>
                                <p class="font-body text-xs text-[#8B5D66]">Last visit: 1 week ago</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs font-medium text-[#E98CA5] bg-[#F7E9E6] border border-[#E98CA5]/20 rounded-full">18 visits</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-[#C85E78]/10 flex items-center justify-center text-[#C85E78] font-semibold text-sm">
                                EC
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34]">Emily Chen</h3>
                                <p class="font-body text-xs text-[#8B5D66]">Last visit: 3 days ago</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs font-medium text-[#E98CA5] bg-[#F7E9E6] border border-[#E98CA5]/20 rounded-full">32 visits</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 rounded-lg border border-[#EFEFEF] hover:border-[#E98CA5]/20 hover:shadow-sm transition-all">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-[#8B5D66]/10 flex items-center justify-center text-[#8B5D66] font-semibold text-sm">
                                JD
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34]">Jessica Davis</h3>
                                <p class="font-body text-xs text-[#8B5D66]">Last visit: 5 days ago</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs font-medium text-[#E98CA5] bg-[#F7E9E6] border border-[#E98CA5]/20 rounded-full">12 visits</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="mt-6 bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
            <h2 class="font-body text-xl font-bold text-[#2C2C34] mb-2">Weekly Revenue</h2>
            <p class="font-body text-sm text-[#8B5D66] mb-6">Last 7 days performance</p>
            
            
            <div class="h-64 flex items-center justify-center border-2 border-dashed border-[#EFEFEF] rounded-lg">
                <div class="text-center">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'chart-bar','class' => 'w-12 h-12 text-[#DCC7A1] mx-auto mb-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'chart-bar','class' => 'w-12 h-12 text-[#DCC7A1] mx-auto mb-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                    <p class="font-body text-sm text-[#8B5D66]">Chart visualization will be added here</p>
                    <p class="font-body text-xs text-[#8B5D66] mt-1">(Integration with Chart.js or similar library)</p>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $attributes = $__attributesOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $component = $__componentOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__componentOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\Instyle\resources\views\manager\dashboard.blade.php ENDPATH**/ ?>