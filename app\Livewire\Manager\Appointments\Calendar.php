<?php

namespace App\Livewire\Manager\Appointments;

use App\Models\Appointment;

use App\Models\Service;
use App\Models\User;
use Livewire\Component;
use Carbon\Carbon;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.manager', ['title' => 'Appointments Calendar'])]
class Calendar extends Component
{
    // Calendar view state
    public $view = 'month'; // month, week, day
    public $currentDate;
    public $selectedDate;

    // Filters
    public $statusFilter = 'all';
    public $staffFilter = 'all';


    // Modal state
    public $showQuickView = false;
    public $selectedAppointment = null;

    // Form fields for new/edit appointment
    public $appointment_id;
    public $client_id;

    public $staff_id;
    public $appointment_date;
    public $start_time;
    public $notes;
    public $appointment_status = 'pending';

    // Drag and drop
    public $draggedAppointmentId;

    protected function rules()
    {
        return [
            'client_id' => 'required|exists:users,id',

            'staff_id' => 'nullable|exists:users,id',
            'appointment_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'notes' => 'nullable|string',
        ];
    }

    public function mount()
    {
        $this->currentDate = Carbon::now()->format('Y-m-d');
        $this->selectedDate = $this->currentDate;
    }

    // Navigation methods
    public function previousPeriod()
    {
        $date = Carbon::parse($this->currentDate);

        switch ($this->view) {
            case 'month':
                $this->currentDate = $date->subMonth()->format('Y-m-d');
                break;
            case 'week':
                $this->currentDate = $date->subWeek()->format('Y-m-d');
                break;
            case 'day':
                $this->currentDate = $date->subDay()->format('Y-m-d');
                break;
        }
    }

    public function nextPeriod()
    {
        $date = Carbon::parse($this->currentDate);

        switch ($this->view) {
            case 'month':
                $this->currentDate = $date->addMonth()->format('Y-m-d');
                break;
            case 'week':
                $this->currentDate = $date->addWeek()->format('Y-m-d');
                break;
            case 'day':
                $this->currentDate = $date->addDay()->format('Y-m-d');
                break;
        }
    }

    public function goToToday()
    {
        $this->currentDate = Carbon::now()->format('Y-m-d');
        $this->selectedDate = $this->currentDate;
    }

    public function changeView($view)
    {
        $this->view = $view;
    }

    public function selectDate($date)
    {
        $this->selectedDate = $date;
        $this->appointment_date = $date;
    }

    // Get appointments based on current view
    public function getAppointmentsProperty()
    {
        $query = Appointment::with(['client', 'staff']);

        $date = Carbon::parse($this->currentDate);

        switch ($this->view) {
            case 'month':
                $startOfMonth = $date->copy()->startOfMonth();
                $endOfMonth = $date->copy()->endOfMonth();
                $query->whereBetween('appointment_date', [$startOfMonth, $endOfMonth]);
                break;
            case 'week':
                $startOfWeek = $date->copy()->startOfWeek();
                $endOfWeek = $date->copy()->endOfWeek();
                $query->whereBetween('appointment_date', [$startOfWeek, $endOfWeek]);
                break;
            case 'day':
                $query->whereDate('appointment_date', $date);
                break;
        }

        // Apply filters
        if ($this->statusFilter !== 'all') {
            $query->where('status', $this->statusFilter);
        }

        if ($this->staffFilter !== 'all') {
            $query->where('staff_id', $this->staffFilter);
        }



        return $query->orderBy('appointment_date')->orderBy('start_time')->get();
    }

    // Get calendar data for month view
    public function getCalendarDaysProperty()
    {
        $date = Carbon::parse($this->currentDate);
        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();

        // Start from the beginning of the week (Sunday)
        $startDate = $startOfMonth->copy()->startOfWeek(Carbon::SUNDAY);
        $endDate = $endOfMonth->copy()->endOfWeek();

        $days = [];
        $currentDay = $startDate->copy();

        while ($currentDay <= $endDate) {
            $days[] = [
                'date' => $currentDay->format('Y-m-d'),
                'day' => $currentDay->day,
                'isCurrentMonth' => $currentDay->month === $date->month,
                'isToday' => $currentDay->isToday(),
                'isPast' => $currentDay->isPast() && !$currentDay->isToday(),
            ];
            $currentDay->addDay();
        }

        return $days;
    }

    // Get week days for week view
    public function getWeekDaysProperty()
    {
        $date = Carbon::parse($this->currentDate);
        $startOfWeek = $date->copy()->startOfWeek(Carbon::SUNDAY);

        $days = [];
        for ($i = 0; $i < 7; $i++) {
            $day = $startOfWeek->copy()->addDays($i);
            $days[] = [
                'date' => $day->format('Y-m-d'),
                'dayName' => $day->format('D'),
                'day' => $day->day,
                'isToday' => $day->isToday(),
            ];
        }

        return $days;
    }

    // Get appointments for a specific date
    public function getAppointmentsForDate($date)
    {
        return $this->appointments->filter(function ($appointment) use ($date) {
            return $appointment->appointment_date->format('Y-m-d') === $date;
        });
    }

    // Get statistics
    public function getStatsProperty()
    {
        $appointments = $this->appointments;

        return [
            'total' => $appointments->count(),
            'confirmed' => $appointments->where('status', 'confirmed')->count(),
            'pending' => $appointments->where('status', 'pending')->count(),
            'cancelled' => $appointments->where('status', 'cancelled')->count(),
            'completed' => $appointments->where('status', 'completed')->count(),
        ];
    }

    // CRUD operations
    public function createAppointment()
    {
        $this->validate();


        $startTime = Carbon::parse($this->start_time);
        $endTime = $startTime->copy()->addMinutes(60);

        Appointment::create([
            'client_id' => $this->client_id,

            'staff_id' => $this->staff_id ?: null,
            'appointment_date' => $this->appointment_date,
            'start_time' => $this->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration' => 60,
            'status' => 'pending',
            'total_amount' => 0.00,
            'notes' => $this->notes ?: null,
        ]);

        $this->resetForm();
        session()->flash('success', 'Appointment created successfully!');
        $this->dispatch('appointment-created');
    }

    public function updateAppointment()
    {
        $this->validate();

        $appointment = Appointment::findOrFail($this->appointment_id);

        $startTime = Carbon::parse($this->start_time);
        $endTime = $startTime->copy()->addMinutes(60);

        $appointment->update([
            'client_id' => $this->client_id,

            'staff_id' => $this->staff_id ?: null,
            'appointment_date' => $this->appointment_date,
            'start_time' => $this->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration' => 60,
            'status' => $this->appointment_status,
            'total_amount' => 0.00,
            'notes' => $this->notes ?: null,
        ]);

        $this->resetForm();
        session()->flash('success', 'Appointment updated successfully!');
        $this->dispatch('appointment-updated');
    }

    public function updateStatus($id, $status)
    {
        $appointment = Appointment::findOrFail($id);
        $appointment->update(['status' => $status]);
        session()->flash('success', 'Appointment status updated!');
    }

    public function deleteAppointment($id)
    {
        $appointment = Appointment::findOrFail($id);
        $appointment->delete();
        session()->flash('success', 'Appointment cancelled successfully!');
    }

    // Drag and drop functionality
    public function moveAppointment($appointmentId, $newDate, $newTime = null)
    {
        $appointment = Appointment::findOrFail($appointmentId);

        $updateData = ['appointment_date' => $newDate];

        if ($newTime) {
            $startTime = Carbon::parse($newTime);
            $endTime = $startTime->copy()->addMinutes($appointment->duration);
            $updateData['start_time'] = $newTime;
            $updateData['end_time'] = $endTime->format('H:i');
        }

        $appointment->update($updateData);
        session()->flash('success', 'Appointment moved successfully!');
    }

    private function resetForm()
    {
        $this->appointment_id = null;
        $this->client_id = null;
        $this->service_id = null;
        $this->staff_id = null;
        $this->start_time = null;
        $this->notes = null;
        $this->appointment_status = 'pending';
        $this->resetErrorBag();
    }

    public function getClientsProperty()
    {
        return User::where('role', 'customer')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }



    public function getServicesProperty()
    {
        return Service::where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    public function getStaffProperty()
    {
        return User::whereIn('role', ['manager', 'staff'])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    public function render()
    {
        return view('livewire.manager.appointments.calendar', [
            'appointments' => $this->appointments,
            'calendarDays' => $this->calendarDays,
            'weekDays' => $this->weekDays,
            'stats' => $this->stats,
            'clients' => $this->clients,
            'services' => $this->services,
            'staff' => $this->staff,
        ]);
    }
}
