<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Get report entries
$entries = $progressiveReportEntry->getByReportId($reportId);

$pageTitle = "Progressive Report - " . $report['client_name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Back Navigation -->
                    <div class="mb-6">
                        <a href="<?= getBasePath() ?>/admin/progressive-reports" 
                           class="inline-flex items-center text-sm font-medium text-redolence-blue hover:text-blue-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Back to Progressive Reports
                        </a>
                    </div>

                    <!-- Report Header -->
                    <div class="medical-glass border border-redolence-green/20 rounded-2xl p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="medical-customer-avatar">
                                        <span class="text-2xl font-bold">
                                            <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-6">
                                    <h1 class="text-3xl font-bold text-redolence-navy">
                                        <?= htmlspecialchars($report['title']) ?>
                                    </h1>
                                    <p class="mt-2 text-xl text-redolence-green font-semibold">
                                        <?= htmlspecialchars($report['client_name']) ?>
                                    </p>
                                    <div class="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                            <?= htmlspecialchars($report['client_email']) ?>
                                        </div>
                                        <?php if ($report['client_phone']): ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                                </svg>
                                                <?= htmlspecialchars($report['client_phone']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($report['description']): ?>
                                        <p class="mt-4 text-gray-600"><?= htmlspecialchars($report['description']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex flex-col sm:flex-row gap-3">
                                <span class="medical-status-badge medical-status-<?= strtolower($report['status']) ?>">
                                    <?= ucfirst(strtolower($report['status'])) ?>
                                </span>
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/edit.php?id=<?= $report['id'] ?>"
                                   class="medical-btn-secondary text-center">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                    Edit Report
                                </a>
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/export-pdf.php?id=<?= $report['id'] ?>"
                                   class="medical-btn-secondary text-center" target="_blank">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Export PDF
                                </a>
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/add-entry.php?report_id=<?= $report['id'] ?>"
                                   class="medical-btn-primary text-center">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    Add Entry
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Report Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-6 w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-redolence-navy"><?= number_format($report['total_entries']) ?></div>
                                    <div class="text-sm text-gray-500">Total Entries</div>
                                </div>
                            </div>
                        </div>
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-6 w-6 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-redolence-navy">
                                        <?= $report['last_entry_date'] ? date('M j', strtotime($report['last_entry_date'])) : 'N/A' ?>
                                    </div>
                                    <div class="text-sm text-gray-500">Last Entry</div>
                                </div>
                            </div>
                        </div>
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-redolence-navy"><?= htmlspecialchars($report['created_by_name']) ?></div>
                                    <div class="text-sm text-gray-500">Created By</div>
                                </div>
                            </div>
                        </div>
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-redolence-navy"><?= date('M j', strtotime($report['created_at'])) ?></div>
                                    <div class="text-sm text-gray-500">Created</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Report Entries Timeline -->
                    <div class="medical-glass border border-gray-200 rounded-xl p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-redolence-navy">Treatment Timeline</h2>
                            <a href="<?= getBasePath() ?>/admin/progressive-reports/add-entry.php?report_id=<?= $report['id'] ?>"
                               class="medical-btn-primary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                Add Entry
                            </a>
                        </div>

                        <?php if (empty($entries)): ?>
                            <div class="text-center py-12">
                                <div class="medical-icon mx-auto mb-4">
                                    <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-redolence-navy mb-2">No Entries Yet</h3>
                                <p class="text-gray-500 mb-6">Start documenting the patient's treatment progress.</p>
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/add-entry.php?report_id=<?= $report['id'] ?>" 
                                   class="medical-btn-primary">
                                    Add First Entry
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-6">
                                <?php foreach ($entries as $entry): ?>
                                    <div class="medical-timeline-entry">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0">
                                                <div class="medical-timeline-marker">
                                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="ml-6 flex-1">
                                                <div class="medical-timeline-content">
                                                    <div class="flex items-center justify-between mb-3">
                                                        <div>
                                                            <h3 class="text-lg font-semibold text-redolence-navy">
                                                                <?= htmlspecialchars($entry['treatment']) ?>
                                                            </h3>
                                                            <div class="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                                                <span><?= date('F j, Y', strtotime($entry['entry_date'])) ?></span>
                                                                <span>•</span>
                                                                <span>By <?= htmlspecialchars($entry['created_by_name']) ?></span>
                                                                <?php if ($entry['appointment_id']): ?>
                                                                    <span>•</span>
                                                                    <span>Appointment: <?= date('M j', strtotime($entry['appointment_date'])) ?></span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <a href="<?= getBasePath() ?>/admin/progressive-reports/edit-entry.php?id=<?= $entry['id'] ?>"
                                                               class="medical-action-btn medical-action-edit text-xs">
                                                                Edit
                                                            </a>
                                                            <button onclick="deleteEntry('<?= $entry['id'] ?>')"
                                                                    class="medical-action-btn medical-action-delete text-xs">
                                                                Delete
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="prose prose-sm max-w-none">
                                                        <p class="text-gray-700"><?= nl2br(htmlspecialchars($entry['description'])) ?></p>
                                                        <?php if ($entry['notes']): ?>
                                                            <div class="mt-3 p-3 bg-blue-50 rounded-lg border-l-4 border-redolence-blue">
                                                                <p class="text-sm text-gray-600 font-medium mb-1">Additional Notes:</p>
                                                                <p class="text-sm text-gray-700"><?= nl2br(htmlspecialchars($entry['notes'])) ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if ($entry['images'] && is_array($entry['images']) && !empty($entry['images'])): ?>
                                                        <div class="mt-4">
                                                            <p class="text-sm font-medium text-gray-700 mb-2">Images:</p>
                                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                                <?php foreach ($entry['images'] as $image): ?>
                                                                    <img src="<?= htmlspecialchars($image) ?>" 
                                                                         alt="Treatment image" 
                                                                         class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-opacity"
                                                                         onclick="openImageModal('<?= htmlspecialchars($image) ?>')">
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center">
    <div class="max-w-4xl max-h-full p-4">
        <img id="modalImage" src="" alt="Full size image" class="max-w-full max-h-full object-contain">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">
            ×
        </button>
    </div>
</div>

<script>
function deleteEntry(entryId) {
    if (confirm('Are you sure you want to delete this entry? This action cannot be undone.')) {
        fetch(`<?= getBasePath() ?>/api/admin/progressive-report-entries.php`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: entryId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting entry: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting entry');
        });
    }
}

function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Close modal when clicking outside the image
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
