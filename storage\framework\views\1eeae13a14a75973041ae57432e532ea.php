<?php if (isset($component)) { $__componentOriginal7a52060a93cef17096fd090ab42bb46b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a52060a93cef17096fd090ab42bb46b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.manager','data' => ['title' => __('Appointments')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.manager'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Appointments'))]); ?>
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if (isset($component)) { $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.page-header','data' => ['title' => __('Appointments'),'subtitle' => __('Manage and track all salon appointments')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Appointments')),'subtitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Manage and track all salon appointments'))]); ?>
             <?php $__env->slot('actions', null, []); ?> 
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'outline','icon' => 'calendar','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outline','icon' => 'calendar','size' => 'md']); ?>
                    Calendar View
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['variant' => 'primary','icon' => 'plus','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','icon' => 'plus','size' => 'md']); ?>
                    New Appointment
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $attributes = $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $component = $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>

        
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'calendar','label' => __('Total Appointments'),'value' => $totalAppointments,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Total Appointments')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalAppointments),'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'clock','label' => __('Pending'),'value' => $pendingAppointments,'iconColor' => 'beige']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'clock','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Pending')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($pendingAppointments),'iconColor' => 'beige']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'check-circle','label' => __('Confirmed'),'value' => $confirmedAppointments,'iconColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'check-circle','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Confirmed')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($confirmedAppointments),'iconColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'calendar-days','label' => __('Today'),'value' => $todayAppointments,'trend' => ($todayTrend > 0 ? '+' : '') . $todayTrend . ' from yesterday','trendUp' => $todayTrendUp,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar-days','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Today')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($todayAppointments),'trend' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(($todayTrend > 0 ? '+' : '') . $todayTrend . ' from yesterday'),'trendUp' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($todayTrendUp),'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
        </div>

        
        <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <form method="GET" action="<?php echo e(route('manager.appointments.index')); ?>">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                            <input 
                                type="text" 
                                name="search"
                                value="<?php echo e(request('search')); ?>"
                                placeholder="Search by client name, service, or staff..."
                                class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                            />
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <select name="status" class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                            <option value="all" <?php echo e(request('status') == 'all' || !request('status') ? 'selected' : ''); ?>>All Status</option>
                            <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="confirmed" <?php echo e(request('status') == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                            <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                            <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                        </select>
                        <input 
                            type="date"
                            name="date"
                            value="<?php echo e(request('date')); ?>"
                            class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                        />
                        <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['type' => 'submit','variant' => 'primary','icon' => 'funnel','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary','icon' => 'funnel','size' => 'md']); ?>
                            Filter
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        
        <div class="bg-white rounded-xl border border-[#EFEFEF] shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-[#EFEFEF]">
                    <thead class="bg-[#F7E9E6]/30">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Client</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Staff</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Date & Time</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-[#EFEFEF]">
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__empty_1 = true; $__currentLoopData = $appointments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-[#F7E9E6]/20 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold text-sm">
                                        <?php echo e($appointment->client->initials()); ?>

                                    </div>
                                    <div>
                                        <div class="font-body font-semibold text-[#2C2C34] text-sm"><?php echo e($appointment->client->name); ?></div>
                                        <div class="font-body text-xs text-[#8B5D66]"><?php echo e($appointment->client->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34] font-medium"><?php echo e($appointment->service->name); ?></div>
                                <div class="font-body text-xs text-[#8B5D66]"><?php echo e($appointment->service->category ?? 'Service'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34]"><?php echo e($appointment->staff?->name ?? 'Unassigned'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34] font-medium">
                                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($appointment->appointment_date->isToday()): ?>
                                        Today, <?php echo e($appointment->start_time->format('g:i A')); ?>

                                    <?php elseif($appointment->appointment_date->isTomorrow()): ?>
                                        Tomorrow, <?php echo e($appointment->start_time->format('g:i A')); ?>

                                    <?php else: ?>
                                        <?php echo e($appointment->appointment_date->format('M j, Y')); ?>, <?php echo e($appointment->start_time->format('g:i A')); ?>

                                    <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                </div>
                                <div class="font-body text-xs text-[#8B5D66]"><?php echo e($appointment->appointment_date->format('M j, Y')); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34]"><?php echo e($appointment->duration); ?> min</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if (isset($component)) { $__componentOriginal4af424b90c762a172adad803e3194c97 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4af424b90c762a172adad803e3194c97 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.status-badge','data' => ['status' => $appointment->status]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($appointment->status)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $attributes = $__attributesOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__attributesOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $component = $__componentOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__componentOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center gap-2">
                                    <button class="p-1.5 text-[#E98CA5] hover:bg-[#F7E9E6] rounded-lg transition-colors" title="View">
                                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'eye','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'eye','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                    </button>
                                    <button class="p-1.5 text-[#4A4A52] hover:bg-[#F7E9E6] rounded-lg transition-colors" title="Edit">
                                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'pencil','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'pencil','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                    </button>
                                    <form method="POST" action="<?php echo e(route('manager.appointments.destroy', $appointment->id)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?');">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="p-1.5 text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Cancel">
                                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'calendar','class' => 'w-12 h-12 text-[#8B5D66]/30 mb-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','class' => 'w-12 h-12 text-[#8B5D66]/30 mb-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                    <p class="text-[#2C2C34] font-semibold mb-1">No appointments found</p>
                                    <p class="text-[#8B5D66] text-sm">Try adjusting your filters or create a new appointment</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </tbody>
                </table>
            </div>

            
            <div class="px-6 py-4 border-t border-[#EFEFEF] flex items-center justify-between">
                <div class="text-sm text-[#8B5D66]">
                    Showing <span class="font-semibold text-[#2C2C34]"><?php echo e($appointments->firstItem() ?? 0); ?></span> to <span class="font-semibold text-[#2C2C34]"><?php echo e($appointments->lastItem() ?? 0); ?></span> of <span class="font-semibold text-[#2C2C34]"><?php echo e($appointments->total()); ?></span> appointments
                </div>
                <div class="flex gap-2">
                    <?php echo e($appointments->links('pagination::simple-tailwind')); ?>

                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $attributes = $__attributesOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__attributesOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a52060a93cef17096fd090ab42bb46b)): ?>
<?php $component = $__componentOriginal7a52060a93cef17096fd090ab42bb46b; ?>
<?php unset($__componentOriginal7a52060a93cef17096fd090ab42bb46b); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Instyle\resources\views\manager\appointments\index.blade.php ENDPATH**/ ?>