<?php
/**
 * Medical Treatments Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';

// Require medical admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createService($_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Medical treatment created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'update':
            $result = updateService($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Medical treatment updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'delete':
            $result = deleteService($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Medical treatment deleted successfully!';
            } else {
                // Store detailed error information for JavaScript to handle
                $_SESSION['delete_error'] = json_encode($result);
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/services');
}

// Get all medical treatments with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$category = sanitize($_GET['category'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($category) {
    $whereClause .= " AND category = ?";
    $params[] = $category;
}

$treatments = $database->fetchAll(
    "SELECT * FROM services $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset",
    $params
);

$totalTreatments = $database->fetch(
    "SELECT COUNT(*) as count FROM services $whereClause",
    $params
)['count'];

$totalPages = ceil($totalTreatments / $limit);

// Get medical categories for filter
$categories = getActiveServiceCategories();

// Clean up any package-related session messages
if (isset($_SESSION['success']) && (strpos($_SESSION['success'], 'Package') !== false || strpos($_SESSION['success'], 'package') !== false)) {
    unset($_SESSION['success']);
}
if (isset($_SESSION['error']) && (strpos($_SESSION['error'], 'Package') !== false || strpos($_SESSION['error'], 'package') !== false)) {
    unset($_SESSION['error']);
}
if (isset($_SESSION['delete_error'])) {
    $deleteError = json_decode($_SESSION['delete_error'], true);
    if ($deleteError && isset($deleteError['error']) && (strpos($deleteError['error'], 'Package') !== false || strpos($deleteError['error'], 'package') !== false)) {
        unset($_SESSION['delete_error']);
    }
}

// Handle messages for display
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Medical Treatments Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Treatments Management CSS -->
<style>
/* Medical Treatments Specific Styles */
.medical-treatment-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-treatment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-treatment-card:hover::before {
    left: 100%;
}

.medical-treatment-card:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.medical-filter-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-header-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.treatment-image {
    border-radius: 16px;
    transition: all 0.3s ease;
}

.treatment-image:hover {
    transform: scale(1.05);
}

.medical-status-active {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.medical-status-inactive {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medical-category-badge {
    background: linear-gradient(135deg, rgba(88, 148, 210, 0.1), rgba(88, 148, 210, 0.05));
    color: var(--primary-blue);
    border: 1px solid rgba(88, 148, 210, 0.3);
}

.medical-price {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.medical-btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.medical-btn-primary:hover::before {
    left: 100%;
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-btn-secondary {
    background: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.medical-btn-edit {
    background: linear-gradient(135deg, var(--primary-blue), #3b82f6);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(88, 148, 210, 0.3);
}

.medical-btn-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-pagination {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(25px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 24px;
}

@media (max-width: 768px) {
    .medical-treatment-card {
        border-radius: 16px;
        padding: 1rem;
    }
    
    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                
                    <!-- Medical Header -->
                    <div class="medical-header-card p-6 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Medical Treatments
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Manage aesthetic procedures, pricing, and treatment categories</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $totalTreatments ?> Total Treatments
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                        </svg>
                                        Medical Procedures
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <a href="<?= getBasePath() ?>/admin/categories/" class="medical-btn-secondary inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    Manage Categories
                                </a>
                                <button onclick="openCreateModal()" class="medical-btn-primary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Add Treatment
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-xl border-2 <?= $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <?php if ($messageType === 'success'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php else: ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php endif; ?>
                                </svg>
                                <?= htmlspecialchars($message) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-6 mb-8">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                           placeholder="Search medical treatments..." 
                                           class="w-full pl-10 pr-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                            </div>
                            <div>
                                <select name="category" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?= htmlspecialchars($cat['name']) ?>" <?= $category === $cat['name'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($cat['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <button type="submit" class="medical-btn-primary">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter
                            </button>
                            <?php if ($search || $category): ?>
                                <a href="<?= getBasePath() ?>/admin/services" class="medical-btn-secondary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Clear
                                </a>
                            <?php endif; ?>
                        </form>
                    </div>

                    <!-- Medical Treatments Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                        <?php foreach ($treatments as $treatment): ?>
                            <div class="medical-treatment-card p-6">
                                <div class="aspect-w-16 aspect-h-9 mb-4">
                                    <?php if ($treatment['image']): ?>
                                        <?php
                                        // Check if image is a URL or uploaded file
                                        $imageSrc = filter_var($treatment['image'], FILTER_VALIDATE_URL)
                                            ? $treatment['image']
                                            : getBasePath() . '/uploads/' . $treatment['image'];
                                        ?>
                                        <img src="<?= htmlspecialchars($imageSrc) ?>" alt="<?= htmlspecialchars($treatment['name']) ?>"
                                             class="treatment-image w-full h-48 object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-redolence-green/10 to-redolence-blue/10 rounded-2xl flex items-center justify-center">
                                            <svg class="w-16 h-16 text-redolence-green/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="flex items-start justify-between mb-3">
                                    <h3 class="text-lg font-bold text-redolence-navy"><?= htmlspecialchars($treatment['name']) ?></h3>
                                    <span class="<?= $treatment['is_active'] ? 'medical-status-active' : 'medical-status-inactive' ?> px-3 py-1 rounded-full text-xs font-semibold">
                                        <?= $treatment['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>
                                
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?= htmlspecialchars($treatment['description']) ?></p>
                                
                                <div class="flex items-center justify-between mb-4">
                                    <span class="medical-price text-2xl font-bold">
                                        <?= $treatment['price'] ? formatCurrency($treatment['price']) : '<span class="text-redolence-blue">TSH</span>' ?>
                                    </span>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500">Duration</div>
                                        <div class="text-redolence-blue font-semibold">
                                            <?= $treatment['duration'] ? $treatment['duration'] . ' min' : 'Variable' ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Treatment Badges -->
                                <div class="mb-4 flex flex-wrap gap-2">
                                    <?php if ($treatment['featured'] ?? false): ?>
                                        <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            ⭐ Featured
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($treatment['popular'] ?? false): ?>
                                        <span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            🔥 Popular
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($treatment['new_treatment'] ?? false): ?>
                                        <span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            ✨ New
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Session Frequency & Technology -->
                                <?php if ($treatment['session_frequency'] || $treatment['technology_used']): ?>
                                    <div class="mb-4 space-y-1">
                                        <?php if ($treatment['session_frequency']): ?>
                                            <div class="text-xs text-gray-600">
                                                <span class="font-semibold">Frequency:</span> <?= htmlspecialchars($treatment['session_frequency']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($treatment['technology_used']): ?>
                                            <div class="text-xs text-gray-600">
                                                <span class="font-semibold">Technology:</span> <?= htmlspecialchars($treatment['technology_used']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="space-y-2">
                                    <button onclick="manageVariations('<?= $treatment['id'] ?>', '<?= htmlspecialchars($treatment['name']) ?>')"
                                            class="w-full medical-btn-primary text-sm">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                        Treatment Variations
                                    </button>
                                    
                                    <div class="flex gap-2">
                                        <button onclick="editService('<?= $treatment['id'] ?>')"
                                                class="flex-1 medical-btn-edit text-sm">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit
                                        </button>
                                        <button onclick="deleteService('<?= $treatment['id'] ?>', '<?= htmlspecialchars($treatment['name']) ?>')"
                                                class="flex-1 medical-btn-delete text-sm">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-pagination px-6 py-4 flex items-center justify-between">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&filter=<?= urlencode($filter) ?>"
                                       class="medical-btn-secondary">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&filter=<?= urlencode($filter) ?>"
                                       class="medical-btn-secondary">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">
                                        Showing <span class="font-semibold text-redolence-green"><?= $offset + 1 ?></span> to
                                        <span class="font-semibold text-redolence-green"><?= min($offset + $limit, $totalTreatments) ?></span> of
                                        <span class="font-semibold text-redolence-green"><?= $totalTreatments ?></span> medical treatments
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&filter=<?= urlencode($filter) ?>"
                                               class="relative inline-flex items-center px-4 py-2 border text-sm font-semibold transition-all <?= $i === $page ? 'z-10 bg-redolence-green border-redolence-green text-white' : 'bg-white border-gray-300 text-gray-700 hover:bg-redolence-green/10 hover:border-redolence-green hover:text-redolence-green' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Medical Treatment Modal -->
<div id="serviceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-modal p-8 w-full max-w-4xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-8">
            <h2 id="modalTitle" class="text-2xl font-bold text-redolence-navy">Add Medical Treatment</h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="serviceForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="serviceId">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Name *</label>
                    <input type="text" name="name" id="serviceName" required
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Technology Used</label>
                    <input type="text" name="technology_used" id="serviceTechnology"
                           placeholder="e.g., HIFEM with RF Technology"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Session Frequency</label>
                    <input type="text" name="session_frequency" id="serviceFrequency"
                           placeholder="e.g., Once a week for 4 weeks"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Sort Order</label>
                    <input type="number" name="sort_order" id="serviceSortOrder" min="0" value="0"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
                </div>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Description</label>
                <div class="mb-2 flex justify-between items-center">
                    <small class="text-gray-600">
                        Rich text editor with medical treatment formatting
                    </small>
                    <div class="flex gap-2">
                        <button type="button" onclick="toggleEditor()" id="editorToggle"
                                class="text-xs px-3 py-1 bg-redolence-blue text-white rounded-lg hover:bg-redolence-blue/80 transition-colors">
                            Switch to HTML
                        </button>
                        <button type="button" onclick="insertTemplate()"
                                class="text-xs px-3 py-1 bg-redolence-green text-white rounded-lg hover:bg-redolence-green/80 transition-colors">
                            Insert Template
                        </button>
                    </div>
                </div>
                <textarea name="description" id="serviceDescription" rows="12"
                          placeholder="Enter treatment description with benefits and features..."
                          class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"></textarea>
                <div class="mt-2 text-xs text-gray-500" id="editorHelp">
                    <strong>Quick Tips:</strong> Use the toolbar above to format text, add lists, and create professional treatment descriptions.
                </div>
            </div>

            <!-- Treatment Badges -->
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Badges</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="flex items-center p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-xl border border-yellow-200">
                        <input type="checkbox" name="featured" id="serviceFeatured" value="1"
                               class="text-yellow-500 focus:ring-yellow-400 rounded">
                        <span class="ml-3 text-sm font-semibold text-yellow-700">⭐ Featured Treatment</span>
                    </label>
                    <label class="flex items-center p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-xl border border-red-200">
                        <input type="checkbox" name="popular" id="servicePopular" value="1"
                               class="text-red-500 focus:ring-red-400 rounded">
                        <span class="ml-3 text-sm font-semibold text-red-700">🔥 Popular Treatment</span>
                    </label>
                    <label class="flex items-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200">
                        <input type="checkbox" name="new_treatment" id="serviceNew" value="1"
                               class="text-green-500 focus:ring-green-400 rounded">
                        <span class="ml-3 text-sm font-semibold text-green-700">✨ New Treatment</span>
                    </label>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Price (Optional)</label>
                    <input type="number" name="price" id="servicePrice" step="0.01" min="0"
                           placeholder="Leave empty for TSH pricing"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <p class="text-xs text-gray-500 mt-1">Leave empty to display "TSH" (To be discussed)</p>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Duration (Optional)</label>
                    <input type="number" name="duration" id="serviceDuration" min="1"
                           placeholder="Duration in minutes"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <p class="text-xs text-gray-500 mt-1">Leave empty for variable duration treatments</p>
                </div>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Image</label>

                <!-- Image Type Selection -->
                <div class="mb-4">
                    <div class="flex space-x-6">
                        <label class="flex items-center">
                            <input type="radio" name="image_type" value="upload" id="imageTypeUpload" checked
                                   class="text-redolence-green focus:ring-redolence-green">
                            <span class="ml-2 text-sm text-gray-700 font-medium">Upload File</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="image_type" value="url" id="imageTypeUrl"
                                   class="text-redolence-green focus:ring-redolence-green">
                            <span class="ml-2 text-sm text-gray-700 font-medium">Image URL</span>
                        </label>
                    </div>
                </div>

                <!-- File Upload Option -->
                <div id="imageUploadSection" class="mb-4">
                    <input type="file" name="image" id="serviceImage" accept="image/*"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-redolence-green file:text-white hover:file:bg-redolence-green/90 transition-all">
                    <p class="text-xs text-gray-500 mt-2">Upload an image file (JPEG, PNG, GIF, WebP - max 5MB)</p>
                </div>

                <!-- URL Input Option -->
                <div id="imageUrlSection" class="mb-4 hidden">
                    <input type="url" name="image_url" id="serviceImageUrl" placeholder="https://example.com/image.jpg"
                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <p class="text-xs text-gray-500 mt-2">Enter a direct URL to an image</p>
                </div>

                <!-- Image Preview -->
                <div id="imagePreview" class="hidden mt-4">
                    <p class="text-sm text-gray-700 font-medium mb-3">Preview:</p>
                    <img id="previewImg" src="" alt="Image preview" class="w-32 h-32 object-cover rounded-xl border-2 border-gray-200">
                </div>
            </div>
            
            <div class="mb-8">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="serviceActive" value="1" checked 
                           class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                    <span class="ml-3 text-sm text-gray-700 font-medium">Treatment is active</span>
                </label>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Save Medical Treatment
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Custom Alert Modal -->
<div id="customAlert" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="medical-modal p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
            <div id="alertIcon" class="flex-shrink-0 mr-3">
                <!-- Icon will be inserted here -->
            </div>
            <h3 id="alertTitle" class="text-lg font-semibold text-redolence-navy"></h3>
        </div>
        <div id="alertMessage" class="text-gray-600 mb-6"></div>
        <div class="flex gap-3 justify-end">
            <button id="alertCancel" class="medical-btn-secondary hidden">
                Cancel
            </button>
            <button id="alertConfirm" class="medical-btn-primary">
                OK
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="medical-modal p-6 w-full max-w-lg mx-4 border-2 border-red-200">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
                <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-redolence-navy">Delete Medical Treatment</h3>
        </div>
        <div id="deleteMessage" class="text-gray-600 mb-6"></div>
        <div class="flex gap-3 justify-end">
            <button id="deleteCancel" class="medical-btn-secondary">
                Cancel
            </button>
            <button id="deleteConfirm" class="medical-btn-delete">
                Delete Treatment
            </button>
        </div>
    </div>
</div>

<!-- Include the existing JavaScript with medical terminology updates -->
<script>
// Clear any cached alert content on page load
document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing alert modals
    const alertModal = document.getElementById('customAlert');
    if (alertModal) {
        alertModal.classList.add('hidden');
    }

    // Clear any alert content that might be cached
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    if (alertTitle) alertTitle.textContent = '';
    if (alertMessage) alertMessage.innerHTML = '';

});

// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Medical Treatment';
    document.getElementById('formAction').value = 'create';
    document.getElementById('serviceForm').reset();
    document.getElementById('serviceActive').checked = true;

    // Reset image sections
    const currentImageDiv = document.getElementById('currentImageDisplay');
    if (currentImageDiv) {
        currentImageDiv.remove();
    }

    // Reset to upload option safely
    const imageTypeUpload = document.getElementById('imageTypeUpload');
    const imageUploadSection = document.getElementById('imageUploadSection');
    const imageUrlSection = document.getElementById('imageUrlSection');

    if (imageTypeUpload) {
        imageTypeUpload.checked = true;
    }

    if (imageUploadSection && imageUrlSection) {
        imageUploadSection.classList.remove('hidden');
        imageUrlSection.classList.add('hidden');
    }

    const modal = document.getElementById('serviceModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function closeModal() {
    document.getElementById('serviceModal').classList.add('hidden');
}

function editService(serviceId) {
    // Fetch service data and populate form
    fetch(`<?= getBasePath() ?>/api/admin/services/get.php?id=${serviceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(service => {
            document.getElementById('modalTitle').textContent = 'Edit Medical Treatment';
            document.getElementById('formAction').value = 'update';
            document.getElementById('serviceId').value = service.id;
            document.getElementById('serviceName').value = service.name;
            document.getElementById('serviceDescription').value = service.description || '';
            document.getElementById('servicePrice').value = service.price || '';
            document.getElementById('serviceDuration').value = service.duration || '';
            document.getElementById('serviceTechnology').value = service.technology_used || '';
            document.getElementById('serviceFrequency').value = service.session_frequency || '';
            document.getElementById('serviceSortOrder').value = service.sort_order || 0;
            document.getElementById('serviceActive').checked = service.is_active == 1;

            // Set treatment badges
            document.getElementById('serviceFeatured').checked = service.featured == 1;
            document.getElementById('servicePopular').checked = service.popular == 1;
            document.getElementById('serviceNew').checked = service.new_treatment == 1;

            // Handle image display if exists
            const currentImageDiv = document.getElementById('currentImageDisplay');
            if (currentImageDiv) {
                currentImageDiv.remove();
            }

            if (service.image) {
                const imageDisplay = document.createElement('div');
                imageDisplay.id = 'currentImageDisplay';
                imageDisplay.className = 'mb-3';

                // Check if image is a URL or uploaded file
                const imageSrc = service.image.startsWith('http') ? service.image : `<?= getBasePath() ?>/uploads/${service.image}`;

                imageDisplay.innerHTML = `
                    <p class="text-sm text-gray-700 font-medium mb-3">Current image:</p>
                    <img src="${imageSrc}" alt="Current treatment image" class="w-32 h-32 object-cover rounded-xl border-2 border-gray-200">
                `;
                document.getElementById('serviceImage').parentNode.insertBefore(imageDisplay, document.getElementById('serviceImage'));

                // If current image is a URL, pre-select URL option and populate field
                if (service.image.startsWith('http')) {
                    document.getElementById('imageTypeUrl').checked = true;
                    document.getElementById('serviceImageUrl').value = service.image;
                    toggleImageSections();
                }
            }

            document.getElementById('serviceModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error fetching service:', error);
            showAlert('error', 'Error Loading Treatment', 'Failed to load medical treatment data: ' + error.message);
        });
}

function deleteService(serviceId, serviceName) {
    showDeleteConfirmation(
        `Are you sure you want to delete "${serviceName}"?`,
        'This action cannot be undone. The medical treatment will be permanently removed from the system.',
        () => {
            // Show loading state
            showAlert('info', 'Deleting Treatment', 'Please wait while we delete the medical treatment...');

            // Perform delete request
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" value="${serviceId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    );
}

// Medical treatment form helper functions
function clearForm() {
    document.getElementById('serviceForm').reset();
    document.getElementById('formAction').value = 'create';
    document.getElementById('serviceId').value = '';
    document.getElementById('modalTitle').textContent = 'Add Medical Treatment';

    // Clear any existing image display
    const currentImageDiv = document.getElementById('currentImageDisplay');
    if (currentImageDiv) {
        currentImageDiv.remove();
    }

    // Reset image type selection
    document.getElementById('imageTypeUpload').checked = true;
    toggleImageInput();
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Close modal on backdrop click
document.getElementById('serviceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Global variables for image handling
let imageTypeUpload, imageTypeUrl, imageUploadSection, imageUrlSection, serviceImageUrl, serviceImage, imagePreview, previewImg;

// Global function for toggling image sections
function toggleImageSections() {
    if (imageTypeUpload.checked) {
        imageUploadSection.classList.remove('hidden');
        imageUrlSection.classList.add('hidden');
        serviceImageUrl.value = '';
        hidePreview();
    } else {
        imageUploadSection.classList.add('hidden');
        imageUrlSection.classList.remove('hidden');
        serviceImage.value = '';
        hidePreview();
    }
}

// Image type switching functionality
document.addEventListener('DOMContentLoaded', function() {
    imageTypeUpload = document.getElementById('imageTypeUpload');
    imageTypeUrl = document.getElementById('imageTypeUrl');
    imageUploadSection = document.getElementById('imageUploadSection');
    imageUrlSection = document.getElementById('imageUrlSection');
    serviceImageUrl = document.getElementById('serviceImageUrl');
    serviceImage = document.getElementById('serviceImage');
    imagePreview = document.getElementById('imagePreview');
    previewImg = document.getElementById('previewImg');

    // Make these functions global
    window.hidePreview = function() {
        imagePreview.classList.add('hidden');
        previewImg.src = '';
    }

    window.showPreview = function(src) {
        previewImg.src = src;
        imagePreview.classList.remove('hidden');
    }

    imageTypeUpload.addEventListener('change', toggleImageSections);
    imageTypeUrl.addEventListener('change', toggleImageSections);

    // Handle file upload preview
    serviceImage.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                showPreview(e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            hidePreview();
        }
    });

    // Handle URL input preview
    serviceImageUrl.addEventListener('input', function(e) {
        const url = e.target.value.trim();
        if (url && isValidImageUrl(url)) {
            // Test if image loads
            const testImg = new Image();
            testImg.onload = function() {
                showPreview(url);
            };
            testImg.onerror = function() {
                hidePreview();
            };
            testImg.src = url;
        } else {
            hidePreview();
        }
    });

    function isValidImageUrl(url) {
        try {
            new URL(url);
            return /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url);
        } catch {
            return false;
        }
    }

    // Check for delete error from PHP session
    <?php if (isset($_SESSION['delete_error'])): ?>
        const deleteErrorData = <?= $_SESSION['delete_error'] ?>;
        showDeleteError(deleteErrorData);
        <?php unset($_SESSION['delete_error']); ?>
    <?php endif; ?>
});

// Custom Alert System
function showAlert(type, title, message, callback = null) {
    const alertModal = document.getElementById('customAlert');
    const alertIcon = document.getElementById('alertIcon');
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    const alertConfirm = document.getElementById('alertConfirm');
    const alertCancel = document.getElementById('alertCancel');

    // Set icon based on type
    const icons = {
        success: '<svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
        error: '<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
        warning: '<svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
        info: '<svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    };

    alertIcon.innerHTML = icons[type] || icons.info;
    alertTitle.textContent = title;
    alertMessage.innerHTML = message;

    // Handle buttons
    alertCancel.classList.add('hidden');
    alertConfirm.onclick = () => {
        alertModal.classList.add('hidden');
        if (callback) callback();
    };

    alertModal.classList.remove('hidden');
}

function showDeleteConfirmation(title, message, onConfirm) {
    const deleteModal = document.getElementById('deleteModal');
    const deleteMessage = document.getElementById('deleteMessage');
    const deleteConfirm = document.getElementById('deleteConfirm');
    const deleteCancel = document.getElementById('deleteCancel');

    deleteMessage.innerHTML = `<p class="mb-2 font-semibold">${title}</p><p class="text-sm">${message}</p>`;

    deleteConfirm.onclick = () => {
        deleteModal.classList.add('hidden');
        onConfirm();
    };

    deleteCancel.onclick = () => {
        deleteModal.classList.add('hidden');
    };

    deleteModal.classList.remove('hidden');
}

// Enhanced error display for delete operations
function showDeleteError(errorData) {
    let message = `<p class="font-semibold text-red-600 mb-3">${errorData.error}</p>`;

    if (errorData.details) {
        message += `<p class="mb-3">${errorData.details}</p>`;
    }

    if (errorData.bookings && errorData.bookings.length > 0) {
        message += '<div class="bg-red-50 p-3 rounded-lg mb-3 border border-red-200"><p class="font-medium mb-2 text-red-800">Active Patient Appointments:</p>';
        errorData.bookings.forEach(booking => {
            message += `<p class="text-sm text-red-700">${booking}</p>`;
        });
        message += '</div>';
    }

    if (errorData.suggestions && errorData.suggestions.length > 0) {
        message += '<div class="bg-blue-50 p-3 rounded-lg border border-blue-200"><p class="font-medium mb-2 text-blue-800">Suggestions:</p><ul class="text-sm text-blue-700">';
        errorData.suggestions.forEach(suggestion => {
            message += `<li class="mb-1">• ${suggestion}</li>`;
        });
        message += '</ul></div>';
    }

    showAlert('error', 'Cannot Delete Medical Treatment', message);
}

// Treatment Variations Management Functions
function manageVariations(serviceId, serviceName) {
    // This would open a variations management modal
    // For now, we'll show an alert
    showAlert('info', 'Treatment Variations', `Variations management for "${serviceName}" will be implemented in the next phase.`);
}
</script>

<!-- TinyMCE Rich Text Editor -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
let editorInstance = null;
let isRichTextMode = true;

// Initialize TinyMCE when modal opens
function initializeEditor() {
    if (editorInstance) {
        return; // Already initialized
    }

    tinymce.init({
        selector: '#serviceDescription',
        height: 400,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; font-size: 14px; }',
        setup: function(editor) {
            editorInstance = editor;

            // Add custom button for medical template
            editor.ui.registry.addButton('medicalTemplate', {
                text: 'Medical Template',
                onAction: function() {
                    insertTemplate();
                }
            });

            // Update toolbar to include custom button
            editor.settings.toolbar += ' | medicalTemplate';
        },
        init_instance_callback: function(editor) {
            // Update help text when editor is ready
            document.getElementById('editorHelp').innerHTML =
                '<strong>Rich Text Mode:</strong> Use the toolbar to format your treatment description. Click "Switch to HTML" to edit raw HTML.';
        }
    });
}

// Toggle between rich text and HTML mode
function toggleEditor() {
    const toggleBtn = document.getElementById('editorToggle');

    if (isRichTextMode) {
        // Switch to HTML mode
        if (editorInstance) {
            const content = editorInstance.getContent();
            tinymce.remove('#serviceDescription');
            editorInstance = null;
            document.getElementById('serviceDescription').value = content;
            document.getElementById('serviceDescription').classList.add('font-mono', 'text-sm');
        }
        toggleBtn.textContent = 'Switch to Rich Text';
        document.getElementById('editorHelp').innerHTML =
            '<strong>HTML Mode:</strong> Edit raw HTML code. Use proper HTML tags for formatting.';
        isRichTextMode = false;
    } else {
        // Switch to rich text mode
        document.getElementById('serviceDescription').classList.remove('font-mono', 'text-sm');
        initializeEditor();
        toggleBtn.textContent = 'Switch to HTML';
        isRichTextMode = true;
    }
}

// Insert medical treatment template
function insertTemplate() {
    const template = `<h3>TREATMENT NAME</h3>
<p>Brief description of the treatment and how it works.</p>

<h4>Benefits:</h4>
<ul>
<li>✔️ Benefit 1</li>
<li>✔️ Benefit 2</li>
<li>✔️ Benefit 3</li>
</ul>

<p><strong>Session Duration:</strong> 30-60 minutes</p>
<p><strong>Recommended Frequency:</strong> Once a week for 4-6 weeks</p>

<h4>What to Expect:</h4>
<p>Describe the treatment experience and expected results.</p>`;

    if (isRichTextMode && editorInstance) {
        editorInstance.setContent(template);
    } else {
        document.getElementById('serviceDescription').value = template;
    }
}

// Enhanced openCreateModal function with editor support
window.openCreateModal = function() {
    // Set modal title and form action
    document.getElementById('modalTitle').textContent = 'Add Medical Treatment';
    document.getElementById('formAction').value = 'create';
    document.getElementById('serviceForm').reset();
    document.getElementById('serviceActive').checked = true;

    // Reset image sections
    const currentImageDiv = document.getElementById('currentImageDisplay');
    if (currentImageDiv) {
        currentImageDiv.remove();
    }

    // Reset to upload option safely
    const imageTypeUpload = document.getElementById('imageTypeUpload');
    const imageUploadSection = document.getElementById('imageUploadSection');
    const imageUrlSection = document.getElementById('imageUrlSection');

    if (imageTypeUpload) {
        imageTypeUpload.checked = true;
    }

    if (imageUploadSection && imageUrlSection) {
        imageUploadSection.classList.remove('hidden');
        imageUrlSection.classList.add('hidden');
    }

    // Show the modal
    const modal = document.getElementById('serviceModal');
    if (modal) {
        modal.classList.remove('hidden');
    }

    // Initialize editor after modal is shown
    setTimeout(() => {
        if (isRichTextMode) {
            initializeEditor();
        }
    }, 100);
};

// Override the editService function to handle editor
const originalEditService = editService;
editService = function(serviceId) {
    // First call the original function
    fetch(`<?= getBasePath() ?>/api/admin/services/get.php?id=${serviceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(service => {
            // Set all the form values
            document.getElementById('modalTitle').textContent = 'Edit Medical Treatment';
            document.getElementById('formAction').value = 'update';
            document.getElementById('serviceId').value = service.id;
            document.getElementById('serviceName').value = service.name;
            document.getElementById('serviceDescription').value = service.description || '';
            document.getElementById('servicePrice').value = service.price || '';
            document.getElementById('serviceDuration').value = service.duration || '';
            document.getElementById('serviceTechnology').value = service.technology_used || '';
            document.getElementById('serviceFrequency').value = service.session_frequency || '';
            document.getElementById('serviceSortOrder').value = service.sort_order || 0;
            document.getElementById('serviceActive').checked = service.is_active == 1;

            // Set treatment badges
            document.getElementById('serviceFeatured').checked = service.featured == 1;
            document.getElementById('servicePopular').checked = service.popular == 1;
            document.getElementById('serviceNew').checked = service.new_treatment == 1;

            // Handle image display
            const currentImageDiv = document.getElementById('currentImageDisplay');
            if (currentImageDiv) {
                currentImageDiv.remove();
            }

            if (service.image) {
                const imageDisplay = document.createElement('div');
                imageDisplay.id = 'currentImageDisplay';
                imageDisplay.className = 'mb-3';
                const imageSrc = service.image.startsWith('http') ? service.image : `<?= getBasePath() ?>/uploads/${service.image}`;
                imageDisplay.innerHTML = `
                    <p class="text-sm text-gray-700 font-medium mb-3">Current image:</p>
                    <img src="${imageSrc}" alt="Current treatment image" class="w-32 h-32 object-cover rounded-xl border-2 border-gray-200">
                `;
                document.getElementById('serviceImage').parentNode.insertBefore(imageDisplay, document.getElementById('serviceImage'));

                if (service.image.startsWith('http')) {
                    document.getElementById('imageTypeUrl').checked = true;
                    document.getElementById('serviceImageUrl').value = service.image;
                    toggleImageSections();
                }
            }

            document.getElementById('serviceModal').classList.remove('hidden');

            // Initialize editor after modal is shown
            setTimeout(() => {
                if (isRichTextMode) {
                    initializeEditor();
                    // Set content after editor is initialized
                    setTimeout(() => {
                        if (editorInstance) {
                            editorInstance.setContent(service.description || '');
                        }
                    }, 200);
                }
            }, 100);
        })
        .catch(error => {
            console.error('Error fetching service:', error);
            showAlert('error', 'Error Loading Treatment', 'Failed to load medical treatment data: ' + error.message);
        });
};

// Clean up editor when modal closes
const originalCloseModal = closeModal;
closeModal = function() {
    if (editorInstance) {
        tinymce.remove('#serviceDescription');
        editorInstance = null;
    }
    isRichTextMode = true;
    document.getElementById('editorToggle').textContent = 'Switch to HTML';
    originalCloseModal();
};
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>