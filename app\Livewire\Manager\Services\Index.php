<?php

namespace App\Livewire\Manager\Services;

use App\Models\Service;
use App\Models\ServiceCategory;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.manager')]
class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $categoryFilter = 'all';
    public $statusFilter = 'all';
    public $onlineFilter = 'all';

    public $confirmingDeletion = false;
    public $serviceToDelete = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'categoryFilter' => ['except' => 'all'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function confirmDeletion($id)
    {
        $this->confirmingDeletion = true;
        $this->serviceToDelete = $id;
    }

    public function delete()
    {
        $service = Service::findOrFail($this->serviceToDelete);
        $service->delete(); // Immediate deletion (Hard delete as requested)

        $this->confirmingDeletion = false;
        $this->serviceToDelete = null;
        session()->flash('message', 'Service deleted successfully.');
    }

    public function toggleStatus(Service $service)
    {
        $service->update(['is_active' => !$service->is_active]);
        $status = $service->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Service {$status} successfully.");
    }

    public function toggleFeatured(Service $service)
    {
        $service->update(['is_featured' => !$service->is_featured]);
        $status = $service->is_featured ? 'featured' : 'unfeatured';
        session()->flash('message', "Service {$status} successfully.");
    }

    public function toggleOnline(Service $service)
    {
        $service->update(['is_available_online' => !$service->is_available_online]);
        $status = $service->is_available_online ? 'enabled for online booking' : 'disabled from online booking';
        session()->flash('message', "Service {$status} successfully.");
    }

    public function render()
    {
        $query = Service::with('category');

        if ($this->search) {
            $query->where('name', 'like', '%' . $this->search . '%');
        }

        if ($this->categoryFilter !== 'all') {
            $query->where('category_id', $this->categoryFilter);
        }

        if ($this->statusFilter !== 'all') {
            $query->where('is_active', $this->statusFilter === 'active');
        }

        if ($this->onlineFilter !== 'all') {
            $query->where('is_available_online', $this->onlineFilter === 'online');
        }

        $services = $query->orderBy('name')->paginate(10);

        $categories = ServiceCategory::orderBy('name')->get();

        // Stats
        $stats = [
            'total' => Service::count(),
            'active' => Service::where('is_active', true)->count(),
            'online' => Service::where('is_available_online', true)->count(),
        ];

        return view('livewire.manager.services.index', [
            'services' => $services,
            'categories' => $categories,
            'stats' => $stats,
        ]);
    }
}
