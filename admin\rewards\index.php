<?php
/**
 * Admin Rewards Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/rewards_functions.php';

// Create customer_tiers table if it doesn't exist
try {
    $database->execute("
        CREATE TABLE IF NOT EXISTS customer_tiers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            min_spent INT NOT NULL DEFAULT 0,
            points_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.00,
            benefits TEXT,
            color VARCHAR(50) NOT NULL DEFAULT 'text-gray-600',
            bg_color VARCHAR(50) NOT NULL DEFAULT 'bg-gray-100',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Check if table has any data, if not create default tiers
    $tierCountResult = $database->fetch("SELECT COUNT(*) as count FROM customer_tiers");
    $tierCount = $tierCountResult['count'];
    if ($tierCount == 0) {
        createDefaultCustomerTiers();
    }
} catch (Exception $e) {
    error_log("Error creating customer_tiers table: " . $e->getMessage());
}

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_settings':
                    $settings = [
                        'pointsPerTZS' => floatval($_POST['points_per_tzs']),
                        'welcomeBonus' => intval($_POST['welcome_bonus']),
                        'referralBonus' => intval($_POST['referral_bonus']),
                        'birthdayBonus' => intval($_POST['birthday_bonus']),
                        'reviewBonus' => intval($_POST['review_bonus']),
                        'minimumRedemption' => intval($_POST['minimum_redemption'])
                    ];
                    
                    updateRewardSettings($settings);
                    $message = 'Reward settings updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'award_points':
                    $customerId = $_POST['customer_id'];
                    $points = intval($_POST['points']);
                    $description = $_POST['description'];
                    
                    awardPoints($customerId, $points, $description);
                    $message = 'Points awarded successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'redeem_points':
                    $customerId = $_POST['customer_id'];
                    $points = intval($_POST['points']);
                    $description = $_POST['description'];

                    redeemPoints($customerId, $points, $description);
                    $message = 'Points redeemed successfully!';
                    $messageType = 'success';
                    break;

                case 'create_reward':
                    $rewardData = [
                        'name' => $_POST['reward_name'],
                        'description' => $_POST['reward_description'],
                        'points_required' => intval($_POST['points_cost']),
                        'value' => floatval($_POST['reward_value']),
                        'type' => $_POST['reward_type'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $rewardId = createReward($rewardData);
                    if ($rewardId) {
                        $message = 'Reward created successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to create reward');
                    }
                    break;

                case 'update_reward':
                    $rewardId = $_POST['reward_id'];
                    $rewardData = [
                        'name' => $_POST['reward_name'],
                        'description' => $_POST['reward_description'],
                        'points_required' => intval($_POST['points_cost']),
                        'value' => floatval($_POST['reward_value']),
                        'type' => $_POST['reward_type'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $result = updateReward($rewardId, $rewardData);
                    if ($result) {
                        $message = 'Reward updated successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to update reward');
                    }
                    break;

                case 'delete_reward':
                    $rewardId = $_POST['reward_id'];

                    $result = deleteReward($rewardId);
                    if ($result) {
                        $message = 'Reward deleted successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to delete reward');
                    }
                    break;

                case 'create_tier':
                    $tierData = [
                        'name' => $_POST['tier_name'],
                        'min_spent' => intval($_POST['min_spent']),
                        'points_multiplier' => floatval($_POST['points_multiplier']),
                        'benefits' => $_POST['benefits'],
                        'color' => $_POST['color'],
                        'bg_color' => $_POST['bg_color'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'sort_order' => intval($_POST['sort_order'])
                    ];

                    $tierId = createCustomerTier($tierData);
                    if ($tierId) {
                        $message = 'Customer tier created successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to create customer tier');
                    }
                    break;

                case 'update_tier':
                    $tierId = $_POST['tier_id'];
                    $tierData = [
                        'name' => $_POST['tier_name'],
                        'min_spent' => intval($_POST['min_spent']),
                        'points_multiplier' => floatval($_POST['points_multiplier']),
                        'benefits' => $_POST['benefits'],
                        'color' => $_POST['color'],
                        'bg_color' => $_POST['bg_color'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'sort_order' => intval($_POST['sort_order'])
                    ];

                    $result = updateCustomerTier($tierId, $tierData);
                    if ($result) {
                        $message = 'Customer tier updated successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to update customer tier');
                    }
                    break;

                case 'delete_tier':
                    $tierId = $_POST['tier_id'];

                    $result = deleteCustomerTier($tierId);
                    if ($result) {
                        $message = 'Customer tier deactivated successfully. The tier is now inactive and customers will be reassigned to appropriate tiers.';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to deactivate customer tier');
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get rewards data
$rewardsData = getRewardsData();

$pageTitle = "Points & Rewards";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Rewards Management CSS -->
<style>
/* Medical Rewards Management Specific Styles */
.medical-rewards-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-rewards-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-rewards-card:hover::before {
    left: 100%;
}

.medical-rewards-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-reward-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-reward-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-reward-item:hover::before {
    transform: scaleX(1);
}

.medical-reward-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-tier-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tier-bronze {
    background: linear-gradient(135deg, #cd7f32, #b8722c);
    color: white;
}

.tier-silver {
    background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
    color: white;
}

.tier-gold {
    background: linear-gradient(135deg, #ffd700, #e6c200);
    color: black;
}

.tier-platinum {
    background: linear-gradient(135deg, #e5e4e2, #d3d3d3);
    color: black;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-rewards-grid {
        grid-template-columns: 1fr;
    }

    .medical-rewards-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-rewards-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Points & Rewards
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive loyalty program and customer rewards management system</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= number_format($rewardsData['stats']['activeMembers']) ?> Active Members
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Loyalty System
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openSettingsModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    ⚙️ Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Points Issued</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($rewardsData['stats']['totalPointsIssued']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Points Redeemed</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($rewardsData['stats']['totalPointsRedeemed']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Active Members</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($rewardsData['stats']['activeMembers']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-purple-100 to-purple-200">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Engagement Rate</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $rewardsData['stats']['engagementRate'] ?>%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Customer Tiers -->
                        <div class="medical-rewards-card p-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Customer Tiers</h3>
                                <button onclick="openCreateTierModal()" class="medical-btn-primary text-sm px-4 py-2">
                                    + Add Tier
                                </button>
                            </div>
                            <div class="space-y-4">
                                <?php if (empty($rewardsData['tiers'])): ?>
                                    <div class="text-center py-8">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                        </svg>
                                        <p class="text-gray-600 mb-4">No customer tiers available</p>
                                        <button onclick="openCreateTierModal()" class="medical-btn-primary">
                                            Create First Tier
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($rewardsData['tiers'] as $tier): ?>
                                        <div class="medical-reward-item">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="flex items-center gap-3">
                                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-redolence-green to-redolence-blue"></div>
                                                    <h4 class="font-bold text-redolence-navy"><?= $tier['name'] ?></h4>
                                                    <?php if (!$tier['isActive']): ?>
                                                        <span class="medical-tier-badge bg-red-100 text-red-800">Inactive</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <span class="text-sm font-semibold text-redolence-green"><?= $tier['pointsMultiplier'] ?>x points</span>
                                                    <div class="flex gap-1">
                                                        <button onclick="editTier('<?= $tier['id'] ?>')" class="medical-btn-secondary text-xs px-2 py-1">
                                                            Edit
                                                        </button>
                                                        <button onclick="deleteTierConfirm('<?= $tier['id'] ?>', '<?= htmlspecialchars($tier['name']) ?>')" class="medical-btn-danger text-xs px-2 py-1">
                                                            Deactivate
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-3">Minimum spent: TSH <?= number_format($tier['minSpent']) ?></p>
                                            <div class="flex flex-wrap gap-1">
                                                <?php foreach ($tier['benefits'] as $benefit): ?>
                                                    <span class="inline-block px-2 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-xs rounded-full text-green-800 border border-green-200">
                                                        <?= htmlspecialchars($benefit) ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Tier Distribution -->
                        <div class="medical-rewards-card p-8">
                            <h3 class="text-xl font-bold text-redolence-navy mb-6">Tier Distribution</h3>
                            <div class="space-y-4">
                                <?php foreach ($rewardsData['tierDistribution'] as $distribution): ?>
                                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                                        <div class="flex items-center gap-3">
                                            <div class="w-4 h-4 rounded-full bg-gradient-to-r from-redolence-green to-redolence-blue"></div>
                                            <span class="font-semibold text-redolence-navy"><?= $distribution['name'] ?></span>
                                        </div>
                                        <div class="text-right">
                                            <span class="text-xl font-bold text-redolence-green"><?= $distribution['count'] ?></span>
                                            <span class="text-sm text-gray-600 ml-1">customers</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Rewards Catalog & Recent Transactions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Rewards Management -->
                        <div class="medical-rewards-card p-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Reward Catalog</h3>
                                <button onclick="openCreateRewardModal()" class="medical-btn-primary text-sm px-4 py-2">
                                    + Add Reward
                                </button>
                            </div>
                            <div class="space-y-4">
                                <?php if (empty($rewardsData['redemptions'])): ?>
                                    <div class="text-center py-8">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                        </svg>
                                        <p class="text-gray-600 mb-4">No rewards available</p>
                                        <button onclick="openCreateRewardModal()" class="medical-btn-primary">
                                            Create First Reward
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($rewardsData['redemptions'] as $reward): ?>
                                        <div class="medical-reward-item">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="flex items-center gap-3">
                                                    <h4 class="font-bold text-redolence-navy"><?= htmlspecialchars($reward['name']) ?></h4>
                                                    <?php if (!$reward['isActive']): ?>
                                                        <span class="medical-tier-badge bg-red-100 text-red-800">Inactive</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <span class="font-bold text-redolence-green"><?= number_format($reward['pointsCost']) ?> pts</span>
                                                    <div class="flex gap-1">
                                                        <button onclick="editReward('<?= $reward['id'] ?>')" class="medical-btn-secondary text-xs px-2 py-1">
                                                            Edit
                                                        </button>
                                                        <button onclick="deleteRewardConfirm('<?= $reward['id'] ?>', '<?= htmlspecialchars($reward['name']) ?>')" class="medical-btn-danger text-xs px-2 py-1">
                                                            Delete
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-3"><?= htmlspecialchars($reward['description']) ?></p>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm font-semibold text-green-600">Value: TSH <?= number_format($reward['value']) ?></span>
                                                <div class="flex items-center gap-4">
                                                    <span class="text-sm text-gray-600"><?= $reward['usageCount'] ?> redeemed</span>
                                                    <span class="text-xs text-gray-500 capitalize bg-gray-100 px-2 py-1 rounded-full"><?= $reward['type'] ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Recent Transactions -->
                        <div class="medical-rewards-card p-8">
                            <h3 class="text-xl font-bold text-redolence-navy mb-6">Recent Transactions</h3>
                            <div class="space-y-4">
                                <?php if (empty($rewardsData['recentTransactions'])): ?>
                                    <div class="text-center py-8">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <p class="text-gray-600">No point transactions yet</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($rewardsData['recentTransactions'] as $transaction): ?>
                                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                                            <div>
                                                <p class="font-semibold text-redolence-navy"><?= htmlspecialchars($transaction['customerName']) ?></p>
                                                <p class="text-sm text-gray-600"><?= htmlspecialchars($transaction['description']) ?></p>
                                                <p class="text-xs text-gray-500"><?= date('M j, Y', strtotime($transaction['createdAt'])) ?></p>
                                            </div>
                                            <div class="text-right">
                                                <span class="text-lg font-bold <?= $transaction['type'] === 'EARNED' ? 'text-green-600' : 'text-red-600' ?>">
                                                    <?= $transaction['type'] === 'EARNED' ? '+' : '' ?><?= number_format($transaction['points']) ?>
                                                </span>
                                                <p class="text-xs text-gray-500 uppercase tracking-wide"><?= $transaction['type'] ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="medical-rewards-card p-8">
                        <h3 class="text-xl font-bold text-redolence-navy mb-6">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <button onclick="openAwardPointsModal()" 
                                    class="flex items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-2xl hover:border-redolence-green transition-colors group">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                    <p class="font-bold text-redolence-navy">Award Points</p>
                                    <p class="text-sm text-gray-600">Give points to customers</p>
                                </div>
                            </button>
                            
                            <button onclick="openRedeemPointsModal()" 
                                    class="flex items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-2xl hover:border-redolence-green transition-colors group">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                        </svg>
                                    </div>
                                    <p class="font-bold text-redolence-navy">Redeem Points</p>
                                    <p class="text-sm text-gray-600">Process point redemptions</p>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Settings Modal -->
<div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-redolence-navy">Reward Settings</h3>
                    <button onclick="closeModal('settingsModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <form method="POST" class="p-8 space-y-6">
                <input type="hidden" name="action" value="update_settings">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="points_per_tzs" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Points per TSH 1,000</label>
                        <input type="number" id="points_per_tzs" name="points_per_tzs"
                               value="<?= $rewardsData['settings']['pointsPerTZS'] ?>" min="0" step="0.1" required
                               class="medical-form-input w-full">
                    </div>

                    <div>
                        <label for="minimum_redemption" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Minimum Redemption</label>
                        <input type="number" id="minimum_redemption" name="minimum_redemption"
                               value="<?= $rewardsData['settings']['minimumRedemption'] ?>" min="1" required
                               class="medical-form-input w-full">
                    </div>

                    <div>
                        <label for="welcome_bonus" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Welcome Bonus</label>
                        <input type="number" id="welcome_bonus" name="welcome_bonus"
                               value="<?= $rewardsData['settings']['welcomeBonus'] ?>" min="0" required
                               class="medical-form-input w-full">
                    </div>

                    <div>
                        <label for="referral_bonus" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Referral Bonus</label>
                        <input type="number" id="referral_bonus" name="referral_bonus"
                               value="<?= $rewardsData['settings']['referralBonus'] ?>" min="0" required
                               class="medical-form-input w-full">
                    </div>

                    <div>
                        <label for="birthday_bonus" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Birthday Bonus</label>
                        <input type="number" id="birthday_bonus" name="birthday_bonus"
                               value="<?= $rewardsData['settings']['birthdayBonus'] ?>" min="0" required
                               class="medical-form-input w-full">
                    </div>

                    <div>
                        <label for="review_bonus" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Review Bonus</label>
                        <input type="number" id="review_bonus" name="review_bonus"
                               value="<?= $rewardsData['settings']['reviewBonus'] ?>" min="0" required
                               class="medical-form-input w-full">
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('settingsModal')" class="medical-btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Award Points Modal -->
<div id="awardPointsModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 max-w-md w-full">
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-redolence-navy">Award Points</h3>
                    <button onclick="closeModal('awardPointsModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <form method="POST" class="p-8 space-y-6">
                <input type="hidden" name="action" value="award_points">

                <div>
                    <label for="customer_id_award" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Customer ID</label>
                    <input type="text" id="customer_id_award" name="customer_id" required class="medical-form-input w-full">
                </div>

                <div>
                    <label for="points_award" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Points to Award</label>
                    <input type="number" id="points_award" name="points" min="1" required class="medical-form-input w-full">
                </div>

                <div>
                    <label for="description_award" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Description</label>
                    <input type="text" id="description_award" name="description" required class="medical-form-input w-full">
                </div>

                <div class="flex items-center justify-end space-x-4 pt-4">
                    <button type="button" onclick="closeModal('awardPointsModal')" class="medical-btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary">
                        Award Points
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Redeem Points Modal -->
<div id="redeemPointsModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 max-w-md w-full">
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-redolence-navy">Redeem Points</h3>
                    <button onclick="closeModal('redeemPointsModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <form method="POST" class="p-8 space-y-6">
                <input type="hidden" name="action" value="redeem_points">

                <div>
                    <label for="customer_id_redeem" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Customer ID</label>
                    <input type="text" id="customer_id_redeem" name="customer_id" required class="medical-form-input w-full">
                </div>

                <div>
                    <label for="points_redeem" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Points to Redeem</label>
                    <input type="number" id="points_redeem" name="points" min="1" required class="medical-form-input w-full">
                </div>

                <div>
                    <label for="description_redeem" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Description</label>
                    <input type="text" id="description_redeem" name="description" required class="medical-form-input w-full">
                </div>

                <div class="flex items-center justify-end space-x-4 pt-4">
                    <button type="button" onclick="closeModal('redeemPointsModal')" class="medical-btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-danger">
                        Redeem Points
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openSettingsModal() {
    document.getElementById('settingsModal').classList.remove('hidden');
}

function openAwardPointsModal() {
    document.getElementById('awardPointsModal').classList.remove('hidden');
}

function openRedeemPointsModal() {
    document.getElementById('redeemPointsModal').classList.remove('hidden');
}

function openCreateRewardModal() {
    // Implementation would go here
    alert('Create reward modal functionality would be implemented here');
}

function editReward(rewardId) {
    // Implementation would go here
    alert('Edit reward functionality would be implemented here');
}

function deleteRewardConfirm(rewardId, rewardName) {
    if (confirm(`Are you sure you want to delete "${rewardName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_reward">
            <input type="hidden" name="reward_id" value="${rewardId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function openCreateTierModal() {
    // Implementation would go here
    alert('Create tier modal functionality would be implemented here');
}

function editTier(tierId) {
    // Implementation would go here
    alert('Edit tier functionality would be implemented here');
}

function deleteTierConfirm(tierId, tierName) {
    if (confirm(`Are you sure you want to deactivate "${tierName}"? Customers will be reassigned to appropriate tiers.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_tier">
            <input type="hidden" name="tier_id" value="${tierId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = ['settingsModal', 'awardPointsModal', 'redeemPointsModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal && !modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});

// Close modal on backdrop click
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        closeModal(e.target.id);
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>