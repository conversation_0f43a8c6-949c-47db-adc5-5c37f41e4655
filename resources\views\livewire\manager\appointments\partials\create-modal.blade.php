{{-- Create Appointment Modal --}}
<div 
    x-data="{ show: @entangle('showCreateModal') }"
    x-show="show"
    x-cloak
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title" 
    role="dialog" 
    aria-modal="true">
    
    {{-- Backdrop --}}
    <div 
        x-show="show"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="$wire.closeModals()">
    </div>

    {{-- Modal Panel --}}
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div 
            x-show="show"
            x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
            
            {{-- Header --}}
            <div class="bg-gradient-to-r from-[#E98CA5] to-[#F7B5C8] px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <flux:icon icon="plus" class="w-6 h-6 text-white" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Create New Appointment</h3>
                            <p class="text-sm text-white/80">Schedule a new appointment for a client</p>
                        </div>
                    </div>
                    <button 
                        wire:click="closeModals"
                        class="text-white/80 hover:text-white transition-colors">
                        <flux:icon icon="x-mark" class="w-6 h-6" />
                    </button>
                </div>
            </div>

            {{-- Form --}}
            <form wire:submit.prevent="createAppointment">
                <div class="px-6 py-6 space-y-6">
                    {{-- Client Selection --}}
                    <div>
                        <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                            Client <span class="text-red-500">*</span>
                        </label>
                        <select 
                            wire:model="client_id"
                            class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                            <option value="">Select a client</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}">{{ $client->name }} ({{ $client->email }})</option>
                            @endforeach
                        </select>
                        @error('client_id') 
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    {{-- Service Selection --}}
                    <div>
                        <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                            Service <span class="text-red-500">*</span>
                        </label>
                        <select 
                            wire:model="service_id"
                            class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                            <option value="">Select a service</option>
                            @foreach($services as $service)
                                <option value="{{ $service->id }}">
                                    {{ $service->name }} - ${{ number_format($service->price, 2) }} ({{ $service->duration }} min)
                                </option>
                            @endforeach
                        </select>
                        @error('service_id') 
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    {{-- Staff Selection --}}
                    <div>
                        <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                            Staff Member
                        </label>
                        <select 
                            wire:model="staff_id"
                            class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                            <option value="">No preference</option>
                            @foreach($staff as $member)
                                <option value="{{ $member->id }}">{{ $member->name }}</option>
                            @endforeach
                        </select>
                        @error('staff_id') 
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        {{-- Date --}}
                        <div>
                            <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                                Date <span class="text-red-500">*</span>
                            </label>
                            <input 
                                type="date"
                                wire:model="appointment_date"
                                min="{{ date('Y-m-d') }}"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                            />
                            @error('appointment_date') 
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        {{-- Time --}}
                        <div>
                            <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                                Time <span class="text-red-500">*</span>
                            </label>
                            <input 
                                type="time"
                                wire:model="start_time"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                            />
                            @error('start_time') 
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    {{-- Notes --}}
                    <div>
                        <label class="block text-sm font-semibold text-[#2C2C34] mb-2">
                            Notes
                        </label>
                        <textarea 
                            wire:model="notes"
                            rows="3"
                            placeholder="Add any special requests or notes..."
                            class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all resize-none"></textarea>
                        @error('notes') 
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                {{-- Footer --}}
                <div class="bg-[#F7E9E6]/30 px-6 py-4 flex items-center justify-end gap-3">
                    <x-manager.button 
                        type="button"
                        wire:click="closeModals"
                        variant="outline" 
                        size="md">
                        Cancel
                    </x-manager.button>
                    <x-manager.button 
                        type="submit"
                        variant="primary" 
                        icon="check"
                        size="md">
                        Create Appointment
                    </x-manager.button>
                </div>
            </form>
        </div>
    </div>
</div>
