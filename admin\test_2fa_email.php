<?php
/**
 * Test 2FA Email Functionality
 * This script tests if the 2FA email system is working properly
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/admin_2fa_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('Access denied. Admin login required.');
}

$adminId = $_SESSION['user_id'];
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'test_email') {
        $testEmail = $_POST['test_email'] ?? '';
        
        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $messageType = 'error';
        } else {
            // Test the 2FA email function
            $result = sendAdminEmail(
                $testEmail,
                'Test 2FA Email - Flix Salon & SPA',
                'admin_2fa_email_code',
                [
                    'admin_name' => $_SESSION['user_name'],
                    'verification_code' => '123456',
                    'expires_minutes' => 10,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            );
            
            if ($result) {
                $message = "Test email sent successfully to: $testEmail";
                $messageType = 'success';
            } else {
                $message = "Failed to send test email to: $testEmail. Check error logs for details.";
                $messageType = 'error';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 2FA Email - Flix Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-gray-800 rounded-lg p-6">
                <h1 class="text-2xl font-bold mb-6">🧪 Test 2FA Email System</h1>
                
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-500/20 border border-green-500/50 text-green-200' : 'bg-red-500/20 border border-red-500/50 text-red-200'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">📧 Email Configuration Status</h2>
                    <div class="bg-gray-700 rounded p-4 space-y-2">
                        <div><strong>SMTP Host:</strong> <?php echo SMTP_HOST; ?></div>
                        <div><strong>SMTP Port:</strong> <?php echo SMTP_PORT; ?></div>
                        <div><strong>SMTP Username:</strong> <?php echo SMTP_USERNAME; ?></div>
                        <div><strong>SMTP Security:</strong> <?php echo SMTP_SECURE; ?></div>
                        <div><strong>From Email:</strong> <?php echo SMTP_FROM_EMAIL; ?></div>
                        <div><strong>From Name:</strong> <?php echo SMTP_FROM_NAME; ?></div>
                    </div>
                </div>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="test_email" class="block text-sm font-medium mb-2">Test Email Address:</label>
                        <input type="email" 
                               id="test_email" 
                               name="test_email" 
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter email to test..."
                               required>
                    </div>
                    
                    <button type="submit" 
                            name="action" 
                            value="test_email"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        📤 Send Test Email
                    </button>
                </form>
                
                <div class="mt-8 p-4 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                    <h3 class="font-semibold text-yellow-200 mb-2">ℹ️ Test Information</h3>
                    <ul class="text-yellow-300 text-sm space-y-1">
                        <li>• This will send a test 2FA email with code "123456"</li>
                        <li>• Check your email inbox and spam folder</li>
                        <li>• If the email fails, check the PHP error logs</li>
                        <li>• Make sure SMTP credentials are correct in config/app.php</li>
                    </ul>
                </div>
                
                <div class="mt-6 text-center">
                    <a href="/flix/admin/profile/index.php" class="text-blue-400 hover:text-blue-300">← Back to Profile</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
