<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Get report entries
$entries = $progressiveReportEntry->getByReportId($reportId, 'entry_date ASC');

// Simple PDF generation without external libraries
class SimplePDF {
    private $content = '';
    private $title = '';
    
    public function __construct($title = 'Document') {
        $this->title = $title;
    }
    
    public function addContent($content) {
        $this->content .= $content;
    }
    
    public function output($filename = 'document.pdf') {
        // For now, we'll create an HTML version that can be printed to PDF
        // This is a lightweight approach that works without external libraries
        
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="' . $filename . '"');
        
        echo $this->generateHTML();
    }
    
    private function generateHTML() {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>' . htmlspecialchars($this->title) . '</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #49A75C;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #49A75C;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .patient-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #49A75C;
        }
        .patient-info h2 {
            margin-top: 0;
            color: #49A75C;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-label {
            font-weight: bold;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            color: #333;
        }
        .entries-section {
            margin-top: 40px;
        }
        .entries-title {
            font-size: 24px;
            color: #49A75C;
            border-bottom: 2px solid #49A75C;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .entry {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .entry-date {
            font-weight: bold;
            color: #49A75C;
            font-size: 16px;
        }
        .entry-treatment {
            background: #e8f5e8;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            color: #2d5a2d;
        }
        .entry-description {
            margin: 15px 0;
            line-height: 1.8;
        }
        .entry-notes {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #49A75C;
            margin-top: 15px;
        }
        .entry-notes h4 {
            margin-top: 0;
            color: #49A75C;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #49A75C;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .print-button:hover {
            background: #3d8a4f;
        }
        @media print {
            .print-button { display: none; }
        }
    </style>
    <script>
        function printPDF() {
            window.print();
        }
    </script>
</head>
<body>
    <button class="print-button no-print" onclick="printPDF()">Print/Save as PDF</button>
    ' . $this->content . '
</body>
</html>';
    }
}

// Create PDF
$pdf = new SimplePDF($report['title'] . ' - ' . $report['client_name']);

// Add content
$content = '
<div class="header">
    <div class="logo">REDOLENCE</div>
    <div class="subtitle">Medical Aesthetics & Wellness</div>
    <div class="subtitle">Progressive Treatment Report</div>
</div>

<div class="patient-info">
    <h2>Patient Information</h2>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Patient Name</div>
            <div class="info-value">' . htmlspecialchars($report['client_name']) . '</div>
        </div>
        <div class="info-item">
            <div class="info-label">Email</div>
            <div class="info-value">' . htmlspecialchars($report['client_email']) . '</div>
        </div>
        <div class="info-item">
            <div class="info-label">Report Title</div>
            <div class="info-value">' . htmlspecialchars($report['title']) . '</div>
        </div>
        <div class="info-item">
            <div class="info-label">Status</div>
            <div class="info-value">' . ucfirst(strtolower($report['status'])) . '</div>
        </div>
        <div class="info-item">
            <div class="info-label">Created Date</div>
            <div class="info-value">' . date('F j, Y', strtotime($report['created_at'])) . '</div>
        </div>
        <div class="info-item">
            <div class="info-label">Total Entries</div>
            <div class="info-value">' . count($entries) . ' treatment entries</div>
        </div>
    </div>';

if ($report['description']) {
    $content .= '
    <div style="margin-top: 20px;">
        <div class="info-label">Treatment Plan & Goals</div>
        <div class="info-value">' . nl2br(htmlspecialchars($report['description'])) . '</div>
    </div>';
}

$content .= '
</div>

<div class="entries-section">
    <h2 class="entries-title">Treatment Timeline (' . count($entries) . ' entries)</h2>';

if (empty($entries)) {
    $content .= '<p style="text-align: center; color: #666; font-style: italic; padding: 40px;">No treatment entries recorded yet.</p>';
} else {
    foreach ($entries as $entry) {
        $content .= '
        <div class="entry">
            <div class="entry-header">
                <div class="entry-date">' . date('F j, Y', strtotime($entry['entry_date'])) . '</div>
                <div class="entry-treatment">' . htmlspecialchars($entry['treatment']) . '</div>
            </div>
            
            <div class="entry-description">
                ' . nl2br(htmlspecialchars($entry['description'])) . '
            </div>';
        
        if ($entry['notes']) {
            $content .= '
            <div class="entry-notes">
                <h4>Clinical Notes & Recommendations</h4>
                ' . nl2br(htmlspecialchars($entry['notes'])) . '
            </div>';
        }
        
        if ($entry['appointment_date']) {
            $content .= '
            <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #eee; font-size: 14px; color: #666;">
                <strong>Related Appointment:</strong> ' . date('M j, Y', strtotime($entry['appointment_date']));
            
            if ($entry['service_name']) {
                $content .= ' - ' . htmlspecialchars($entry['service_name']);
            }
            
            if ($entry['staff_name']) {
                $content .= ' (with ' . htmlspecialchars($entry['staff_name']) . ')';
            }
            
            $content .= '
            </div>';
        }
        
        $content .= '
        </div>';
    }
}

$content .= '
</div>

<div class="footer">
    <p><strong>REDOLENCE Medical Aesthetics & Wellness</strong></p>
    <p>This report was generated on ' . date('F j, Y \a\t g:i A') . '</p>
    <p>For questions about this report, please contact our medical team.</p>
</div>';

$pdf->addContent($content);

// Output PDF
$filename = 'Progressive_Report_' . preg_replace('/[^A-Za-z0-9_-]/', '_', $report['client_name']) . '_' . date('Y-m-d') . '.pdf';
$pdf->output($filename);
?>
