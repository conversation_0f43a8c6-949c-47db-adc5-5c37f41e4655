<div>
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        {{-- Flash Messages --}}
        @if (session()->has('success'))
            <div
                class="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-xl p-4 flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="font-medium">{{ session('success') }}</p>
                </div>
                <button onclick="this.parentElement.remove()" class="text-green-600 hover:text-green-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif

        {{-- Page Header (Feature 1: Professional Header) --}}
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
            <div>
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Team Management</h1>
                <p class="text-gray-600 mt-1 text-sm sm:text-base">Manage staff members, roles, and performance</p>
            </div>
            <button onclick="showCreateModal()"
                class="w-full sm:w-auto px-4 py-2 bg-[#B48B5B] hover:bg-[#9A7449] text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Team Member
            </button>
        </div>

        {{-- Statistics Cards (Feature 2: Real-time Statistics) --}}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Total Team</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1">{{ $stats['total'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Active</p>
                        <p class="text-3xl font-bold text-green-600 mt-1">{{ $stats['active'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">On Leave</p>
                        <p class="text-3xl font-bold text-yellow-600 mt-1">{{ $stats['on_leave'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Available</p>
                        <p class="text-3xl font-bold text-purple-600 mt-1">{{ $stats['available'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Advanced Filters (Feature 3: Multi-criteria Filtering) --}}
        <div class="bg-white rounded-xl p-4 sm:p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <div class="flex flex-col gap-4">
                {{-- Search Bar --}}
                <div class="w-full">
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input type="text" wire:model.live="search" placeholder="Search by name, email, position..."
                            class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent transition-all" />
                    </div>
                </div>

                {{-- Filter Dropdowns --}}
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                    <select wire:model.live="departmentFilter"
                        class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-[#B48B5B] transition-all">
                        <option value="all">All Departments</option>
                        @foreach($departments as $dept)
                            <option value="{{ $dept }}">{{ $dept }}</option>
                        @endforeach
                    </select>
                    <select wire:model.live="statusFilter"
                        class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-[#B48B5B] transition-all">
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="on-leave">On Leave</option>
                        <option value="terminated">Terminated</option>
                    </select>
                    <select wire:model.live="employmentTypeFilter"
                        class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-[#B48B5B] transition-all">
                        <option value="all">All Types</option>
                        <option value="full-time">Full-time</option>
                        <option value="part-time">Part-time</option>
                        <option value="contractor">Contractor</option>
                    </select>
                    @if($search || $departmentFilter !== 'all' || $statusFilter !== 'all' || $employmentTypeFilter !== 'all')
                        <button wire:click="clearFilters"
                            class="w-full sm:w-auto px-4 py-2.5 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Clear Filters
                        </button>
                    @endif
                </div>
            </div>
        </div>

        {{-- Bulk Actions (Feature 4: Bulk Operations) --}}
        @if(count($selectedStaff) > 0)
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div class="flex items-center gap-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="font-medium text-blue-900">{{ count($selectedStaff) }} selected</span>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                        <button wire:click="bulkUpdateStatus('active')"
                            class="w-full sm:w-auto px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors">
                            Mark Active
                        </button>
                        <button wire:click="bulkUpdateStatus('on-leave')"
                            class="w-full sm:w-auto px-4 py-2 bg-yellow-600 text-white rounded-lg text-sm hover:bg-yellow-700 transition-colors">
                            Mark On Leave
                        </button>
                        <button wire:click="bulkDelete"
                            wire:confirm="Are you sure you want to delete selected staff members?"
                            class="w-full sm:w-auto px-4 py-2 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 transition-colors">
                            Delete Selected
                        </button>
                    </div>
                </div>
            </div>
        @endif

        {{-- Team Grid (Feature 5: Card-based Staff Display) --}}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            @forelse($staff as $member)
                <div
                    class="bg-white rounded-xl border border-[#EFEFEF] shadow-sm overflow-hidden hover:shadow-lg transition-shadow">
                    {{-- Checkbox for bulk selection (Feature 6) --}}
                    <div class="p-4 border-b border-[#EFEFEF]">
                        <div class="flex items-center justify-between">
                            <input type="checkbox" wire:model.live="selectedStaff" value="{{ $member->id }}"
                                class="rounded border-gray-300 text-[#B48B5B] focus:ring-[#B48B5B]">
                            <div class="flex items-center gap-2">
                                @if($member->status === 'active')
                                    <span
                                        class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                @elseif($member->status === 'on-leave')
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">On
                                        Leave</span>
                                @else
                                    <span
                                        class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Terminated</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{-- Avatar & Info (Feature 7: Professional Profile Cards) --}}
                    <div class="p-6 text-center">
                        <div
                            class="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#C85E78] flex items-center justify-center text-white text-2xl font-bold">
                            @if($member->avatar)
                                <img src="{{ Storage::url($member->avatar) }}" alt="{{ $member->name }}"
                                    class="w-full h-full rounded-full object-cover">
                            @else
                                {{ $member->initials() }}
                            @endif
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">{{ $member->name }}</h3>
                        <p class="text-sm text-gray-600">{{ $member->position }}</p>
                        @if($member->department)
                            <p class="text-xs text-gray-500 mt-1">{{ $member->department }}</p>
                        @endif
                    </div>

                    {{-- Performance Metrics (Feature 8: Performance Tracking) --}}
                    <div class="px-6 py-3 bg-gray-50 border-t border-[#EFEFEF]">
                        <div class="grid grid-cols-3 gap-2 text-center">
                            <div>
                                <p class="text-xs text-gray-500">Rating</p>
                                <p class="text-sm font-semibold text-gray-900">{{ number_format($member->rating, 1) }} ⭐</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">Bookings</p>
                                <p class="text-sm font-semibold text-gray-900">{{ $member->total_appointments }}</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">Revenue</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    ${{ number_format($member->total_revenue, 0) }}</p>
                            </div>
                        </div>
                    </div>

                    {{-- Contact Info (Feature 9: Quick Contact) --}}
                    <div class="px-6 py-3 border-t border-[#EFEFEF]">
                        <div class="flex items-center gap-2 text-sm text-gray-600 mb-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span class="truncate">{{ $member->email }}</span>
                        </div>
                        @if($member->phone)
                            <div class="flex items-center gap-2 text-sm text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span>{{ $member->phone }}</span>
                            </div>
                        @endif
                    </div>

                    {{-- Availability Toggle (Feature 10: Quick Availability Control) --}}
                    <div class="px-6 py-3 border-t border-[#EFEFEF] bg-gray-50">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Available for Booking</span>
                            <button wire:click="toggleAvailability({{ $member->id }})"
                                class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors {{ $member->available_for_booking ? 'bg-green-600' : 'bg-gray-300' }}">
                                <span
                                    class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {{ $member->available_for_booking ? 'translate-x-6' : 'translate-x-1' }}"></span>
                            </button>
                        </div>
                    </div>

                    {{-- Action Buttons (Feature 11: Quick Actions) --}}
                    <div class="px-6 py-4 border-t border-[#EFEFEF] flex gap-2">
                        <button onclick="viewStaff({{ $member->id }})"
                            class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center justify-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            View
                        </button>
                        <button wire:click="editStaff({{ $member->id }})" onclick="showEditModal()"
                            class="px-3 py-2 bg-[#B48B5B] text-white rounded-lg text-sm hover:bg-[#9A7449] transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </button>
                        <button wire:click="deleteStaff({{ $member->id }})"
                            wire:confirm="Are you sure you want to remove {{ $member->name }}?"
                            class="px-3 py-2 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            @empty
                <div class="col-span-full bg-white rounded-xl border border-[#EFEFEF] p-12 text-center">
                    <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No team members found</h3>
                    <p class="text-gray-600 mb-4">Get started by adding your first team member</p>
                    <button onclick="showCreateModal()"
                        class="px-4 py-2 bg-[#B48B5B] hover:bg-[#9A7449] text-white rounded-lg font-medium transition-colors">
                        Add Team Member
                    </button>
                </div>
            @endforelse
        </div>

        {{-- Pagination (Feature 12: Pagination) --}}
        <div class="mt-6">
            {{ $staff->links() }}
        </div>

        {{-- Create/Edit Modal --}}
        <div id="create-modal" class="fixed inset-0 z-50 overflow-y-auto"
            style="display: none; opacity: 0; transition: opacity 0.3s ease-out;">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="hideCreateModal()">
                </div>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full"
                    style="transform: translateY(20px); transition: transform 0.3s ease-out;">
                    <form wire:submit.prevent="createStaff">
                        <div class="bg-white px-6 pt-5 pb-4">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Add Team Member</h3>
                                <button type="button" onclick="hideCreateModal()"
                                    class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                                    <input type="text" wire:model="name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('name') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                    <input type="email" wire:model="email"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('email') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                                    <input type="password" wire:model="password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('password') <span class="text-red-600 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input type="text" wire:model="phone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Position *</label>
                                    <input type="text" wire:model="position" placeholder="e.g., Hair Stylist"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('position') <span class="text-red-600 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                                    <input type="text" wire:model="department" placeholder="e.g., Hair, Nails, Spa"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Employment Type
                                        *</label>
                                    <select wire:model="employment_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                        <option value="full-time">Full-time</option>
                                        <option value="part-time">Part-time</option>
                                        <option value="contractor">Contractor</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                                    <select wire:model="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                        <option value="active">Active</option>
                                        <option value="on-leave">On Leave</option>
                                        <option value="terminated">Terminated</option>
                                    </select>
                                </div>

                                <div class="col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                                    <textarea wire:model="bio" rows="2"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact
                                        Name</label>
                                    <input type="text" wire:model="emergency_contact_name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact
                                        Phone</label>
                                    <input type="text" wire:model="emergency_contact_phone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div class="col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                    <input type="text" wire:model="address"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
                            <button type="button" onclick="hideCreateModal()"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                                Cancel
                            </button>
                            <button type="submit"
                                class="px-4 py-2 bg-[#B48B5B] text-white rounded-lg hover:bg-[#9A7449]">
                                Add Team Member
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {{-- View Modal --}}
        <div id="view-modal" class="fixed inset-0 z-50 overflow-y-auto"
            style="display: none; opacity: 0; transition: opacity 0.3s ease-out;">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="hideViewModal()"></div>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
                    style="transform: translateY(20px); transition: transform 0.3s ease-out;">
                    <div class="bg-white px-6 pt-5 pb-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Staff Details</h3>
                            <button type="button" onclick="hideViewModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div id="staff-details-content"></div>
                    </div>

                    <div class="bg-gray-50 px-6 py-4 flex justify-between">
                        <button type="button" onclick="editStaffFromView()"
                            class="px-4 py-2 bg-[#B48B5B] text-white rounded-lg hover:bg-[#9A7449] transition-colors">
                            Edit Staff
                        </button>
                        <button type="button" onclick="hideViewModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Edit Modal --}}
        <div id="edit-modal" class="fixed inset-0 z-50 overflow-y-auto" wire:ignore.self
            style="display: none; opacity: 0; transition: opacity 0.3s ease-out;">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="hideEditModal()"></div>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full"
                    style="transform: translateY(20px); transition: transform 0.3s ease-out;">
                    <form wire:submit.prevent="updateStaff">
                        <div class="bg-white px-6 pt-5 pb-4">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Edit Team Member</h3>
                                <button type="button" onclick="hideEditModal()"
                                    class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                                    <input type="text" wire:model="name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('name') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                    <input type="email" wire:model="email"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('email') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password <span
                                            class="text-xs text-gray-500">(leave blank to keep current)</span></label>
                                    <input type="password" wire:model="password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('password') <span class="text-red-600 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input type="text" wire:model="phone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Position *</label>
                                    <input type="text" wire:model="position" placeholder="e.g., Hair Stylist"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    @error('position') <span class="text-red-600 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                                    <input type="text" wire:model="department" placeholder="e.g., Hair, Nails, Spa"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Employment Type
                                        *</label>
                                    <select wire:model="employment_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                        <option value="full-time">Full-time</option>
                                        <option value="part-time">Part-time</option>
                                        <option value="contractor">Contractor</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                                    <select wire:model="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="on-leave">On Leave</option>
                                        <option value="terminated">Terminated</option>
                                    </select>
                                </div>

                                <div class="col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                                    <textarea wire:model="bio" rows="2"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact
                                        Name</label>
                                    <input type="text" wire:model="emergency_contact_name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact
                                        Phone</label>
                                    <input type="text" wire:model="emergency_contact_phone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>

                                <div class="col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                    <input type="text" wire:model="address"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
                            <button type="button" onclick="hideEditModal()"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                                Cancel
                            </button>
                            <button type="submit"
                                class="px-4 py-2 bg-[#B48B5B] text-white rounded-lg hover:bg-[#9A7449]">
                                Update Staff
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let staffData = @json($staff->items());
    let currentEditStaffId = null;

    function showCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function showEditModal() {
        const modal = document.getElementById('edit-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideEditModal() {
        const modal = document.getElementById('edit-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function editStaffFromView() {
        if (currentEditStaffId) {
            hideViewModal();
            setTimeout(() => {
                @this.call('editStaff', currentEditStaffId);
                showEditModal();
            }, 300);
        }
    }

    function viewStaff(id) {
        currentEditStaffId = id; // Store for edit from view
        const staff = staffData.find(s => s.id === id);
        if (!staff) return;

        const content = `
            <div class="space-y-4">
                <div class="flex items-center gap-4 pb-4 border-b border-gray-200">
                    <div class="w-20 h-20 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#C85E78] flex items-center justify-center text-white text-2xl font-bold">
                        ${staff.avatar ? `<img src="/storage/${staff.avatar}" alt="${staff.name}" class="w-full h-full rounded-full object-cover">` : staff.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                    </div>
                    <div>
                        <h4 class="text-xl font-bold text-gray-900">${staff.name}</h4>
                        <p class="text-gray-600">${staff.position}</p>
                        ${staff.department ? `<p class="text-sm text-gray-500">${staff.department}</p>` : ''}
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Email</label>
                        <div class="text-base text-gray-900">${staff.email}</div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Phone</label>
                        <div class="text-base text-gray-900">${staff.phone || 'N/A'}</div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Employment Type</label>
                        <div class="text-base text-gray-900 capitalize">${staff.employment_type?.replace('-', ' ')}</div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Status</label>
                        <div class="mt-1">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full
                                ${staff.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                                ${staff.status === 'on-leave' ? 'bg-yellow-100 text-yellow-800' : ''}
                                ${staff.status === 'terminated' ? 'bg-red-100 text-red-800' : ''}">
                                ${staff.status?.replace('-', ' ').toUpperCase()}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <p class="text-sm text-gray-500">Rating</p>
                        <p class="text-lg font-bold text-gray-900">${staff.rating || 0} ⭐</p>
                    </div>
                    <div class="text-center">
                        <p class="text-sm text-gray-500">Appointments</p>
                        <p class="text-lg font-bold text-gray-900">${staff.total_appointments || 0}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-sm text-gray-500">Revenue</p>
                        <p class="text-lg font-bold text-gray-900">$${(staff.total_revenue || 0).toLocaleString()}</p>
                    </div>
                </div>

                ${staff.bio ? `
                    <div>
                        <label class="text-sm font-medium text-gray-500">Bio</label>
                        <div class="text-base text-gray-900 bg-gray-50 p-3 rounded-lg mt-1">
                            ${staff.bio}
                        </div>
                    </div>
                ` : ''}

                <div class="grid grid-cols-2 gap-4">
                    ${staff.hire_date ? `
                        <div>
                            <label class="text-sm font-medium text-gray-500">Hire Date</label>
                            <div class="text-base text-gray-900">${new Date(staff.hire_date).toLocaleDateString()}</div>
                        </div>
                    ` : ''}
                    ${staff.experience_years ? `
                        <div>
                            <label class="text-sm font-medium text-gray-500">Experience</label>
                            <div class="text-base text-gray-900">${staff.experience_years} years</div>
                        </div>
                    ` : ''}
                    ${staff.hourly_rate ? `
                        <div>
                            <label class="text-sm font-medium text-gray-500">Hourly Rate</label>
                            <div class="text-base text-gray-900">$${staff.hourly_rate}</div>
                        </div>
                    ` : ''}
                    ${staff.commission_rate ? `
                        <div>
                            <label class="text-sm font-medium text-gray-500">Commission</label>
                            <div class="text-base text-gray-900">${staff.commission_rate}%</div>
                        </div>
                    ` : ''}
                </div>

                ${staff.emergency_contact_name || staff.emergency_contact_phone ? `
                    <div class="pt-4 border-t border-gray-200">
                        <h5 class="text-sm font-semibold text-gray-700 mb-2">Emergency Contact</h5>
                        <div class="grid grid-cols-2 gap-4">
                            ${staff.emergency_contact_name ? `
                                <div>
                                    <label class="text-sm font-medium text-gray-500">Name</label>
                                    <div class="text-base text-gray-900">${staff.emergency_contact_name}</div>
                                </div>
                            ` : ''}
                            ${staff.emergency_contact_phone ? `
                                <div>
                                    <label class="text-sm font-medium text-gray-500">Phone</label>
                                    <div class="text-base text-gray-900">${staff.emergency_contact_phone}</div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                ` : ''}

                ${staff.address ? `
                    <div>
                        <label class="text-sm font-medium text-gray-500">Address</label>
                        <div class="text-base text-gray-900">${staff.address}</div>
                    </div>
                ` : ''}

                <div>
                    <label class="text-sm font-medium text-gray-500">Available for Booking</label>
                    <div class="text-base font-semibold ${staff.available_for_booking ? 'text-green-600' : 'text-red-600'}">
                        ${staff.available_for_booking ? 'Yes' : 'No'}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('staff-details-content').innerHTML = content;

        const modal = document.getElementById('view-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideViewModal() {
        const modal = document.getElementById('view-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Event listeners
    document.addEventListener('livewire:init', () => {
        Livewire.on('staff-created', () => {
            hideCreateModal();
        });

        Livewire.on('staff-updated', () => {
            hideEditModal();
        });
    });
</script>