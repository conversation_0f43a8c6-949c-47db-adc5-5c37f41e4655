<x-layouts.manager :title="__('Clients')">
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        {{-- <PERSON> Header --}}
        <x-manager.page-header :title="__('Clients')" :subtitle="__('Manage client profiles and track their salon journey')">
            <x-slot:actions>
                <x-manager.button variant="outline" icon="arrow-down-tray" size="md">
                    Export
                </x-manager.button>
                <!-- Add Client Button triggers modal -->
                <x-manager.button variant="primary" icon="plus" size="md" onclick="showModal()">
                    Add Client
                </x-manager.button>
            </x-slot:actions>
        </x-manager.page-header>

        {{-- Statistics Cards --}}
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <x-manager.metric-card 
                icon="users"
                :label="__('Total Clients')"
                :value="$totalClients ?? 0"
                :trend="'+'.($newThisMonth ?? 0).' this month'"
                :trendUp="true"
                iconColor="rose"
            />
            <x-manager.metric-card 
                icon="user-plus"
                :label="__('New This Month')"
                :value="$newThisMonth ?? 0"
                iconColor="green"
            />
            <x-manager.metric-card 
                icon="currency-dollar"
                :label="__('Avg. Lifetime Value')"
                :value="number_format($avgLifetimeValue ?? 0, 0, '.', ',')"
                iconColor="beige"
            />
            <!-- Following project specification: Removed VIP clients metric -->
        </div>

        {{-- Search and Filters --}}
        <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <flux:icon icon="magnifying-glass" class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]" />
                        <input 
                            type="text" 
                            placeholder="Search by name, email, or phone..."
                            class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                        />
                    </div>
                </div>
                <div class="flex gap-3">
                    <select class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                        <option>Sort by: Recent</option>
                        <option>Name (A-Z)</option>
                        <option>Name (Z-A)</option>
                        <option>Visit Count</option>
                    </select>
                    <x-manager.button variant="primary" icon="funnel" size="md">
                        Filter
                    </x-manager.button>
                </div>
            </div>
        </div>

        {{-- Clients Grid --}}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($clients ?? [] as $client)
            <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm hover:shadow-md hover:border-[#E98CA5]/30 transition-all">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-bold text-lg">
                            {{ strtoupper(substr($client->name, 0, 2)) }}
                        </div>
                        <div>
                            <h3 class="font-body font-bold text-[#2C2C34]">{{ $client->name }}</h3>
                            <p class="font-body text-xs text-[#8B5D66]">
                                @if($client->is_active)
                                    <span class="text-green-600">● Active</span>
                                @else
                                    <span class="text-red-600">● Inactive</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    <div class="relative">
                        <button onclick="toggleMenu({{ $client->id }})" class="p-1.5 text-[#8B5D66] hover:bg-[#F7E9E6] rounded-lg transition-colors">
                            <flux:icon icon="ellipsis-vertical" class="w-5 h-5" />
                        </button>
                        <div id="menu-{{ $client->id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#EFEFEF] z-10">
                            <form method="POST" action="{{ route('manager.clients.toggle-active', $client->id) }}" class="block">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-[#4A4A52] hover:bg-[#F7E9E6] flex items-center gap-2">
                                    <flux:icon icon="{{ $client->is_active ? 'x-circle' : 'check-circle' }}" class="w-4 h-4" />
                                    {{ $client->is_active ? 'Deactivate' : 'Activate' }}
                                </button>
                            </form>
                            <button type="button" onclick="showDeleteConfirm({{ $client->id }}, '{{ addslashes($client->name) }}')" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2">
                                <flux:icon icon="trash" class="w-4 h-4" />
                                Delete
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-3 mb-4">
                    <div class="flex items-center gap-2 text-sm">
                        <flux:icon icon="envelope" class="w-4 h-4 text-[#8B5D66]" />
                        <span class="font-body text-[#4A4A52]">{{ $client->email }}</span>
                    </div>
                    @if($client->phone)
                    <div class="flex items-center gap-2 text-sm">
                        <flux:icon icon="phone" class="w-4 h-4 text-[#8B5D66]" />
                        <span class="font-body text-[#4A4A52]">{{ $client->phone }}</span>
                    </div>
                    @endif
                    <div class="flex items-center gap-2 text-sm">
                        <flux:icon icon="calendar" class="w-4 h-4 text-[#8B5D66]" />
                        <span class="font-body text-[#4A4A52]">Last visit: {{ $client->updated_at->diffForHumans() }}</span>
                    </div>
                </div>

                <div class="flex items-center justify-center py-3 px-4 bg-[#F7E9E6] rounded-lg mb-4">
                    <div class="text-center">
                        <div class="font-body text-xs text-[#8B5D66] mb-0.5">Total Visits</div>
                        <div class="font-body text-lg font-bold text-[#2C2C34]">{{ $client->total_visits }}</div>
                    </div>
                </div>

                <div class="flex gap-2">
                    <x-manager.button variant="outline" size="sm" class="flex-1" onclick="showEditModal({{ $client->id }}, '{{ addslashes($client->name) }}', '{{ $client->email }}', '{{ $client->phone ?? '' }}')">
                        <flux:icon icon="eye" class="w-4 h-4" />
                        View
                    </x-manager.button>
                    <x-manager.button variant="primary" size="sm" class="flex-1">
                        <flux:icon icon="calendar" class="w-4 h-4" />
                        Book
                    </x-manager.button>
                </div>
            </div>
            @empty
            <div class="col-span-3 text-center py-12">
                <flux:icon icon="users" class="w-16 h-16 mx-auto text-[#8B5D66] mb-4" />
                <h3 class="font-body text-xl font-bold text-[#2C2C34] mb-2">No clients found</h3>
                <p class="font-body text-[#8B5D66] mb-6">Get started by adding your first client</p>
                <x-manager.button variant="primary" icon="plus" size="md">
                    Add Client
                </x-manager.button>
            </div>
            @endforelse
        </div>

        {{-- Pagination --}}
        @if(isset($clients) && $clients->hasPages())
        <div class="mt-8 flex justify-center">
            {{ $clients->links() }}
        </div>
        @endif
    </div>
</x-layouts.manager>

{{-- Add Client Modal --}}
<div id="add-client-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <form method="POST" action="{{ route('manager.clients.store') }}">
                @csrf
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-[#E98CA5]/10 sm:mx-0 sm:h-10 sm:w-10">
                            <flux:icon icon="user-plus" class="h-6 w-6 text-[#E98CA5]" />
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-[#2C2C34]" id="modal-title">
                                Add New Client
                            </h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-[#4A4A52]">Full Name</label>
                                    <div class="mt-1">
                                        <input type="text" name="name" id="name" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-[#4A4A52]">Email Address</label>
                                    <div class="mt-1">
                                        <input type="email" name="email" id="email" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-[#4A4A52]">Phone Number</label>
                                    <div class="mt-1">
                                        <input type="text" name="phone" id="phone"
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="password" class="block text-sm font-medium text-[#4A4A52]">Password</label>
                                    <div class="mt-1 relative">
                                        <input type="password" name="password" id="password" required
                                            class="py-2.5 px-4 pr-12 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                        <button type="button" onclick="togglePassword()" 
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-[#8B5D66] hover:text-[#E98CA5]">
                                            <svg id="eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <svg id="eye-slash-icon" class="h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-1 text-xs text-[#8B5D66]">Client will use this password to log in (min. 8 characters)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:ml-3 sm:w-auto sm:text-sm">
                        Add Client
                    </button>
                    <button type="button" onclick="hideModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Edit Client Modal --}}
<div id="edit-client-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideEditModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <form method="POST" id="edit-client-form">
                @csrf
                @method('PUT')
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-[#E98CA5]/10 sm:mx-0 sm:h-10 sm:w-10">
                            <flux:icon icon="user" class="h-6 w-6 text-[#E98CA5]" />
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-[#2C2C34]" id="edit-modal-title">
                                Edit Client Details
                            </h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label for="edit-name" class="block text-sm font-medium text-[#4A4A52]">Full Name</label>
                                    <div class="mt-1">
                                        <input type="text" name="name" id="edit-name" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-email" class="block text-sm font-medium text-[#4A4A52]">Email Address</label>
                                    <div class="mt-1">
                                        <input type="email" name="email" id="edit-email" required
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-phone" class="block text-sm font-medium text-[#4A4A52]">Phone Number</label>
                                    <div class="mt-1">
                                        <input type="text" name="phone" id="edit-phone"
                                            class="py-2.5 px-4 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-password" class="block text-sm font-medium text-[#4A4A52]">New Password</label>
                                    <div class="mt-1 relative">
                                        <input type="password" name="password" id="edit-password"
                                            class="py-2.5 px-4 pr-12 block w-full shadow-sm focus:ring-[#E98CA5] focus:border-[#E98CA5] border-[#EFEFEF] rounded-lg">
                                        <button type="button" onclick="toggleEditPassword()" 
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-[#8B5D66] hover:text-[#E98CA5]">
                                            <svg id="edit-eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <svg id="edit-eye-slash-icon" class="h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-1 text-xs text-[#8B5D66]">Leave blank to keep current password (min. 8 characters if changing)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:ml-3 sm:w-auto sm:text-sm">
                        Save Changes
                    </button>
                    <button type="button" onclick="hideEditModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Delete Confirmation Modal --}}
<div id="delete-confirm-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideDeleteConfirm()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-[#2C2C34]">
                            Delete Client
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-[#8B5D66]">
                                Are you sure you want to delete <span id="delete-client-name" class="font-semibold text-[#2C2C34]"></span>? This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form method="POST" id="delete-client-form" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit"
                        class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete
                    </button>
                </form>
                <button type="button" onclick="hideDeleteConfirm()"
                    class="mt-3 w-full inline-flex justify-center rounded-lg border border-[#EFEFEF] shadow-sm px-4 py-2.5 bg-white text-base font-medium text-[#4A4A52] hover:bg-[#F7E9E6] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

{{-- Success Notification Modal --}}
<div id="success-modal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none; opacity: 0; transition: opacity 0.3s ease;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" onclick="hideSuccessModal()">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full" style="transform: translateY(20px); transition: transform 0.3s ease;">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-[#2C2C34]">
                            Success
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-[#8B5D66]" id="success-message">
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="hideSuccessModal()"
                    class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-base font-medium text-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E98CA5] sm:w-auto sm:text-sm">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show delete confirmation modal
    function showDeleteConfirm(clientId, clientName) {
        const modal = document.getElementById('delete-confirm-modal');
        const form = document.getElementById('delete-client-form');
        const nameSpan = document.getElementById('delete-client-name');
        
        // Set form action and client name
        form.action = '/manager/clients/' + clientId;
        nameSpan.textContent = clientName;
        
        // Show modal
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    // Hide delete confirmation modal
    function hideDeleteConfirm() {
        const modal = document.getElementById('delete-confirm-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Show success notification modal
    function showSuccessModal(message) {
        const modal = document.getElementById('success-modal');
        const messageEl = document.getElementById('success-message');
        
        messageEl.textContent = message;
        
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            hideSuccessModal();
        }, 3000);
    }

    // Hide success notification modal
    function hideSuccessModal() {
        const modal = document.getElementById('success-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Toggle dropdown menu
    function toggleMenu(clientId) {
        const menu = document.getElementById('menu-' + clientId);
        // Close all other menus
        document.querySelectorAll('[id^="menu-"]').forEach(m => {
            if (m.id !== 'menu-' + clientId) {
                m.classList.add('hidden');
            }
        });
        menu.classList.toggle('hidden');
    }

    // Close menus when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick^="toggleMenu"]') && !event.target.closest('[id^="menu-"]')) {
            document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Show edit modal
    function showEditModal(id, name, email, phone) {
        const modal = document.getElementById('edit-client-modal');
        const form = document.getElementById('edit-client-form');
        
        // Set form action
        form.action = '/manager/clients/' + id;
        
        // Populate form fields
        document.getElementById('edit-name').value = name;
        document.getElementById('edit-email').value = email;
        document.getElementById('edit-phone').value = phone;
        
        // Show modal
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    // Hide edit modal
    function hideEditModal() {
        const modal = document.getElementById('edit-client-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eye-icon');
        const eyeSlashIcon = document.getElementById('eye-slash-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.add('hidden');
            eyeSlashIcon.classList.remove('hidden');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('hidden');
            eyeSlashIcon.classList.add('hidden');
        }
    }

    // Toggle edit password visibility
    function toggleEditPassword() {
        const passwordInput = document.getElementById('edit-password');
        const eyeIcon = document.getElementById('edit-eye-icon');
        const eyeSlashIcon = document.getElementById('edit-eye-slash-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.add('hidden');
            eyeSlashIcon.classList.remove('hidden');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('hidden');
            eyeSlashIcon.classList.add('hidden');
        }
    }
    
    // Simple modal show/hide functions
    function showModal() {
        const modal = document.getElementById('add-client-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }
    
    function hideModal() {
        const modal = document.getElementById('add-client-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
            // Reset password visibility when modal closes
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            const eyeSlashIcon = document.getElementById('eye-slash-icon');
            if (passwordInput && passwordInput.type === 'text') {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('hidden');
                eyeSlashIcon.classList.add('hidden');
            }
        }, 300);
    }
    
    // Close modal when clicking outside
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('add-client-modal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideModal();
                }
            });
        }
        
        // Show success modal if there's a success message
        @if(session('success'))
            showSuccessModal('{{ session('success') }}');
        @endif
    });
</script>
