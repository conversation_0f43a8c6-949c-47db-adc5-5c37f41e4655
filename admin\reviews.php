<?php
/**
 * Patient Testimonials Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/reviews_functions.php';

// Check medical admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    header('Location: ' . getBasePath() . '/auth/login.php');
    exit;
}

// Handle testimonial actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $reviewId = sanitize($_POST['review_id'] ?? '');

        if (empty($reviewId)) {
            throw new Exception("Invalid testimonial ID");
        }

        switch ($_POST['action']) {
            case 'approve':
                $result = $database->execute("UPDATE reviews SET status = 'verified', is_verified = 1 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Patient testimonial approved successfully.";
                } else {
                    throw new Exception("Failed to approve testimonial");
                }
                break;

            case 'reject':
                $result = $database->execute("UPDATE reviews SET status = 'rejected', is_verified = 0 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Patient testimonial rejected successfully.";
                } else {
                    throw new Exception("Failed to reject testimonial");
                }
                break;

            case 'feature':
                $result = $database->execute("UPDATE reviews SET is_featured = 1 WHERE id = ? AND status = 'verified'", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Testimonial marked as featured.";
                } else {
                    throw new Exception("Failed to feature testimonial. Make sure the testimonial is verified first.");
                }
                break;

            case 'unfeature':
                $result = $database->execute("UPDATE reviews SET is_featured = 0 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Testimonial removed from featured.";
                } else {
                    throw new Exception("Failed to unfeature testimonial");
                }
                break;

            case 'delete':
                $result = $database->execute("DELETE FROM reviews WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Patient testimonial deleted successfully.";
                } else {
                    throw new Exception("Failed to delete testimonial");
                }
                break;

            default:
                throw new Exception("Invalid action");
        }

        // Redirect to prevent form resubmission
        $redirectUrl = getBasePath() . '/admin/reviews';
        if (!empty($_GET)) {
            $redirectUrl .= '?' . http_build_query($_GET);
        }
        header('Location: ' . $redirectUrl);
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = "Error: " . $e->getMessage();
        // Redirect even on error
        $redirectUrl = getBasePath() . '/admin/reviews';
        if (!empty($_GET)) {
            $redirectUrl .= '?' . http_build_query($_GET);
        }
        header('Location: ' . $redirectUrl);
        exit;
    }
}

// Get filter parameters
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');
$rating = sanitize($_GET['rating'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;

// Build query conditions
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(r.title LIKE ? OR r.comment LIKE ? OR u.name LIKE ? OR u.email LIKE ? OR s.name LIKE ? OR p.name LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($status && $status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

if ($rating) {
    $whereConditions[] = "r.rating = ?";
    $params[] = $rating;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get patient testimonials
$offset = ($page - 1) * $limit;
$sql = "
    SELECT 
        r.*,
        u.name as patient_name,
        u.email as patient_email,
        s.name as treatment_name,
        p.name as package_name
    FROM reviews r
    JOIN users u ON r.customer_id = u.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN packages p ON r.package_id = p.id
    {$whereClause}
    ORDER BY r.created_at DESC
    LIMIT ? OFFSET ?
";

$allParams = array_merge($params, [$limit, $offset]);
$testimonials = $database->fetchAll($sql, $allParams);

// Get total count for pagination
$countSql = "
    SELECT COUNT(*) as total
    FROM reviews r
    JOIN users u ON r.customer_id = u.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN packages p ON r.package_id = p.id
    {$whereClause}
";
$totalResult = $database->fetch($countSql, $params);
$totalTestimonials = $totalResult['total'];
$totalPages = ceil($totalTestimonials / $limit);

// Get statistics
$stats = getReviewStats();

$pageTitle = "Patient Testimonials Management";
include __DIR__ . '/../includes/admin_header.php';
?>

<!-- Medical Testimonials Management CSS -->
<style>
/* Medical Testimonials Specific Styles */
.medical-testimonial-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-testimonial-card:hover::before {
    left: 100%;
}

.medical-testimonial-card:hover {
    transform: translateY(-6px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.medical-stats-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-filter-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-header-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.patient-avatar {
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
}

.medical-status-verified {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.medical-status-pending {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
    color: #d97706;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.medical-status-rejected {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medical-rating-stars {
    color: #f59e0b;
    filter: drop-shadow(0 1px 2px rgba(245, 158, 11, 0.3));
}

.medical-rating-empty {
    color: #d1d5db;
}

.medical-btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.medical-btn-primary:hover::before {
    left: 100%;
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-btn-secondary {
    background: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.medical-btn-approve {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-approve:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.medical-btn-reject {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-reject:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-btn-feature {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-feature:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.medical-btn-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-pagination {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.testimonial-content {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.02), rgba(88, 148, 210, 0.02));
    border-left: 4px solid var(--primary-green);
    padding: 1rem;
    border-radius: 0 12px 12px 0;
    margin: 1rem 0;
}

.featured-badge {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05));
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.verified-badge {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

@media (max-width: 768px) {
    .medical-testimonial-card {
        border-radius: 16px;
        padding: 1rem;
    }
    
    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                
                    <!-- Medical Header -->
                    <div class="medical-header-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Patient Testimonials
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Manage patient testimonials and treatment feedback</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $totalTestimonials ?> Total Testimonials
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                        Patient Feedback
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <button onclick="exportTestimonials()" class="medical-btn-secondary inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export Testimonials
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Success/Error Messages -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="mb-8 p-4 rounded-xl border-2 bg-green-50 border-green-200 text-green-800">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?= htmlspecialchars($_SESSION['success']) ?>
                            </div>
                        </div>
                        <?php unset($_SESSION['success']); ?>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="mb-8 p-4 rounded-xl border-2 bg-red-50 border-red-200 text-red-800">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?= htmlspecialchars($_SESSION['error']) ?>
                            </div>
                        </div>
                        <?php unset($_SESSION['error']); ?>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-blue/20 to-blue-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Testimonials</dt>
                                        <dd class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total_reviews'] ?? 0) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Average Rating</dt>
                                        <dd class="text-2xl font-bold text-yellow-600"><?= $stats['average_rating'] ?? '0.0' ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Verified Testimonials</dt>
                                        <dd class="text-2xl font-bold text-green-600"><?= number_format($stats['verified_reviews'] ?? 0) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-400/20 to-violet-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Recommendation Rate</dt>
                                        <dd class="text-2xl font-bold text-purple-600"><?= $stats['recommendation_rate'] ?? 0 ?>%</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-6 mb-8">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <input type="text" name="search" value="<?= htmlspecialchars($search ?? '') ?>"
                                           placeholder="Search patient testimonials..."
                                           class="w-full pl-10 pr-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                            </div>
                            <div>
                                <select name="status" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="">All Status</option>
                                    <option value="pending" <?= ($status ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="verified" <?= ($status ?? '') === 'verified' ? 'selected' : '' ?>>Verified</option>
                                    <option value="rejected" <?= ($status ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                </select>
                            </div>
                            <div>
                                <select name="rating" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="">All Ratings</option>
                                    <?php for ($i = 5; $i >= 1; $i--): ?>
                                        <option value="<?= $i ?>" <?= ($rating ?? '') == $i ? 'selected' : '' ?>><?= $i ?> Stars</option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <button type="submit" class="medical-btn-primary">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter
                            </button>
                            <?php if (($search ?? '') || ($status ?? '') || ($rating ?? '')): ?>
                                <a href="<?= getBasePath() ?>/admin/reviews" class="medical-btn-secondary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Clear
                                </a>
                            <?php endif; ?>
                        </form>
                    </div>

                    <!-- Medical Testimonials Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                        <?php if (empty($testimonials)): ?>
                            <div class="col-span-full medical-testimonial-card p-12 text-center">
                                <div class="text-gray-400">
                                    <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    <p class="text-xl font-semibold text-redolence-navy">No testimonials found</p>
                                    <p class="text-gray-500">No patient testimonials match your current criteria.</p>
                                </div>
                            </div>
                        <?php else: ?>
                            <?php foreach ($testimonials as $testimonial): ?>
                                <div class="medical-testimonial-card p-6">
                                    <!-- Patient Info -->
                                    <div class="flex items-center mb-4">
                                        <div class="patient-avatar w-12 h-12">
                                            <?= strtoupper(substr($testimonial['patient_name'] ?? 'U', 0, 1)) ?>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="text-sm font-semibold text-redolence-navy"><?= htmlspecialchars($testimonial['patient_name'] ?? 'Unknown') ?></div>
                                            <div class="text-xs text-gray-500"><?= htmlspecialchars($testimonial['patient_email'] ?? '') ?></div>
                                        </div>
                                        <div class="ml-auto">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?php
                                                switch ($testimonial['status'] ?? 'pending') {
                                                    case 'verified':
                                                        echo 'medical-status-verified';
                                                        break;
                                                    case 'pending':
                                                        echo 'medical-status-pending';
                                                        break;
                                                    case 'rejected':
                                                        echo 'medical-status-rejected';
                                                        break;
                                                    default:
                                                        echo 'medical-status-pending';
                                                }
                                                ?>">
                                                <?= ucfirst($testimonial['status'] ?? 'pending') ?>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Treatment/Package Info -->
                                    <div class="mb-4">
                                        <div class="text-sm text-redolence-navy font-semibold">
                                            <?= htmlspecialchars($testimonial['treatment_name'] ?? $testimonial['package_name'] ?? 'N/A') ?>
                                        </div>
                                        <div class="text-xs text-gray-500 flex items-center mt-1">
                                            <?= ($testimonial['treatment_name'] ?? false) ? 'Medical Treatment' : 'Treatment Package' ?>
                                            <?php if ($testimonial['is_verified'] ?? false): ?>
                                                <span class="verified-badge px-2 py-1 rounded-full text-xs font-semibold ml-2">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    Verified
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($testimonial['is_featured'] ?? false): ?>
                                                <span class="featured-badge px-2 py-1 rounded-full text-xs font-semibold ml-2">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                    </svg>
                                                    Featured
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    <div class="flex items-center mb-4">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <svg class="w-4 h-4 <?= $i <= ($testimonial['rating'] ?? 0) ? 'medical-rating-stars' : 'medical-rating-empty' ?>" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        <?php endfor; ?>
                                        <span class="ml-2 text-sm text-gray-600 font-medium"><?= $testimonial['rating'] ?? 0 ?>/5</span>
                                        <span class="ml-auto text-xs text-gray-400"><?= date('M j, Y', strtotime($testimonial['created_at'] ?? 'now')) ?></span>
                                    </div>

                                    <!-- Testimonial Content -->
                                    <div class="testimonial-content">
                                        <h4 class="text-sm font-semibold text-redolence-navy mb-2"><?= htmlspecialchars($testimonial['title'] ?? 'No title') ?></h4>
                                        <p class="text-sm text-gray-700 line-clamp-4">
                                            <?= htmlspecialchars($testimonial['comment'] ?? 'No comment') ?>
                                        </p>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex gap-2 mt-4">
                                        <?php if (($testimonial['status'] ?? 'pending') === 'pending'): ?>
                                            <form method="POST" class="flex-1">
                                                <input type="hidden" name="action" value="approve">
                                                <input type="hidden" name="review_id" value="<?= $testimonial['id'] ?? '' ?>">
                                                <button type="submit" class="w-full medical-btn-approve text-sm">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    Approve
                                                </button>
                                            </form>
                                            <form method="POST" class="flex-1">
                                                <input type="hidden" name="action" value="reject">
                                                <input type="hidden" name="review_id" value="<?= $testimonial['id'] ?? '' ?>">
                                                <button type="submit" class="w-full medical-btn-reject text-sm">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                    </svg>
                                                    Reject
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if (($testimonial['status'] ?? '') === 'verified'): ?>
                                            <?php if (!($testimonial['is_featured'] ?? false)): ?>
                                                <form method="POST" class="flex-1">
                                                    <input type="hidden" name="action" value="feature">
                                                    <input type="hidden" name="review_id" value="<?= $testimonial['id'] ?? '' ?>">
                                                    <button type="submit" class="w-full medical-btn-feature text-sm">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                        </svg>
                                                        Feature
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" class="flex-1">
                                                    <input type="hidden" name="action" value="unfeature">
                                                    <input type="hidden" name="review_id" value="<?= $testimonial['id'] ?? '' ?>">
                                                    <button type="submit" class="w-full medical-btn-secondary text-sm">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                        </svg>
                                                        Unfeature
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <form method="POST" onsubmit="return confirm('Are you sure you want to delete this patient testimonial?')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="review_id" value="<?= $testimonial['id'] ?? '' ?>">
                                            <button type="submit" class="medical-btn-delete">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Medical Pagination -->
                    <?php if (($totalPages ?? 1) > 1): ?>
                        <div class="medical-pagination px-6 py-4 flex items-center justify-between">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if (($page ?? 1) > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => ($page ?? 1) - 1])) ?>"
                                       class="medical-btn-secondary">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if (($page ?? 1) < ($totalPages ?? 1)): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => ($page ?? 1) + 1])) ?>"
                                       class="medical-btn-secondary">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">
                                        Showing <span class="font-semibold text-redolence-green"><?= (($page ?? 1) - 1) * ($limit ?? 10) + 1 ?></span> to 
                                        <span class="font-semibold text-redolence-green"><?= min(($page ?? 1) * ($limit ?? 10), ($totalTestimonials ?? 0)) ?></span> of 
                                        <span class="font-semibold text-redolence-green"><?= $totalTestimonials ?? 0 ?></span> patient testimonials
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
                                        <?php for ($i = 1; $i <= ($totalPages ?? 1); $i++): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                               class="relative inline-flex items-center px-4 py-2 border text-sm font-semibold transition-all <?= $i === ($page ?? 1) ? 'z-10 bg-redolence-green border-redolence-green text-white' : 'bg-white border-gray-300 text-gray-700 hover:bg-redolence-green/10 hover:border-redolence-green hover:text-redolence-green' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function exportTestimonials() {
    // Implementation for exporting testimonials
    alert('Export functionality will be implemented in the next phase.');
}
</script>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>