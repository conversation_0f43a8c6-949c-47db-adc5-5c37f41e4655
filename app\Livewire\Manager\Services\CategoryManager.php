<?php

namespace App\Livewire\Manager\Services;

use App\Models\ServiceCategory;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Illuminate\Support\Str;

#[Layout('components.layouts.manager')]
class CategoryManager extends Component
{
    use WithFileUploads, WithPagination;

    public $search = '';
    public $showModal = false;
    public $confirmingDeletion = false;
    public $categoryToDelete = null;

    // Form fields
    public $categoryId;
    public $name;
    public $description;
    public $image;
    public $oldImage; // To preview existing image in edit
    public $isActive = true;
    public $showImageSettings = false;

    protected $rules = [
        'name' => 'required|min:3|max:255',
        'description' => 'nullable|max:1000',
        'image' => 'nullable|image|max:2048', // 2MB max
        'isActive' => 'boolean',
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function edit(ServiceCategory $category)
    {
        $this->resetForm();
        $this->categoryId = $category->id;
        $this->name = $category->name;
        $this->description = $category->description;
        $this->oldImage = $category->image;
        $this->isActive = $category->is_active;
        $this->showModal = true;
    }

    public function store()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'description' => $this->description,
            'is_active' => $this->isActive,
        ];

        if ($this->image) {
            $path = $this->image->store('categories', 'public');
            $data['image'] = $path;
        }

        if ($this->categoryId) {
            // Update
            $category = ServiceCategory::findOrFail($this->categoryId);

            // Delete old image if new one uploaded
            if ($this->image && $category->image) {
                // Storage::disk('public')->delete($category->image); // Uncomment if you want to delete old files
            }

            $category->update($data);
            session()->flash('message', 'Category updated successfully.');
        } else {
            // Create
            ServiceCategory::create($data);
            session()->flash('message', 'Category created successfully.');
        }

        $this->showModal = false;
        $this->resetForm();
    }

    public function confirmDeletion($id)
    {
        $this->confirmingDeletion = true;
        $this->categoryToDelete = $id;
    }

    public function delete()
    {
        $category = ServiceCategory::findOrFail($this->categoryToDelete);
        $category->delete();

        $this->confirmingDeletion = false;
        $this->categoryToDelete = null;
        session()->flash('message', 'Category deleted successfully.');
    }

    public function toggleStatus(ServiceCategory $category)
    {
        $category->update(['is_active' => !$category->is_active]);
        $status = $category->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Category {$status} successfully.");
    }

    public function toggleImageSettings()
    {
        $this->showImageSettings = !$this->showImageSettings;
    }

    private function resetForm()
    {
        $this->categoryId = null;
        $this->name = '';
        $this->description = '';
        $this->image = null;
        $this->oldImage = null;
        $this->isActive = true;
        $this->showImageSettings = false;
        $this->resetErrorBag();
    }

    public function render()
    {
        $categories = ServiceCategory::where('name', 'like', '%' . $this->search . '%')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.manager.services.category-manager', [
            'categories' => $categories
        ]);
    }
}
