<x-layouts.manager :title="__('Appointments')">
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        {{-- <PERSON> Header --}}
        <x-manager.page-header :title="__('Appointments')" :subtitle="__('Manage and track all salon appointments')">
            <x-slot:actions>
                <x-manager.button variant="outline" icon="calendar" size="md">
                    Calendar View
                </x-manager.button>
                <x-manager.button variant="primary" icon="plus" size="md">
                    New Appointment
                </x-manager.button>
            </x-slot:actions>
        </x-manager.page-header>

        {{-- Statistics Cards --}}
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <x-manager.metric-card 
                icon="calendar"
                :label="__('Total Appointments')"
                :value="$totalAppointments"
                iconColor="rose"
            />
            <x-manager.metric-card 
                icon="clock"
                :label="__('Pending')"
                :value="$pendingAppointments"
                iconColor="beige"
            />
            <x-manager.metric-card 
                icon="check-circle"
                :label="__('Confirmed')"
                :value="$confirmedAppointments"
                iconColor="green"
            />
            <x-manager.metric-card 
                icon="calendar-days"
                :label="__('Today')"
                :value="$todayAppointments"
                :trend="($todayTrend > 0 ? '+' : '') . $todayTrend . ' from yesterday'"
                :trendUp="$todayTrendUp"
                iconColor="rose"
            />
        </div>

        {{-- Filters --}}
        <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <form method="GET" action="{{ route('manager.appointments.index') }}">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <flux:icon icon="magnifying-glass" class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]" />
                            <input 
                                type="text" 
                                name="search"
                                value="{{ request('search') }}"
                                placeholder="Search by client name, service, or staff..."
                                class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                            />
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <select name="status" class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                            <option value="all" {{ request('status') == 'all' || !request('status') ? 'selected' : '' }}>All Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        <input 
                            type="date"
                            name="date"
                            value="{{ request('date') }}"
                            class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all"
                        />
                        <x-manager.button type="submit" variant="primary" icon="funnel" size="md">
                            Filter
                        </x-manager.button>
                    </div>
                </div>
            </form>
        </div>

        {{-- Appointments Table --}}
        <div class="bg-white rounded-xl border border-[#EFEFEF] shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-[#EFEFEF]">
                    <thead class="bg-[#F7E9E6]/30">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Client</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Staff</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Date & Time</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-[#EFEFEF]">
                        @forelse($appointments as $appointment)
                        <tr class="hover:bg-[#F7E9E6]/20 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold text-sm">
                                        {{ $appointment->client->initials() }}
                                    </div>
                                    <div>
                                        <div class="font-body font-semibold text-[#2C2C34] text-sm">{{ $appointment->client->name }}</div>
                                        <div class="font-body text-xs text-[#8B5D66]">{{ $appointment->client->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34] font-medium">{{ $appointment->service->name }}</div>
                                <div class="font-body text-xs text-[#8B5D66]">{{ $appointment->service->category ?? 'Service' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34]">{{ $appointment->staff?->name ?? 'Unassigned' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34] font-medium">
                                    @if($appointment->appointment_date->isToday())
                                        Today, {{ $appointment->start_time->format('g:i A') }}
                                    @elseif($appointment->appointment_date->isTomorrow())
                                        Tomorrow, {{ $appointment->start_time->format('g:i A') }}
                                    @else
                                        {{ $appointment->appointment_date->format('M j, Y') }}, {{ $appointment->start_time->format('g:i A') }}
                                    @endif
                                </div>
                                <div class="font-body text-xs text-[#8B5D66]">{{ $appointment->appointment_date->format('M j, Y') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-body text-sm text-[#2C2C34]">{{ $appointment->duration }} min</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <x-manager.status-badge :status="$appointment->status" />
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center gap-2">
                                    <button class="p-1.5 text-[#E98CA5] hover:bg-[#F7E9E6] rounded-lg transition-colors" title="View">
                                        <flux:icon icon="eye" class="w-4 h-4" />
                                    </button>
                                    <button class="p-1.5 text-[#4A4A52] hover:bg-[#F7E9E6] rounded-lg transition-colors" title="Edit">
                                        <flux:icon icon="pencil" class="w-4 h-4" />
                                    </button>
                                    <form method="POST" action="{{ route('manager.appointments.destroy', $appointment->id) }}" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="p-1.5 text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Cancel">
                                            <flux:icon icon="x-mark" class="w-4 h-4" />
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <flux:icon icon="calendar" class="w-12 h-12 text-[#8B5D66]/30 mb-3" />
                                    <p class="text-[#2C2C34] font-semibold mb-1">No appointments found</p>
                                    <p class="text-[#8B5D66] text-sm">Try adjusting your filters or create a new appointment</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            <div class="px-6 py-4 border-t border-[#EFEFEF] flex items-center justify-between">
                <div class="text-sm text-[#8B5D66]">
                    Showing <span class="font-semibold text-[#2C2C34]">{{ $appointments->firstItem() ?? 0 }}</span> to <span class="font-semibold text-[#2C2C34]">{{ $appointments->lastItem() ?? 0 }}</span> of <span class="font-semibold text-[#2C2C34]">{{ $appointments->total() }}</span> appointments
                </div>
                <div class="flex gap-2">
                    {{ $appointments->links('pagination::simple-tailwind') }}
                </div>
            </div>
        </div>
    </div>
</x-layouts.manager>
