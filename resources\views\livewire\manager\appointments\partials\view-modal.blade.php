{{-- View Appointment Modal --}}
<div 
    x-data="{ show: @entangle('showViewModal') }"
    x-show="show"
    x-cloak
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title" 
    role="dialog" 
    aria-modal="true">
    
    {{-- Backdrop --}}
    <div 
        x-show="show"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="$wire.closeModals()">
    </div>

    {{-- Modal Panel --}}
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div 
            x-show="show"
            x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl">
            
            @if($selectedAppointment)
            {{-- Header --}}
            <div class="bg-gradient-to-r from-[#8B5D66] to-[#A87584] px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <flux:icon icon="eye" class="w-6 h-6 text-white" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Appointment Details</h3>
                            <p class="text-sm text-white/80">Reference #{{ $selectedAppointment->id }}</p>
                        </div>
                    </div>
                    <button 
                        wire:click="closeModals"
                        class="text-white/80 hover:text-white transition-colors">
                        <flux:icon icon="x-mark" class="w-6 h-6" />
                    </button>
                </div>
            </div>

            {{-- Content --}}
            <div class="px-6 py-6">
                {{-- Status Badge --}}
                <div class="mb-6 flex items-center justify-between">
                    <x-manager.status-badge :status="$selectedAppointment->status" />
                    <div class="text-sm text-[#8B5D66]">
                        Created {{ $selectedAppointment->created_at->diffForHumans() }}
                    </div>
                </div>

                {{-- Client Information --}}
                <div class="bg-[#F7E9E6]/30 rounded-xl p-6 mb-6">
                    <h4 class="text-sm font-semibold text-[#2C2C34] uppercase tracking-wider mb-4 flex items-center gap-2">
                        <flux:icon icon="user" class="w-4 h-4 text-[#E98CA5]" />
                        Client Information
                    </h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-12 h-12 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold">
                                    {{ $selectedAppointment->client->initials() }}
                                </div>
                                <div>
                                    <div class="font-semibold text-[#2C2C34]">{{ $selectedAppointment->client->name }}</div>
                                    <div class="text-sm text-[#8B5D66]">{{ $selectedAppointment->client->email }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            @if($selectedAppointment->client->phone)
                            <div class="flex items-center gap-2 text-sm">
                                <flux:icon icon="phone" class="w-4 h-4 text-[#8B5D66]" />
                                <span class="text-[#2C2C34]">{{ $selectedAppointment->client->phone }}</span>
                            </div>
                            @endif
                            @if($selectedAppointment->client->is_vip)
                            <div class="inline-flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">
                                <flux:icon icon="star" class="w-3 h-3" />
                                VIP Client
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Appointment Details --}}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {{-- Service Details --}}
                    <div class="bg-white border border-[#EFEFEF] rounded-xl p-4">
                        <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Service</h5>
                        <div class="space-y-2">
                            <div class="font-semibold text-[#2C2C34]">{{ $selectedAppointment->service->name }}</div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-[#8B5D66]">Duration</span>
                                <span class="font-medium text-[#2C2C34]">{{ $selectedAppointment->duration }} minutes</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-[#8B5D66]">Price</span>
                                <span class="font-semibold text-[#E98CA5]">${{ number_format($selectedAppointment->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    {{-- Date & Time --}}
                    <div class="bg-white border border-[#EFEFEF] rounded-xl p-4">
                        <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Schedule</h5>
                        <div class="space-y-2">
                            <div class="flex items-center gap-2">
                                <flux:icon icon="calendar" class="w-4 h-4 text-[#E98CA5]" />
                                <span class="font-medium text-[#2C2C34]">{{ $selectedAppointment->appointment_date->format('l, F j, Y') }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <flux:icon icon="clock" class="w-4 h-4 text-[#E98CA5]" />
                                <span class="font-medium text-[#2C2C34]">
                                    {{ $selectedAppointment->start_time->format('g:i A') }} - 
                                    {{ $selectedAppointment->end_time->format('g:i A') }}
                                </span>
                            </div>
                            @if($selectedAppointment->appointment_date->isToday())
                                <div class="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full mt-2">
                                    <flux:icon icon="bell" class="w-3 h-3" />
                                    Today
                                </div>
                            @elseif($selectedAppointment->appointment_date->isTomorrow())
                                <div class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full mt-2">
                                    <flux:icon icon="bell" class="w-3 h-3" />
                                    Tomorrow
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Staff Assignment --}}
                <div class="bg-white border border-[#EFEFEF] rounded-xl p-4 mb-6">
                    <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Assigned Staff</h5>
                    @if($selectedAppointment->staff)
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-[#4A4A52]/10 flex items-center justify-center text-[#4A4A52] font-semibold text-sm">
                                {{ $selectedAppointment->staff->initials() }}
                            </div>
                            <div>
                                <div class="font-medium text-[#2C2C34]">{{ $selectedAppointment->staff->name }}</div>
                                <div class="text-sm text-[#8B5D66]">{{ $selectedAppointment->staff->email }}</div>
                            </div>
                        </div>
                    @else
                        <div class="flex items-center gap-2 text-[#8B5D66]">
                            <flux:icon icon="user-group" class="w-4 h-4" />
                            <span class="text-sm">No staff member assigned</span>
                        </div>
                    @endif
                </div>

                {{-- Notes --}}
                @if($selectedAppointment->notes || $selectedAppointment->client_notes)
                <div class="bg-amber-50 border border-amber-200 rounded-xl p-4 mb-6">
                    <h5 class="text-xs font-semibold text-amber-900 uppercase tracking-wider mb-3 flex items-center gap-2">
                        <flux:icon icon="document-text" class="w-4 h-4" />
                        Notes
                    </h5>
                    <div class="space-y-3">
                        @if($selectedAppointment->notes)
                            <div>
                                <div class="text-xs font-medium text-amber-800 mb-1">Internal Notes</div>
                                <p class="text-sm text-amber-900">{{ $selectedAppointment->notes }}</p>
                            </div>
                        @endif
                        @if($selectedAppointment->client_notes)
                            <div>
                                <div class="text-xs font-medium text-amber-800 mb-1">Client Notes</div>
                                <p class="text-sm text-amber-900">{{ $selectedAppointment->client_notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
                @endif

                {{-- Metadata --}}
                <div class="grid grid-cols-2 gap-4 text-xs text-[#8B5D66]">
                    <div>
                        <span class="font-medium">Created:</span> {{ $selectedAppointment->created_at->format('M j, Y g:i A') }}
                    </div>
                    <div>
                        <span class="font-medium">Last Updated:</span> {{ $selectedAppointment->updated_at->format('M j, Y g:i A') }}
                    </div>
                </div>
            </div>

            {{-- Footer Actions --}}
            <div class="bg-[#F7E9E6]/30 px-6 py-4 flex items-center justify-between">
                <x-manager.button 
                    wire:click="closeModals"
                    variant="outline" 
                    size="md">
                    Close
                </x-manager.button>
                <div class="flex gap-2">
                    <x-manager.button 
                        wire:click="openEditModal({{ $selectedAppointment->id }})"
                        variant="outline" 
                        icon="pencil"
                        size="md">
                        Edit
                    </x-manager.button>
                    @if($selectedAppointment->status !== 'cancelled')
                        <x-manager.button 
                            wire:click="deleteAppointment({{ $selectedAppointment->id }})"
                            wire:confirm="Are you sure you want to cancel this appointment?"
                            variant="outline" 
                            size="md"
                            class="!text-red-600 !border-red-600 hover:!bg-red-50">
                            Cancel Appointment
                        </x-manager.button>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
