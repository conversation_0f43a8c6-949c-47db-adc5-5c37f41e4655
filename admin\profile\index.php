<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_profile_functions.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

$currentUserId = $_SESSION['user_id'];
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $data = [
                    'name' => trim($_POST['name']),
                    'email' => trim($_POST['email']),
                    'phone' => trim($_POST['phone']),
                    'date_of_birth' => $_POST['date_of_birth'] ?: null
                ];
                
                updateAdminProfile($currentUserId, $data);
                
                // Update session data
                $_SESSION['user_name'] = $data['name'];
                
                $message = 'Profile updated successfully!';
                $messageType = 'success';
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'];
                $newPassword = $_POST['new_password'];
                $confirmPassword = $_POST['confirm_password'];
                
                changeAdminPassword($currentUserId, $currentPassword, $newPassword, $confirmPassword);
                
                $message = 'Password changed successfully!';
                $messageType = 'success';
                break;
                
            case 'change_email':
                $newEmail = trim($_POST['new_email']);
                $password = $_POST['password'];
                
                changeAdminEmail($currentUserId, $newEmail, $password);
                
                // Update session data
                $_SESSION['user_email'] = $newEmail;
                
                $message = 'Email changed successfully!';
                $messageType = 'success';
                break;
                
            case 'upload_image':
                if (isset($_FILES['profile_image'])) {
                    $imageUrl = uploadProfileImage($_FILES['profile_image'], $currentUserId);

                    // Update profile with new image
                    updateAdminProfile($currentUserId, ['image' => $imageUrl]);

                    $message = 'Profile image updated successfully!';
                    $messageType = 'success';
                }
                break;

            case 'create_admin':
                $data = [
                    'name' => trim($_POST['admin_name']),
                    'email' => trim($_POST['admin_email']),
                    'phone' => trim($_POST['admin_phone']),
                    'date_of_birth' => $_POST['admin_date_of_birth'] ?: null,
                    'password' => $_POST['admin_password']
                ];

                $newAdmin = createAdminUser($data, $currentUserId);

                $message = 'New admin user created successfully!';
                $messageType = 'success';
                break;

            case 'toggle_admin_status':
                $targetAdminId = $_POST['admin_id'];
                $newStatus = toggleAdminStatus($targetAdminId, $currentUserId);

                $message = 'Admin status updated successfully!';
                $messageType = 'success';
                break;

            case 'edit_admin':
                $targetAdminId = $_POST['admin_id'];
                $data = [
                    'name' => trim($_POST['edit_admin_name']),
                    'email' => trim($_POST['edit_admin_email']),
                    'phone' => trim($_POST['edit_admin_phone']),
                    'date_of_birth' => $_POST['edit_admin_date_of_birth'] ?: null
                ];

                updateAdminUser($targetAdminId, $data, $currentUserId);

                $message = 'Admin user updated successfully!';
                $messageType = 'success';
                break;

            case 'reset_admin_password':
                $targetAdminId = $_POST['admin_id'];
                $newPassword = $_POST['new_admin_password'];

                resetAdminPassword($targetAdminId, $newPassword, $currentUserId);

                $message = 'Admin password reset successfully!';
                $messageType = 'success';
                break;

            case 'delete_admin':
                $targetAdminId = $_POST['admin_id'];

                deleteAdminUser($targetAdminId, $currentUserId);

                $message = 'Admin user deleted successfully!';
                $messageType = 'success';
                break;

            case 'enable_2fa':
                $emailEnabled = isset($_POST['email_2fa']) ? 1 : 0;
                $backupEnabled = isset($_POST['backup_codes']) ? 1 : 0;

                if (!$emailEnabled && !$backupEnabled) {
                    throw new Exception('At least one 2FA method must be enabled');
                }

                $result = enableAdmin2FA($currentUserId, $emailEnabled, $backupEnabled);

                if (!$result['success']) {
                    throw new Exception($result['error']);
                }

                $message = 'Two-factor authentication enabled successfully!';
                $messageType = 'success';
                break;

            case 'disable_2fa':
                $result = disableAdmin2FA($currentUserId);

                if (!$result['success']) {
                    throw new Exception($result['error']);
                }

                $message = 'Two-factor authentication disabled successfully!';
                $messageType = 'success';
                break;

            case 'generate_backup_codes':
                // Use a more robust approach with better error handling
                try {
                    error_log("Starting backup codes generation for user: $currentUserId");

                    // CRITICAL FIX: Set 2FA verification flag to prevent logout during backup codes generation
                    $_SESSION['is_2fa_verified'] = true;
                    error_log("Set is_2fa_verified flag to prevent logout");

                    // Ensure required functions are loaded
                    if (!function_exists('generateBackupCodes')) {
                        require_once __DIR__ . '/../includes/admin_2fa_functions.php';
                    }
                    if (!function_exists('generateUUID')) {
                        require_once __DIR__ . '/../includes/functions.php';
                    }

                    // Verify all functions are available
                    if (!function_exists('generateBackupCodes') || !function_exists('generateUUID') || !function_exists('storeAdminBackupCodes')) {
                        throw new Exception("Required functions not available");
                    }

                    // Generate codes
                    $newCodes = generateBackupCodes(10);
                    error_log("Generated " . count($newCodes) . " backup codes");

                    // Store codes (storeAdminBackupCodes handles its own transaction)
                    $result = storeAdminBackupCodes($currentUserId, $newCodes);

                    if (!$result['success']) {
                        throw new Exception($result['error']);
                    }

                    // Store codes in session for display
                    $_SESSION['new_backup_codes'] = $newCodes;

                    error_log("Backup codes generation completed successfully");
                    $message = 'New backup codes generated successfully! Please save them securely.';
                    $messageType = 'success';

                } catch (Exception $e) {
                    error_log("Backup codes generation failed: " . $e->getMessage());
                    $message = 'Failed to generate backup codes: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';

        // Log the error for debugging
        error_log("Profile page error for user $currentUserId, action: " . ($_POST['action'] ?? 'unknown') . ", error: " . $e->getMessage());

        // Don't redirect on error, stay on the page to show the error
    }
}

// Get current admin profile
$adminProfile = getAdminProfile($currentUserId);

if (!$adminProfile) {
    error_log("Admin profile not found for user ID: $currentUserId");
    redirect('/admin');
}

// Get 2FA settings
try {
    $twoFASettings = getAdmin2FASettings($currentUserId);
    $remainingBackupCodes = getRemainingBackupCodesCount($currentUserId);
} catch (Exception $e) {
    error_log("Error getting 2FA settings for user $currentUserId: " . $e->getMessage());
    $twoFASettings = [
        'is_enabled' => false,
        'email_2fa_enabled' => false,
        'backup_codes_enabled' => false
    ];
    $remainingBackupCodes = 0;
}

// Check for new backup codes to display
$newBackupCodes = null;
if (isset($_SESSION['new_backup_codes'])) {
    $newBackupCodes = $_SESSION['new_backup_codes'];
    // Clear the codes from session after displaying
    unset($_SESSION['new_backup_codes']);
}

// Get all admin users for management
if (!function_exists('getAllAdminUsers')) {
    // Fallback: manually include the functions file
    require_once __DIR__ . '/../../includes/admin_profile_functions.php';
}

$allAdmins = getAllAdminUsers();

$pageTitle = "Admin Profile";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Profile Management CSS -->
<style>
/* Medical Profile Management Specific Styles */
.medical-profile-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.08);
}

.medical-profile-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    transition: left 0.6s ease;
}

.medical-profile-card:hover::before {
    left: 100%;
}

.medical-profile-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px rgba(73, 167, 92, 0.12);
}

.medical-section-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-section-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49A75C, #2E8B57);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-section-card:hover::before {
    transform: scaleX(1);
}

.medical-section-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: bold;
    position: relative;
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.3);
    transition: all 0.3s ease;
}

.medical-profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(73, 167, 92, 0.4);
}

.medical-profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.medical-profile-edit-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border: 3px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-profile-edit-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.4);
}

.medical-btn-primary-enhanced {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.medical-btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary-enhanced {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 0.75rem 1.5rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.medical-btn-secondary-enhanced:hover {
    background: rgba(73, 167, 92, 0.1);
    border-color: #49A75C;
    transform: translateY(-1px);
}

.medical-btn-danger-enhanced {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.medical-btn-danger-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.medical-alert-enhanced {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.medical-alert-success {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
    border-color: #16a34a;
}

.medical-alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}

.medical-info-item {
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.medical-info-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-1px);
}

.medical-info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.medical-info-value {
    font-size: 1rem;
    font-weight: 500;
    color: #1e3a8a;
}

.medical-security-item {
    background: rgba(248, 250, 252, 0.9);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.medical-security-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.1);
}

.medical-2fa-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
}

.medical-2fa-enabled {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
}

.medical-2fa-disabled {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.medical-admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.medical-admin-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    flex-direction: column;
}

.medical-admin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49A75C, #2E8B57);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-admin-card:hover::before {
    transform: scaleX(1);
}

.medical-admin-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-admin-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-admin-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.medical-input-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
}

.medical-input-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-label-enhanced {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

.medical-help-text-enhanced {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

.medical-icon-enhanced {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.medical-activity-log-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border-left: 4px solid #49A75C;
}

.medical-activity-log-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.medical-activity-badge {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.medical-activity-details summary {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0;
}

.medical-activity-details[open] summary {
    margin-bottom: 0.5rem;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
}

.medical-security-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    padding: 1.5rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.medical-security-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    transition: left 0.6s ease;
}

.medical-security-card:hover::before {
    left: 100%;
}

.medical-security-card:hover {
    transform: translateY(-4px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.15);
}

.medical-stat-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49A75C, #2E8B57);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-stat-card:hover::before {
    transform: scaleX(1);
}

.medical-stat-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.12);
}


</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Hero Section with Floating Profile Card -->
                    <div class="relative mb-12">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 via-blue-50/30 to-white rounded-3xl"></div>
                        <div class="absolute inset-0 opacity-10">
                            <div class="h-full w-full" style="background-image: radial-gradient(circle at 25% 25%, #49A75C 2px, transparent 2px), radial-gradient(circle at 75% 75%, #49A75C 1px, transparent 1px); background-size: 50px 50px;"></div>
                        </div>

                        <!-- Hero Content -->
                        <div class="relative p-8 text-center">
                            <div class="max-w-4xl mx-auto">
                                <h1 class="text-4xl md:text-5xl font-bold text-redolence-navy mb-4">
                                    Admin Profile Center
                                </h1>
                                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                                    Comprehensive account management with advanced security features and administrative controls
                                </p>

                                <!-- Floating Action Buttons -->
                                <div class="flex flex-wrap justify-center gap-4">
                                    <button onclick="openModal('profileModal')" class="medical-btn-primary-enhanced transform hover:scale-105">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                        Edit Profile
                                    </button>
                                    <button onclick="openModal('passwordModal')" class="medical-btn-secondary-enhanced transform hover:scale-105">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"/>
                                        </svg>
                                        Change Password
                                    </button>
                                    <button onclick="openModal('imageModal')" class="medical-btn-secondary-enhanced transform hover:scale-105">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                        </svg>
                                        Update Photo
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="medical-alert-enhanced medical-alert-<?= $messageType ?>">
                            <?php if ($messageType === 'success'): ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            <?php else: ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                </svg>
                            <?php endif; ?>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Profile Dashboard Layout -->
                    <div class="space-y-8">
                        <!-- Profile Card Row -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- Main Profile Card -->
                            <div class="lg:col-span-1">
                                <div class="medical-profile-card p-8 text-center h-full relative overflow-hidden">
                                    <!-- Decorative Background -->
                                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-redolence-green/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
                                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-100/50 to-transparent rounded-full translate-y-12 -translate-x-12"></div>

                                    <div class="relative z-10">
                                        <div class="medical-profile-avatar mx-auto mb-6 transform hover:scale-105 transition-transform duration-300">
                                            <?php if ($adminProfile['image']): ?>
                                                <img src="<?= htmlspecialchars($adminProfile['image']) ?>" alt="<?= htmlspecialchars($adminProfile['name']) ?>" class="w-full h-full object-cover">
                                            <?php else: ?>
                                                <div class="w-full h-full flex items-center justify-center text-4xl font-bold text-white bg-gradient-to-br from-redolence-green to-green-600">
                                                    <?= strtoupper(substr($adminProfile['name'], 0, 2)) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <h3 class="text-2xl font-bold text-redolence-navy mb-2"><?= htmlspecialchars($adminProfile['name']) ?></h3>
                                        <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-redolence-green to-green-600 text-white rounded-full text-sm font-semibold mb-4">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                            </svg>
                                            <?= htmlspecialchars($adminProfile['role']) ?>
                                        </div>

                                        <div class="text-sm text-gray-600 space-y-2">
                                            <div class="flex items-center justify-center">
                                                <svg class="w-4 h-4 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                </svg>
                                                Member since <?= date('M Y', strtotime($adminProfile['created_at'])) ?>
                                            </div>
                                            <?php if ($adminProfile['last_login']): ?>
                                            <div class="flex items-center justify-center">
                                                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                Last active <?= date('M j', strtotime($adminProfile['last_login'])) ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information Cards -->
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Personal Information -->
                                <div class="medical-section-card">
                                    <h3 class="text-lg font-semibold text-redolence-navy mb-4 flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                        Personal Information
                                    </h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="medical-info-item">
                                            <div class="medical-info-label">Full Name</div>
                                            <div class="medical-info-value"><?= htmlspecialchars($adminProfile['name']) ?></div>
                                        </div>
                                        <div class="medical-info-item">
                                            <div class="medical-info-label">Email Address</div>
                                            <div class="medical-info-value"><?= htmlspecialchars($adminProfile['email']) ?></div>
                                        </div>
                                        <div class="medical-info-item">
                                            <div class="medical-info-label">Phone Number</div>
                                            <div class="medical-info-value"><?= $adminProfile['phone'] ? htmlspecialchars($adminProfile['phone']) : 'Not provided' ?></div>
                                        </div>
                                        <div class="medical-info-item">
                                            <div class="medical-info-label">Date of Birth</div>
                                            <div class="medical-info-value"><?= $adminProfile['date_of_birth'] ? date('M j, Y', strtotime($adminProfile['date_of_birth'])) : 'Not provided' ?></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Status -->
                                <div class="medical-section-card">
                                    <h3 class="text-lg font-semibold text-redolence-navy mb-4 flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Account Status
                                    </h3>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                                            <div class="text-2xl font-bold text-green-600 mb-1">Active</div>
                                            <div class="text-sm text-green-700">Account Status</div>
                                        </div>
                                        <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <div class="text-2xl font-bold text-blue-600 mb-1"><?= $twoFASettings['is_enabled'] ? 'Enabled' : 'Disabled' ?></div>
                                            <div class="text-sm text-blue-700">2FA Security</div>
                                        </div>
                                        <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                                            <div class="text-2xl font-bold text-purple-600 mb-1">Admin</div>
                                            <div class="text-sm text-purple-700">Access Level</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Dashboard -->
                        <div class="medical-section-card">
                            <div class="flex items-center justify-between mb-8">
                                <h2 class="text-2xl font-bold text-redolence-navy flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                    </div>
                                    Security Center
                                </h2>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm font-medium text-green-600">Secure</span>
                                </div>
                            </div>

                            <!-- Security Grid Layout -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 mb-8">
                                <!-- Change Password -->
                                <div class="medical-security-card">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-semibold text-redolence-navy">Password Security</h3>
                                                <p class="text-sm text-gray-600">Last updated: <?= date('M j, Y', strtotime($adminProfile['updated_at'])) ?></p>
                                            </div>
                                        </div>
                                        <button onclick="openModal('passwordModal')" class="medical-btn-secondary-enhanced">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                                            </svg>
                                            Change Password
                                        </button>
                                    </div>
                                </div>

                                <!-- Email Security Card -->
                                <div class="medical-security-card">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <h3 class="text-lg font-bold text-redolence-navy mb-2">Email Address</h3>
                                    <p class="text-sm text-gray-600 mb-4">Current: <?= htmlspecialchars($adminProfile['email']) ?></p>
                                    <button onclick="openModal('emailModal')" class="w-full medical-btn-secondary-enhanced justify-center">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                        Change Email
                                    </button>
                                </div>

                                <!-- Two-Factor Authentication Card -->
                                <div class="medical-security-card">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                            </svg>
                                        </div>
                                        <div class="w-3 h-3 <?= $twoFASettings['is_enabled'] ? 'bg-green-500' : 'bg-red-500' ?> rounded-full"></div>
                                    </div>
                                    <h3 class="text-lg font-bold text-redolence-navy mb-2">Two-Factor Authentication</h3>
                                    <p class="text-sm text-gray-600 mb-4">
                                        Status:
                                        <span class="font-semibold <?= $twoFASettings['is_enabled'] ? 'text-green-600' : 'text-red-600' ?>">
                                            <?= $twoFASettings['is_enabled'] ? 'Enabled' : 'Disabled' ?>
                                        </span>
                                    </p>
                                    <?php if ($twoFASettings['is_enabled']): ?>
                                        <button onclick="openModal('disable2FAModal')" class="w-full medical-btn-danger-enhanced justify-center">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"/>
                                            </svg>
                                            Disable 2FA
                                        </button>
                                    <?php else: ?>
                                        <button onclick="openModal('enable2FAModal')" class="w-full medical-btn-primary-enhanced justify-center">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                            </svg>
                                            Enable 2FA
                                        </button>
                                        <?php endif; ?>
                                    </div>

                                    <?php if ($twoFASettings['is_enabled']): ?>
                                        <!-- 2FA Status Details -->
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div class="medical-info-item">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                                    </svg>
                                                    <span class="medical-info-label">Email Verification</span>
                                                </div>
                                                <div class="medical-info-value mt-1">
                                                    <?= $twoFASettings['email_2fa_enabled'] ? 'Enabled' : 'Disabled' ?>
                                                </div>
                                            </div>

                                            <div class="medical-info-item">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"/>
                                                    </svg>
                                                    <span class="medical-info-label">Backup Codes</span>
                                                </div>
                                                <div class="medical-info-value mt-1">
                                                    <?= $remainingBackupCodes ?> remaining
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 2FA Actions -->
                                        <div class="flex flex-wrap gap-3 mt-6">
                                            <?php if ($twoFASettings['backup_codes_enabled']): ?>
                                                <a href="<?= getBasePath() ?>/admin/auth/backup-codes.php" class="medical-btn-secondary-enhanced">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                    </svg>
                                                    Manage Backup Codes
                                                </a>
                                                <?php if ($remainingBackupCodes <= 2): ?>
                                                    <button onclick="openModal('generateBackupCodesModal')" class="medical-btn-primary-enhanced">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                                        </svg>
                                                        Generate New Codes
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($remainingBackupCodes <= 2): ?>
                                            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                                <div class="flex items-center">
                                                    <svg class="w-5 h-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                                    </svg>
                                                    <span class="text-yellow-800 font-semibold">Low backup codes warning</span>
                                                </div>
                                                <p class="text-yellow-700 text-sm mt-1">
                                                    You have only <?= $remainingBackupCodes ?> backup codes remaining. Generate new ones to ensure account recovery.
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <!-- 2FA Benefits -->
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                                            <h4 class="text-blue-800 font-semibold mb-3 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                Enhanced Security Benefits
                                            </h4>
                                            <ul class="text-blue-700 text-sm space-y-2">
                                                <li class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                    Email verification codes for secure login
                                                </li>
                                                <li class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                    Backup codes for emergency access
                                                </li>
                                                <li class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                    Protection against unauthorized access
                                                </li>
                                                <li class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                    Audit logging for security monitoring
                                                </li>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Team Management Dashboard -->
                    <div class="mt-8">
                        <div class="medical-profile-card p-8">
                            <!-- Header with Stats -->
                            <div class="text-center mb-8">
                                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl mb-4">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                </div>
                                <h2 class="text-3xl font-bold text-redolence-navy mb-2">Team Management</h2>
                                <p class="text-gray-600 mb-6">Manage administrator accounts and monitor team activities</p>
                                <button onclick="openModal('createAdminModal')" class="medical-btn-primary-enhanced transform hover:scale-105">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                    </svg>
                                    Add New Administrator
                                </button>
                            </div>

                            <!-- Team Statistics Dashboard -->
                            <?php
                            $adminStats = getAdminStatistics();
                            ?>
                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                                <div class="medical-stat-card">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-redolence-navy"><?= $adminStats['total_admins'] ?></div>
                                            <div class="text-sm text-gray-600">Total</div>
                                        </div>
                                    </div>
                                    <div class="text-sm font-medium text-gray-700">Team Members</div>
                                </div>

                                <div class="medical-stat-card">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-emerald-600"><?= $adminStats['active_admins'] ?></div>
                                            <div class="text-sm text-gray-600">Active</div>
                                        </div>
                                    </div>
                                    <div class="text-sm font-medium text-gray-700">Online Now</div>
                                </div>

                                <div class="medical-stat-card">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"/>
                                            </svg>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-red-600"><?= $adminStats['inactive_admins'] ?></div>
                                            <div class="text-sm text-gray-600">Inactive</div>
                                        </div>
                                    </div>
                                    <div class="text-sm font-medium text-gray-700">Offline</div>
                                </div>

                                <div class="medical-stat-card">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                            </svg>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-blue-600"><?= $adminStats['recent_activities'] ?></div>
                                            <div class="text-sm text-gray-600">Today</div>
                                        </div>
                                    </div>
                                    <div class="text-sm font-medium text-gray-700">Activities</div>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="medical-section-card mb-6">
                                <div class="flex flex-col md:flex-row gap-4">
                                    <div class="flex-1">
                                        <div class="relative">
                                            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                            <input type="text" id="adminSearch" placeholder="Search admins by name or email..."
                                                   class="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-redolence-green transition-colors">
                                        </div>
                                    </div>
                                    <div class="flex gap-3">
                                        <select id="statusFilter" class="px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:border-redolence-green transition-colors">
                                            <option value="all">All Status</option>
                                            <option value="active">Active Only</option>
                                            <option value="inactive">Inactive Only</option>
                                        </select>
                                        <button onclick="filterAdmins()" class="medical-btn-primary-enhanced">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                            Filter
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="medical-admin-grid">
                                <?php foreach ($allAdmins as $admin): ?>
                                    <div class="admin-card medical-admin-card <?= $admin['is_active'] ? 'border-green-200' : 'border-red-200' ?>"
                                         data-name="<?= htmlspecialchars($admin['name']) ?>"
                                         data-email="<?= htmlspecialchars($admin['email']) ?>"
                                         data-status="<?= $admin['is_active'] ? 'active' : 'inactive' ?>">
                                        <!-- Admin Header -->
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex items-center space-x-3">
                                                <div class="medical-admin-avatar">
                                                    <?php if ($admin['image']): ?>
                                                        <img src="<?= htmlspecialchars($admin['image']) ?>" alt="">
                                                    <?php else: ?>
                                                        <?= strtoupper(substr($admin['name'], 0, 2)) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <h3 class="text-lg font-bold text-redolence-navy"><?= htmlspecialchars($admin['name']) ?></h3>
                                                    <p class="text-sm text-gray-600"><?= htmlspecialchars($admin['email']) ?></p>
                                                </div>
                                            </div>
                                            <div class="flex flex-col items-end space-y-2">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?= $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                    <div class="w-2 h-2 rounded-full <?= $admin['is_active'] ? 'bg-green-500' : 'bg-red-500' ?> mr-2"></div>
                                                    <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Admin Details -->
                                        <div class="flex-1 space-y-3 mb-6">
                                            <div class="flex items-center text-sm text-gray-600">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                                    </svg>
                                                </div>
                                                <span><?= $admin['phone'] ? htmlspecialchars($admin['phone']) : 'No phone provided' ?></span>
                                            </div>
                                            <div class="flex items-center text-sm text-gray-600">
                                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <span>Joined <?= date('M j, Y', strtotime($admin['created_at'])) ?></span>
                                            </div>
                                        </div>

                                        <?php if ($admin['id'] !== $currentUserId): ?>
                                            <div class="space-y-3">
                                                <div class="flex gap-2">
                                                    <button onclick="editAdmin('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>', '<?= htmlspecialchars($admin['email']) ?>', '<?= htmlspecialchars($admin['phone']) ?>', '<?= $admin['date_of_birth'] ?>')"
                                                            class="flex-1 medical-btn-secondary-enhanced text-sm py-2">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                        </svg>
                                                        Edit
                                                    </button>
                                                    <button onclick="resetAdminPassword('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>')"
                                                            class="flex-1 medical-btn-secondary-enhanced text-sm py-2">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"/>
                                                        </svg>
                                                        Reset
                                                    </button>
                                                </div>
                                                <div class="flex gap-2">
                                                    <button onclick="toggleAdminStatus('<?= $admin['id'] ?>', <?= $admin['is_active'] ? 'false' : 'true' ?>)"
                                                            class="flex-1 <?= $admin['is_active'] ? 'medical-btn-danger-enhanced' : 'medical-btn-primary-enhanced' ?> text-sm py-2">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <?php if ($admin['is_active']): ?>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"/>
                                                            <?php else: ?>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                            <?php endif; ?>
                                                        </svg>
                                                        <?= $admin['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                    </button>
                                                    <button onclick="deleteAdmin('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>')"
                                                            class="flex-1 medical-btn-danger-enhanced text-sm py-2">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                        </svg>
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <div class="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg text-blue-800">
                                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                                    </svg>
                                                    Current User
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Activity Log Section -->
                    <div class="mt-6">
                        <div class="medical-profile-card p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-redolence-navy flex items-center">
                                    <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Recent Admin Activities
                                </h2>
                                <button onclick="toggleActivityLog()" class="medical-btn-primary-enhanced">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    View All Logs
                                </button>
                            </div>

                            <div id="activityLogContainer" class="hidden">
                                <?php
                                $recentLogs = getAdminLogs(null, 10, 0);
                                ?>
                                <div class="space-y-4">
                                    <?php foreach ($recentLogs as $log): ?>
                                        <div class="medical-activity-log-item">
                                            <div class="flex items-start justify-between">
                                                <div class="flex items-start space-x-4 flex-1">
                                                    <div class="medical-activity-icon">
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="flex items-center space-x-3 mb-2">
                                                            <span class="font-semibold text-redolence-navy"><?= htmlspecialchars($log['admin_name']) ?></span>
                                                            <span class="medical-activity-badge"><?= htmlspecialchars($log['action']) ?></span>
                                                        </div>
                                                        <p class="text-gray-600 text-sm leading-relaxed"><?= htmlspecialchars($log['description']) ?></p>
                                                        <?php if ($log['metadata']): ?>
                                                            <div class="mt-3">
                                                                <details class="medical-activity-details">
                                                                    <summary class="cursor-pointer text-redolence-green hover:text-redolence-navy transition-colors">
                                                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                        </svg>
                                                                        View Technical Details
                                                                    </summary>
                                                                    <div class="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                                                        <pre class="text-xs text-gray-700 overflow-x-auto"><?= htmlspecialchars(json_encode(json_decode($log['metadata']), JSON_PRETTY_PRINT)) ?></pre>
                                                                    </div>
                                                                </details>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="text-right text-sm text-gray-500 ml-4">
                                                    <div class="font-medium"><?= date('M j, Y', strtotime($log['created_at'])) ?></div>
                                                    <div class="text-xs"><?= date('H:i:s', strtotime($log['created_at'])) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if (empty($recentLogs)): ?>
                                        <div class="text-center py-12">
                                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                            <p class="text-gray-500 font-medium">No admin activities recorded yet</p>
                                            <p class="text-gray-400 text-sm mt-1">Activity logs will appear here when admins perform actions</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Profile Edit Modal -->
<div id="profileModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="medical-profile-card max-w-md w-full p-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-redolence-navy flex items-center">
                    <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit Profile
                </h3>
                <button onclick="closeModal('profileModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="update_profile">

                <div>
                    <label class="medical-label-enhanced">Full Name <span class="text-red-500">*</span></label>
                    <input type="text" name="name" value="<?= htmlspecialchars($adminProfile['name']) ?>" required
                           class="medical-input-enhanced">
                </div>

                <div>
                    <label class="medical-label-enhanced">Email Address <span class="text-red-500">*</span></label>
                    <input type="email" name="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" required
                           class="medical-input-enhanced">
                </div>

                <div>
                    <label class="medical-label-enhanced">Phone Number</label>
                    <input type="tel" name="phone" value="<?= htmlspecialchars($adminProfile['phone'] ?? '') ?>"
                           class="medical-input-enhanced" placeholder="Enter your phone number">
                </div>

                <div>
                    <label class="medical-label-enhanced">Date of Birth</label>
                    <input type="date" name="date_of_birth" value="<?= $adminProfile['date_of_birth'] ?>"
                           class="medical-input-enhanced">
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('profileModal')" class="medical-btn-secondary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Update Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Password Change Modal -->
<div id="passwordModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="medical-profile-card max-w-md w-full p-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-redolence-navy flex items-center">
                    <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"/>
                    </svg>
                    Change Password
                </h3>
                <button onclick="closeModal('passwordModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="change_password">

                <div>
                    <label class="medical-label-enhanced">Current Password <span class="text-red-500">*</span></label>
                    <input type="password" name="current_password" required
                           class="medical-input-enhanced" placeholder="Enter your current password">
                </div>

                <div>
                    <label class="medical-label-enhanced">New Password <span class="text-red-500">*</span></label>
                    <input type="password" name="new_password" required minlength="8"
                           class="medical-input-enhanced" placeholder="Enter your new password">
                    <p class="medical-help-text-enhanced">Must be at least 8 characters with uppercase, lowercase, and number</p>
                </div>

                <div>
                    <label class="medical-label-enhanced">Confirm New Password <span class="text-red-500">*</span></label>
                    <input type="password" name="confirm_password" required minlength="8"
                           class="medical-input-enhanced" placeholder="Confirm your new password">
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('passwordModal')" class="medical-btn-secondary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"/>
                        </svg>
                        Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Change Modal -->
<div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="medical-profile-card max-w-md w-full p-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-redolence-navy flex items-center">
                    <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    Change Email Address
                </h3>
                <button onclick="closeModal('emailModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="change_email">

                <div>
                    <label class="medical-label-enhanced">Current Email Address</label>
                    <input type="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" disabled
                           class="medical-input-enhanced bg-gray-100 text-gray-600 cursor-not-allowed">
                </div>

                <div>
                    <label class="medical-label-enhanced">New Email Address <span class="text-red-500">*</span></label>
                    <input type="email" name="new_email" required
                           class="medical-input-enhanced" placeholder="Enter your new email address">
                </div>

                <div>
                    <label class="medical-label-enhanced">Confirm Password <span class="text-red-500">*</span></label>
                    <input type="password" name="password" required
                           class="medical-input-enhanced" placeholder="Enter your current password to confirm">
                    <p class="medical-help-text-enhanced">Required for security verification</p>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('emailModal')" class="medical-btn-secondary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        Update Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="medical-profile-card max-w-md w-full p-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-redolence-navy flex items-center">
                    <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    Update Profile Image
                </h3>
                <button onclick="closeModal('imageModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                <input type="hidden" name="action" value="upload_image">

                <div>
                    <label class="medical-label-enhanced">Select Profile Image <span class="text-red-500">*</span></label>
                    <input type="file" name="profile_image" accept="image/*" required
                           class="medical-input-enhanced">
                    <p class="medical-help-text-enhanced">Max size: 5MB. Supported formats: JPEG, PNG, GIF, WebP</p>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('imageModal')" class="medical-btn-secondary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary-enhanced">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                        Upload Image
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Admin Modal -->
<div id="createAdminModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-profile-card w-full max-w-md mx-4 p-8">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold text-redolence-navy flex items-center">
                <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                </svg>
                Create New Admin
            </h3>
            <button onclick="closeModal('createAdminModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-6">
            <input type="hidden" name="action" value="create_admin">

            <div>
                <label class="medical-label-enhanced">Full Name <span class="text-red-500">*</span></label>
                <input type="text" name="admin_name" required
                       class="medical-input-enhanced" placeholder="Enter admin's full name">
            </div>

            <div>
                <label class="medical-label-enhanced">Email Address <span class="text-red-500">*</span></label>
                <input type="email" name="admin_email" required
                       class="medical-input-enhanced" placeholder="Enter admin's email address">
            </div>

            <div>
                <label class="medical-label-enhanced">Phone Number</label>
                <input type="tel" name="admin_phone"
                       class="medical-input-enhanced" placeholder="Enter admin's phone number">
            </div>

            <div>
                <label class="medical-label-enhanced">Date of Birth</label>
                <input type="date" name="admin_date_of_birth"
                       class="medical-input-enhanced">
            </div>

            <div>
                <label class="medical-label-enhanced">Password <span class="text-red-500">*</span></label>
                <input type="password" name="admin_password" required
                       class="medical-input-enhanced" placeholder="Create a secure password">
                <p class="medical-help-text-enhanced">Minimum 8 characters with uppercase, lowercase, and number</p>
            </div>

            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeModal('createAdminModal')" class="medical-btn-secondary-enhanced">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Cancel
                </button>
                <button type="submit" class="medical-btn-primary-enhanced">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                    </svg>
                    Create Admin
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Admin Modal -->
<div id="editAdminModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Edit Admin User</h3>
            <button onclick="closeModal('editAdminModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="edit_admin">
            <input type="hidden" name="admin_id" id="editAdminId">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Full Name *</label>
                <input type="text" name="edit_admin_name" id="editAdminName" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
                <input type="email" name="edit_admin_email" id="editAdminEmail" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                <input type="tel" name="edit_admin_phone" id="editAdminPhone"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                <input type="date" name="edit_admin_date_of_birth" id="editAdminDateOfBirth"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('editAdminModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Update Admin
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Reset Admin Password</h3>
            <button onclick="closeModal('resetPasswordModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="reset_admin_password">
            <input type="hidden" name="admin_id" id="resetPasswordAdminId">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Admin User</label>
                <input type="text" id="resetPasswordAdminName" disabled
                       class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-gray-400">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">New Password *</label>
                <input type="password" name="new_admin_password" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                <p class="text-xs text-gray-400 mt-1">Minimum 8 characters with uppercase, lowercase, and number</p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('resetPasswordModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-yellow-600 text-white rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                    Reset Password
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enable 2FA Modal -->
<div id="enable2FAModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Enable Two-Factor Authentication</h3>
            <button onclick="closeModal('enable2FAModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="enable_2fa">

            <div class="mb-4">
                <p class="text-gray-300 text-sm mb-4">
                    Select the 2FA methods you want to enable for enhanced security:
                </p>

                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" name="email_2fa" value="1" checked class="mr-3 rounded">
                        <div>
                            <span class="text-white font-medium">Email Verification</span>
                            <p class="text-gray-400 text-sm">Receive 6-digit codes via email</p>
                        </div>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="backup_codes" value="1" checked class="mr-3 rounded">
                        <div>
                            <span class="text-white font-medium">Backup Codes</span>
                            <p class="text-gray-400 text-sm">Emergency access codes for account recovery</p>
                        </div>
                    </label>
                </div>
            </div>

            <div class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-3 mb-4">
                <p class="text-blue-200 text-sm">
                    <strong>Note:</strong> After enabling 2FA, you'll need to verify your identity with the selected methods each time you log in.
                </p>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('enable2FAModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                    Enable 2FA
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Disable 2FA Modal -->
<div id="disable2FAModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Disable Two-Factor Authentication</h3>
            <button onclick="closeModal('disable2FAModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="disable_2fa">

            <div class="mb-4">
                <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-red-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-red-200 font-semibold mb-2">Security Warning</h4>
                            <p class="text-red-300 text-sm">
                                Disabling 2FA will reduce your account security. Your account will only be protected by your password.
                            </p>
                        </div>
                    </div>
                </div>

                <p class="text-gray-300 text-sm">
                    Are you sure you want to disable two-factor authentication? This action will:
                </p>
                <ul class="text-gray-400 text-sm mt-2 space-y-1 ml-4">
                    <li>• Remove email verification requirement</li>
                    <li>• Invalidate all backup codes</li>
                    <li>• Reduce account security</li>
                </ul>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('disable2FAModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                    Disable 2FA
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Generate Backup Codes Modal -->
<div id="generateBackupCodesModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Generate New Backup Codes</h3>
            <button onclick="closeModal('generateBackupCodesModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="generate_backup_codes">

            <div class="mb-4">
                <div class="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-yellow-200 font-semibold mb-2">Important Notice</h4>
                            <p class="text-yellow-300 text-sm">
                                Generating new backup codes will invalidate all existing codes. Make sure to save the new codes securely.
                            </p>
                        </div>
                    </div>
                </div>

                <p class="text-gray-300 text-sm">
                    This action will:
                </p>
                <ul class="text-gray-400 text-sm mt-2 space-y-1 ml-4">
                    <li>• Generate 10 new backup codes</li>
                    <li>• Invalidate all existing backup codes</li>
                    <li>• Display the new codes for you to save</li>
                </ul>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('generateBackupCodesModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
                    Generate Codes
                </button>
            </div>
        </form>
    </div>
</div>

<?php if ($newBackupCodes): ?>
<!-- New Backup Codes Display Modal -->
<div id="newBackupCodesModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-lg mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Your New Backup Codes</h3>
            <button onclick="closeModal('newBackupCodesModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mb-4">
            <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
                <p class="text-red-200 text-sm">
                    <strong>Important:</strong> Save these codes in a secure location. They will not be shown again and each code can only be used once.
                </p>
            </div>

            <div class="grid grid-cols-2 gap-2">
                <?php foreach ($newBackupCodes as $code): ?>
                    <div class="bg-secondary-700 rounded p-2 text-center font-mono text-salon-gold">
                        <?= htmlspecialchars($code) ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="flex gap-3">
            <button onclick="printBackupCodes()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Print Codes
            </button>
            <button onclick="closeModal('newBackupCodesModal')" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                I've Saved Them
            </button>
        </div>
    </div>
</div>

<script>
// Auto-show new backup codes modal
document.addEventListener('DOMContentLoaded', function() {
    openModal('newBackupCodesModal');
});

function printBackupCodes() {
    window.print();
}
</script>
<?php endif; ?>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        e.target.classList.add('hidden');
    }
});

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    if (confirm(`Are you sure you want to ${activate ? 'activate' : 'deactivate'} this admin user?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_admin_status">
            <input type="hidden" name="admin_id" value="${adminId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function editAdmin(adminId, name, email, phone, dateOfBirth) {
    document.getElementById('editAdminId').value = adminId;
    document.getElementById('editAdminName').value = name;
    document.getElementById('editAdminEmail').value = email;
    document.getElementById('editAdminPhone').value = phone || '';
    document.getElementById('editAdminDateOfBirth').value = dateOfBirth || '';
    openModal('editAdminModal');
}

function resetAdminPassword(adminId, adminName) {
    document.getElementById('resetPasswordAdminId').value = adminId;
    document.getElementById('resetPasswordAdminName').value = adminName;
    openModal('resetPasswordModal');
}

function deleteAdmin(adminId, adminName) {
    if (confirm(`Are you sure you want to delete admin user "${adminName}"? This action will deactivate their account.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_admin">
            <input type="hidden" name="admin_id" value="${adminId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Search and filter functionality
function filterAdmins() {
    const searchTerm = document.getElementById('adminSearch').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const adminCards = document.querySelectorAll('.admin-card');

    adminCards.forEach(card => {
        const name = card.dataset.name.toLowerCase();
        const email = card.dataset.email.toLowerCase();
        const status = card.dataset.status;

        const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
        const matchesStatus = statusFilter === 'all' || status === statusFilter;

        if (matchesSearch && matchesStatus) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Real-time search
document.getElementById('adminSearch').addEventListener('input', filterAdmins);
document.getElementById('statusFilter').addEventListener('change', filterAdmins);

// Activity log toggle
function toggleActivityLog() {
    const container = document.getElementById('activityLogContainer');
    const button = event.target;

    if (container.classList.contains('hidden')) {
        container.classList.remove('hidden');
        button.innerHTML = '<i class="fas fa-eye-slash mr-2"></i>Hide Logs';
    } else {
        container.classList.add('hidden');
        button.innerHTML = '<i class="fas fa-history mr-2"></i>View All Logs';
    }
}

// Password confirmation validation
document.querySelector('input[name="confirm_password"]').addEventListener('input', function() {
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = this.value;

    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// AJAX Backup Codes Generation
function generateBackupCodesAjax() {
    const btn = document.getElementById('generateBackupCodesBtn');
    const originalText = btn.innerHTML;

    // Disable button and show loading
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';

    // Make AJAX request
    fetch('/flix/admin/ajax/generate_backup_codes.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close the generation modal
            closeModal('generateBackupCodesModal');

            // Show the new codes in a modal
            showNewBackupCodes(data.codes);

            // Refresh the page to update the backup codes count
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('Error generating backup codes: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while generating backup codes. Please try again.');
    })
    .finally(() => {
        // Re-enable button
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

function showNewBackupCodes(codes) {
    // Create modal HTML
    const modalHtml = `
        <div id="newBackupCodesModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-lg mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-white">Your New Backup Codes</h3>
                    <button onclick="closeModal('newBackupCodesModal')" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-green-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="text-green-200 font-semibold mb-2">Backup Codes Generated Successfully!</h4>
                            <p class="text-green-300 text-sm">
                                Save these codes in a secure location. Each code can only be used once.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-900 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-2 gap-2 font-mono text-sm">
                        ${codes.map(code => `<div class="bg-gray-800 p-2 rounded text-center text-salon-gold">${code}</div>`).join('')}
                    </div>
                </div>

                <div class="flex gap-3">
                    <button onclick="printBackupCodes()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-print mr-2"></i>Print Codes
                    </button>
                    <button onclick="closeModal('newBackupCodesModal')" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg hover:bg-yellow-500 transition-colors">
                        I've Saved Them
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('newBackupCodesModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function printBackupCodes() {
    window.print();
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>

