# AI Coding Agent Instructions - InStyle Salon

This is a **Laravel 12 + Livewire/Volt + Flux** full-stack application for a luxury feminine salon brand called InStyle. The project combines server-side rendering with reactive components.

## Core Architecture

### Tech Stack
- **Backend:** Laravel 12, PHP 8.2+
- **Frontend:** Livewire Volt (view-based components), Flux (UI components), Tailwind CSS, Alpine.js
- **Auth:** Laravel Fortify (password auth) + Google OAuth (via Socialite)
- **Testing:** Pest + Laravel plugin + Mockery

### Key Components & Patterns

#### 1. **Authentication & Authorization**
- **Fortify Actions** (`app/Actions/Fortify/`) handle user creation and password resets
- **Google OAuth** via `app/Http/Controllers/GoogleAuthController.php` - auto-verifies emails for OAuth users
- **Role-based Access** using `RoleMiddleware.php` - checks `user->role` field (default: 'customer', also 'manager')
- User model includes: `google_id`, `google_token`, `google_refresh_token`, `role`, two-factor support

#### 2. **UI & Views Structure**
- **Volt Components** (view-based): Located in `resources/views/livewire/` - mounted via `VoltServiceProvider.php`
  - Auth flows: `livewire/auth/{login,register,verify-email,two-factor-challenge,etc.}`
  - Settings: `livewire/settings/{profile,password,appearance,two-factor}` - inline PHP classes
- **Blade Templates**: `resources/views/` - extends `x-layouts.app` or `x-layouts.auth`
- **Flux Components**: `<flux:input>`, `<flux:checkbox>`, `<flux:link>` - available throughout
- **Landing Page** (`landing.blade.php`): Luxury feminine design with Alpine.js (scroll effects, mobile menu)

#### 3. **Routing**
- **Volt routes**: Use `Volt::route()` in `routes/web.php` for reactive views
- **Controller routes**: Traditional `Route::get()`, `Route::post()` for OAuth/API endpoints
- **Role-protected routes**: Use middleware `role:manager` to restrict by role
- Dashboard redirects based on role: customers see `/dashboard`, managers see `/manager/dashboard`

#### 4. **Database & Models**
- **User model** only defined model; uses Eloquent factories
- **Migrations** (in order):
  - Base: users, cache, jobs tables
  - Sept 2025: Two-factor columns
  - Dec 2025: Google OAuth columns (google_id, tokens)
  - Dec 2025: Role column (default 'customer')
- **Factory**: `UserFactory.php` - provides test users with hashed passwords

#### 5. **Styling**
- **Tailwind CSS 4.0** via `@tailwindcss/vite`
- **Custom color scheme** (from landing page spec):
  - Pink: `#E98CA5` → `#F4B6C5`, Deep Rose: `#C85E78`
  - Neutrals: Charcoal `#4A4A52`, Gold `#DCC7A1`, Blush `#F7E9E6`
- **Build process**: Vite with `laravel-vite-plugin`

---

## Developer Workflows

### Setup & Local Development
```bash
# Fresh installation
composer install
php artisan key:generate
php artisan migrate
npm install && npm run build

# Running dev server
npm run dev        # Frontend watch (Vite)
php artisan serve  # Backend (or use Laragon/Valet)
```

### Testing
```bash
# Run all tests
./vendor/bin/pest

# Run specific test file
./vendor/bin/pest tests/Feature/DashboardTest.php

# Watch mode
./vendor/bin/pest --watch

# Pest uses RefreshDatabase trait for feature tests (auto-rollback)
```

### Database Management
```bash
php artisan migrate           # Run pending migrations
php artisan migrate:fresh     # Drop all + re-migrate
php artisan tinker            # Interactive shell for testing
```

### Code Quality
```bash
composer run-script pint      # Laravel Pint (code formatting)
```

---

## Project-Specific Patterns & Conventions

### 1. **Volt Component Structure** (Recommended for new features)
```php
<?php
use Livewire\Volt\Component;

new class extends Component {
    public string $property = '';
    
    public function mount(): void {
        // Initialize from auth or route
    }
    
    public function action(): void {
        // Handle form submission
        $this->validate([...]);
        $this->dispatch('success');
    }
};
?>
<form wire:submit="action">
    <flux:input wire:model="property" />
    <flux:button type="submit">Save</flux:button>
</form>
```

### 2. **Adding Role-Based Routes**
```php
// In routes/web.php
Route::middleware(['auth', 'verified', 'role:manager'])->group(function () {
    Route::get('/manager/specific-page', ...)->name('manager.page');
});

// RoleMiddleware checks: abort(403) if user->role !== required role
```

### 3. **Authentication Flow**
- **Password Auth**: Fortify handles login/register via `CreateNewUser` action
- **Google OAuth**: `GoogleAuthController::redirect()` → Google → `callback()` → login & redirect to `/dashboard`
- **Two-Factor**: Fortify manages TOTP; view in `livewire/auth/two-factor-challenge.blade.php`

### 4. **Blade Component Conventions**
- Layout: `<x-layouts.app>` (authenticated views), `<x-layouts.auth>` (auth pages)
- Prefix: `x-` for components, e.g., `<x-auth-header :title="..." />`
- Slot-based patterns for flexible layouts

### 5. **Form Validation (Volt)**
```php
$this->validate([
    'email' => ['required', 'email', Rule::unique(User::class)],
    'password' => $this->passwordRules(), // From PasswordValidationRules trait
]);
```

### 6. **Testing Patterns**
```php
// Feature tests (in tests/Feature)
test('authenticated users can visit dashboard', function () {
    $user = User::factory()->create();
    $this->actingAs($user);
    $response = $this->get(route('dashboard'));
    $response->assertStatus(200);
});
```

---

## Critical Files & Integration Points

| File | Purpose |
|------|---------|
| `routes/web.php` | All route definitions; mix of Volt::route() and traditional routes |
| `app/Models/User.php` | User entity; includes role, OAuth fields, two-factor |
| `app/Http/Controllers/GoogleAuthController.php` | OAuth flow; creates/updates users |
| `app/Http/Middleware/RoleMiddleware.php` | Role-based access control |
| `app/Actions/Fortify/` | User creation & password reset logic |
| `config/fortify.php` | Auth guard, home path, features (2FA, email verification) |
| `config/services.php` | OAuth credentials (Google client_id, secret) |
| `resources/views/livewire/` | All reactive components |
| `resources/views/landing.blade.php` | Luxury landing page design |
| `vite.config.js` | Frontend build config (Tailwind, assets) |
| `.env.example` | Environment variables (GOOGLE_CLIENT_ID, etc.) |

---

## Common Tasks

### Adding a New Authenticated Page
1. Create Volt component: `resources/views/livewire/pages/{name}.blade.php`
2. Register route: `Volt::route('pages/{route}', 'pages.{name}')->middleware(['auth'])->name('page.name');`
3. Use `<x-layouts.app>` wrapper and Flux components

### Modifying User Model
1. Update `app/Models/User.php` - add cast/attribute if needed
2. Create migration: `php artisan make:migration add_field_to_users_table`
3. Add migration logic to `up()` and `down()` methods
4. Run `php artisan migrate`

### Adding Manager-Only Features
1. Create route with `role:manager` middleware
2. Add conditional in dashboard: `if (auth()->user()->role === 'manager') redirect(...)`
3. Use `RoleMiddleware::class` for protected routes

### Testing New Features
1. Create test in `tests/Feature/` using Pest
2. Use `User::factory()` for test data
3. Use `$this->actingAs($user)` for authenticated requests
4. Run `./vendor/bin/pest --watch` during development

---

## Environment & Build System

- **PHP**: 8.2+ required
- **Node**: For npm/Vite
- **Vite**: Hot module replacement on `npm run dev`
- **.env variables**: Copy `.env.example`, configure DB, Google OAuth credentials, mail service
- **Database**: Uses default connection from `.env`

---

## Known Integration Points & Dependencies

- **Fortify**: Provides authentication scaffolding (views, actions, routes)
- **Socialite**: Google OAuth integration
- **Volt**: View-based Livewire components (replaces class-based components)
- **Flux**: Pre-built UI component library (input, checkbox, button, etc.)
- **Tailwind CSS 4**: Integrated via Vite; custom color scheme in landing design
- **Two-Factor**: Handled by Fortify + User model TOTP columns
