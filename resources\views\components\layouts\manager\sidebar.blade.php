@props(['title' => null])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    @include('partials.head')
    @if($title)
        <title>{{ $title }} - {{ config('app.name') }}</title>
    @endif
</head>
<body class="min-h-screen bg-white">
    <flux:sidebar sticky stashable class="bg-white border-r border-[#EFEFEF] shadow-sm">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        {{-- Logo and Brand Section --}}
        <div class="mb-6 pb-6 border-b border-[#EFEFEF]">
            <a href="{{ route('manager.dashboard') }}" class="flex items-center gap-3 group" wire:navigate>
                {{-- Brand Icon with Gradient --}}
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#C85E78] flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-200">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                </div>
                <div class="flex flex-col">
                    <span class="font-body text-xl font-bold text-[#2C2C34]">InStyle</span>
                    <span class="font-body text-xs text-[#8B5D66]">Manager Portal</span>
                </div>
            </a>
        </div>

        {{-- Main Navigation --}}
        <flux:navlist variant="outline">
            <flux:navlist.group :heading="__('Management')" class="space-y-1">
                <flux:navlist.item 
                    icon="sparkles" 
                    :href="route('manager.dashboard')" 
                    :current="request()->routeIs('manager.dashboard')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg"
                    :class="request()->routeIs('manager.dashboard') ? 'text-[#E98CA5] bg-[#F7E9E6] border-l-2 border-[#E98CA5] font-medium' : 'text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50'">
                    {{ __('Dashboard') }}
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="calendar" 
                    :href="route('manager.appointments.index')"
                    :current="request()->routeIs('manager.appointments.*')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg"
                    :class="request()->routeIs('manager.appointments.*') ? 'text-[#E98CA5] bg-[#F7E9E6] border-l-2 border-[#E98CA5] font-medium' : 'text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50'">
                    <div class="flex items-center justify-between w-full">
                        <span>{{ __('Appointments') }}</span>
                    </div>
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="users" 
                    :href="route('manager.clients.index')"
                    :current="request()->routeIs('manager.clients.*')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg"
                    :class="request()->routeIs('manager.clients.*') ? 'text-[#E98CA5] bg-[#F7E9E6] border-l-2 border-[#E98CA5] font-medium' : 'text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50'">
                    {{ __('Clients') }}
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="scissors" 
                    :href="route('manager.services.index')"
                    :current="request()->routeIs('manager.services.*')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg"
                    :class="request()->routeIs('manager.services.*') ? 'text-[#E98CA5] bg-[#F7E9E6] border-l-2 border-[#E98CA5] font-medium' : 'text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50'">
                    {{ __('Services') }}
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="user-group" 
                    :href="route('manager.team.index')"
                    :current="request()->routeIs('manager.team.*')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg"
                    :class="request()->routeIs('manager.team.*') ? 'text-[#E98CA5] bg-[#F7E9E6] border-l-2 border-[#E98CA5] font-medium' : 'text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50'">
                    {{ __('Team') }}
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="chart-bar" 
                    href="#"
                    class="transition-all duration-200 rounded-lg text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50">
                    {{ __('Reports') }}
                </flux:navlist.item>

                <flux:navlist.item 
                    icon="cog" 
                    :href="route('manager.profile.edit')"
                    wire:navigate
                    class="transition-all duration-200 rounded-lg text-[#4A4A52] hover:text-[#2C2C34] hover:bg-[#F7E9E6]/50">
                    {{ __('Settings') }}
                </flux:navlist.item>
            </flux:navlist.group>
        </flux:navlist>

        <flux:spacer />

        {{-- User Profile Section --}}
        <flux:dropdown class="hidden lg:block" position="bottom" align="start">
            <flux:profile 
                :name="auth()->user()->name" 
                :initials="auth()->user()->initials()"
                icon:trailing="chevrons-up-down" 
                class="hover:bg-[#F7E9E6]/80 transition-colors rounded-lg"
            />

            <flux:menu class="w-[220px]">
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span class="flex h-full w-full items-center justify-center rounded-lg bg-[#E98CA5]/10 text-[#E98CA5] font-semibold">
                                    {{ auth()->user()->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-start text-sm leading-tight">
                                <span class="truncate font-semibold text-[#2C2C34]">{{ auth()->user()->name }}</span>
                                <span class="truncate text-xs text-[#4A4A52]">{{ auth()->user()->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('profile.edit')" icon="user" wire:navigate class="hover:bg-[#F7E9E6]">
                        {{ __('Profile') }}
                    </flux:menu.item>
                    <flux:menu.item :href="route('manager.profile.edit')" icon="cog" wire:navigate class="hover:bg-[#F7E9E6]">
                        {{ __('Settings') }}
                    </flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full hover:bg-red-50 hover:text-red-600">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>
    </flux:sidebar>

    {{-- Mobile User Menu --}}
    <flux:header class="lg:hidden bg-white border-b border-[#DCC7A1]/20">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <flux:spacer />

        <flux:dropdown position="top" align="end">
            <flux:profile :initials="auth()->user()->initials()" icon-trailing="chevron-down" />

            <flux:menu>
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span class="flex h-full w-full items-center justify-center rounded-lg bg-[#E98CA5]/10 text-[#E98CA5] font-semibold">
                                    {{ auth()->user()->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-start text-sm leading-tight">
                                <span class="truncate font-semibold text-[#2C2C34]">{{ auth()->user()->name }}</span>
                                <span class="truncate text-xs text-[#4A4A52]">{{ auth()->user()->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('profile.edit')" icon="user" wire:navigate>{{ __('Profile') }}</flux:menu.item>
                    <flux:menu.item :href="route('manager.profile.edit')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>
    </flux:header>

    {{ $slot }}

    @fluxScripts
</body>
</html>
