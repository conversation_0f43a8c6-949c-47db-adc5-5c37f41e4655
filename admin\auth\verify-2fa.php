<?php
/**
 * Admin 2FA Verification Page
 * Flix Salon & SPA - Enhanced Security
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user needs 2FA verification
if (!sessionRequires2FA() || !isset($_SESSION['pending_2fa_admin_id'])) {
    redirect('/admin/services/');
}

$adminId = $_SESSION['pending_2fa_admin_id'];
$error = '';
$success = '';
$showBackupForm = false;

// Get admin details
$admin = $database->fetch(
    "SELECT name, email FROM users WHERE id = ? AND role = 'ADMIN'",
    [$adminId]
);

if (!$admin) {
    clear2FASessionData();
    redirect('/auth/login.php');
}

// Get 2FA settings
$settings = getAdmin2FASettings($adminId);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'verify_email_code') {
        $code = trim($_POST['code'] ?? '');
        
        if (empty($code)) {
            $error = 'Please enter the verification code';
        } else {
            $result = verifyAdmin2FAEmailCode($adminId, $code);

            if ($result['success']) {
                // Complete 2FA verification and create full session
                $authResult = $auth->complete2FAVerification();

                if ($authResult['success']) {
                    log2FAAction($adminId, '2FA_LOGIN_SUCCESS', 'Admin successfully completed 2FA verification');
                    redirect('/admin/services/');
                } else {
                    $error = $authResult['error'];
                }
            } else {
                $error = $result['error'];
            }
        }
    }
    
    elseif ($action === 'verify_backup_code') {
        $backupCode = trim($_POST['backup_code'] ?? '');
        
        if (empty($backupCode)) {
            $error = 'Please enter a backup code';
        } else {
            $result = verifyAdminBackupCode($adminId, $backupCode);

            if ($result['success']) {
                // Complete 2FA verification and create full session
                $authResult = $auth->complete2FAVerification();

                if ($authResult['success']) {
                    log2FAAction($adminId, '2FA_BACKUP_LOGIN_SUCCESS', 'Admin successfully used backup code for 2FA verification');

                    if ($result['remaining_codes'] <= 2) {
                        $_SESSION['backup_codes_warning'] = "Warning: You have only {$result['remaining_codes']} backup codes remaining. Please generate new ones.";
                    }

                    redirect('/admin/services/');
                } else {
                    $error = $authResult['error'];
                }
            } else {
                $error = $result['error'];
            }
        }
    }
    
    elseif ($action === 'send_email_code') {
        $result = sendAdmin2FAEmailCode($adminId);
        
        if ($result['success']) {
            $success = 'Verification code sent to your email address';
        } else {
            $error = $result['error'];
        }
    }
    
    elseif ($action === 'show_backup_form') {
        $showBackupForm = true;
    }
}

// Auto-send email code if email 2FA is enabled
if ($settings['email_2fa_enabled'] && !isset($_POST['action'])) {
    $result = sendAdmin2FAEmailCode($adminId);
    if ($result['success']) {
        $success = 'Verification code sent to your email address';
    }
}

include __DIR__ . '/../../includes/header.php';
?>

<!-- Modern Auth Page Styles - Company Theme -->
<style>
    /* Auth page background with company gradient */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(73, 167, 92, 0.08) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(88, 148, 210, 0.06) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Modern glass container */
    .auth-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(73, 167, 92, 0.15);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(73, 167, 92, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(73, 167, 92, 0.3), transparent);
    }

    /* Modern verification code input */
    .verification-code-input {
        font-size: 24px;
        letter-spacing: 8px;
        text-align: center;
        font-family: 'Courier New', monospace;
        background: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(203, 213, 225, 0.8) !important;
        color: #1f2937 !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .verification-code-input:focus {
        background: rgba(255, 255, 255, 1) !important;
        border-color: rgba(73, 167, 92, 0.6) !important;
        box-shadow:
            0 0 0 3px rgba(73, 167, 92, 0.1),
            0 4px 12px rgba(73, 167, 92, 0.15);
        transform: translateY(-1px);
    }

    /* Modern security icon */
    .security-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #49a75c, #5894d2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
        border: 2px solid rgba(73, 167, 92, 0.2);
    }

    /* Modern button styling */
    .btn-primary {
        background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%);
        border: 1px solid rgba(73, 167, 92, 0.3);
        box-shadow:
            0 4px 15px rgba(73, 167, 92, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        color: white !important;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5cb85c 0%, #6ba4e0 100%);
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(73, 167, 92, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(73, 167, 92, 0.6);
    }

    .btn-primary:hover::before {
        left: 100%;
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
    }

    /* Text colors for light theme */
    .auth-container h2,
    .auth-container h3,
    .auth-container label {
        color: #1f2937 !important;
    }

    .auth-container p {
        color: #4b5563 !important;
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: #49a75c;
    }

    a:hover {
        color: #5894d2;
        text-shadow: 0 0 8px rgba(73, 167, 92, 0.3);
    }

    /* Company brand colors for icons */
    .text-salon-gold {
        color: #49a75c !important;
    }

    .bg-salon-gold {
        background-color: #49a75c !important;
    }

    .hover\:bg-yellow-500:hover {
        background-color: #5894d2 !important;
    }

    .focus\:ring-salon-gold:focus {
        --tw-ring-color: rgba(73, 167, 92, 0.5) !important;
    }

    /* Input styling for backup code */
    input[type="text"] {
        background: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(203, 213, 225, 0.8) !important;
        color: #1f2937 !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    input[type="text"]:focus {
        background: rgba(255, 255, 255, 1) !important;
        border-color: rgba(73, 167, 92, 0.6) !important;
        box-shadow:
            0 0 0 3px rgba(73, 167, 92, 0.1),
            0 4px 12px rgba(73, 167, 92, 0.15);
        transform: translateY(-1px);
    }

    /* Alert styling */
    .bg-red-500\/20 {
        background-color: rgba(239, 68, 68, 0.1) !important;
    }

    .border-red-500\/50 {
        border-color: rgba(239, 68, 68, 0.3) !important;
    }

    .text-red-200 {
        color: #dc2626 !important;
    }

    .bg-green-500\/20 {
        background-color: rgba(16, 185, 129, 0.1) !important;
    }

    .border-green-500\/50 {
        border-color: rgba(16, 185, 129, 0.3) !important;
    }

    .text-green-200 {
        color: #059669 !important;
    }
</style>

<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container rounded-2xl p-8 sm:p-10">
            <!-- Security Icon -->
            <div class="security-icon">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V6a4 4 0 00-8 0v3"></path>
                </svg>
            </div>

            <div class="text-center mb-8">
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Two-Factor Authentication</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    Welcome back, <?= htmlspecialchars($admin['name']) ?>. Please verify your identity to continue.
                </p>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 rounded-lg mb-6">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-3 rounded-lg mb-6">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if (!$showBackupForm && $settings['email_2fa_enabled']): ?>
                <!-- Email Verification Form -->
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="verify_email_code">
                    
                    <div>
                        <label for="code" class="block text-sm font-semibold text-gray-200 mb-2">
                            Enter 6-digit verification code
                        </label>
                        <p class="text-xs text-gray-400 mb-4">
                            Check your email (<?= htmlspecialchars(substr($admin['email'], 0, 3) . '***@' . substr($admin['email'], strpos($admin['email'], '@') + 1)) ?>) for the verification code
                        </p>
                        <input id="code" name="code" type="text" maxlength="6" pattern="[0-9]{6}" required
                               class="verification-code-input appearance-none relative block w-full px-4 py-4 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 backdrop-blur-sm"
                               placeholder="000000" autocomplete="off">
                    </div>

                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-black bg-salon-gold hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 transform hover:scale-105">
                        Verify Code
                    </button>
                </form>

                <div class="mt-6 space-y-4">
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="send_email_code">
                        <button type="submit" class="text-salon-gold hover:text-yellow-400 text-sm transition-colors">
                            Resend verification code
                        </button>
                    </form>

                    <?php if ($settings['backup_codes_enabled']): ?>
                        <div class="text-center">
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="show_backup_form">
                                <button type="submit" class="text-gray-400 hover:text-white text-sm transition-colors">
                                    Use backup code instead
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>

            <?php elseif ($showBackupForm || !$settings['email_2fa_enabled']): ?>
                <!-- Backup Code Form -->
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="verify_backup_code">
                    
                    <div>
                        <label for="backup_code" class="block text-sm font-semibold text-gray-200 mb-2">
                            Enter backup code
                        </label>
                        <p class="text-xs text-gray-400 mb-4">
                            Use one of your 8-character backup codes
                        </p>
                        <input id="backup_code" name="backup_code" type="text" maxlength="8" required
                               class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm text-center font-mono"
                               placeholder="XXXXXXXX" autocomplete="off" style="letter-spacing: 2px;">
                    </div>

                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-black bg-salon-gold hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 transform hover:scale-105">
                        Verify Backup Code
                    </button>
                </form>

                <?php if ($settings['email_2fa_enabled']): ?>
                    <div class="mt-6 text-center">
                        <a href="?" class="text-gray-400 hover:text-white text-sm transition-colors">
                            ← Back to email verification
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="mt-8 text-center">
                <a href="/auth/login.php" class="text-gray-400 hover:text-white text-sm transition-colors">
                    ← Back to login
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus and format verification code input
document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.focus();
        
        codeInput.addEventListener('input', function(e) {
            // Only allow numbers
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Auto-submit when 6 digits entered
            if (this.value.length === 6) {
                this.form.submit();
            }
        });
    }
    
    const backupInput = document.getElementById('backup_code');
    if (backupInput) {
        backupInput.focus();
        
        backupInput.addEventListener('input', function(e) {
            // Convert to uppercase and only allow alphanumeric
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/footer.php'; ?>
