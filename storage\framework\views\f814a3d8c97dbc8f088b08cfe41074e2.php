<?php if (isset($component)) { $__componentOriginalaacc4db8bc37495e56387e4de4703432 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaacc4db8bc37495e56387e4de4703432 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::input.group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::input.group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php echo e($slot); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaacc4db8bc37495e56387e4de4703432)): ?>
<?php $attributes = $__attributesOriginalaacc4db8bc37495e56387e4de4703432; ?>
<?php unset($__attributesOriginalaacc4db8bc37495e56387e4de4703432); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaacc4db8bc37495e56387e4de4703432)): ?>
<?php $component = $__componentOriginalaacc4db8bc37495e56387e4de4703432; ?>
<?php unset($__componentOriginalaacc4db8bc37495e56387e4de4703432); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\Instyle\vendor\livewire\flux\stubs\resources\views\flux\otp\group.blade.php ENDPATH**/ ?>