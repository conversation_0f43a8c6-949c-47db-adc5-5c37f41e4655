<?php
/**
 * Admin Create Customer
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = createCustomer($_POST);
    
    if ($result['success']) {
        $_SESSION['success'] = 'Customer created successfully!';
        redirect('/admin/customers/view.php?id=' . $result['id']);
    } else {
        $_SESSION['error'] = $result['error'];
    }
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Create New Customer";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Create New
                                    <span class="text-redolence-green">Patient</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Add a new patient to the medical system</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        Patient Registration
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Medical Records Setup
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/customers"
                                   class="medical-btn-secondary">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Back to Patients
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'medical-message-success' : 'medical-message-error' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Patient Form -->
                    <div class="medical-card p-8">
                        <form method="POST" id="customerForm">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Medical Personal Information Section -->
                                <div class="lg:col-span-2">
                                    <div class="flex items-center mb-6">
                                        <div class="p-3 rounded-xl bg-gradient-to-br from-redolence-green to-redolence-blue text-white mr-4">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-bold text-redolence-navy">Personal Information</h3>
                                            <p class="text-gray-600 text-sm">Patient identification and contact details</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Full Name -->
                                <div>
                                    <label class="medical-form-label">Full Name *</label>
                                    <input type="text" name="name" id="customerName" required
                                           class="medical-form-input w-full"
                                           placeholder="Enter patient's full name">
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="medical-form-label">Email Address *</label>
                                    <input type="email" name="email" id="customerEmail" required
                                           class="medical-form-input w-full"
                                           placeholder="<EMAIL>">
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="medical-form-label">Phone Number</label>
                                    <input type="tel" name="phone" id="customerPhone"
                                           class="medical-form-input w-full"
                                           placeholder="(*************">
                                </div>

                                <!-- Date of Birth -->
                                <div>
                                    <label class="medical-form-label">Date of Birth</label>
                                    <input type="date" name="date_of_birth" id="customerDOB"
                                           class="medical-form-input w-full">
                                </div>

                                <!-- Medical Account Settings Section -->
                                <div class="lg:col-span-2 mt-8">
                                    <div class="flex items-center mb-6">
                                        <div class="p-3 rounded-xl bg-gradient-to-br from-redolence-blue to-redolence-green text-white mr-4">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-bold text-redolence-navy">Account Settings</h3>
                                            <p class="text-gray-600 text-sm">Security and access configuration</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Password -->
                                <div>
                                    <label class="medical-form-label">Password *</label>
                                    <input type="password" name="password" id="customerPassword" required minlength="6"
                                           class="medical-form-input w-full"
                                           placeholder="Minimum 6 characters">
                                </div>

                                <!-- Confirm Password -->
                                <div>
                                    <label class="medical-form-label">Confirm Password *</label>
                                    <input type="password" name="confirm_password" id="confirmPassword" required minlength="6"
                                           class="medical-form-input w-full"
                                           placeholder="Confirm password">
                                </div>

                                <!-- Initial Points -->
                                <div>
                                    <label class="medical-form-label">Initial Loyalty Points</label>
                                    <input type="number" name="points" id="initialPoints" min="0" value="0"
                                           class="medical-form-input w-full"
                                           placeholder="0">
                                    <p class="text-xs text-gray-600 mt-2 flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Optional welcome bonus points
                                    </p>
                                </div>

                                <!-- Send Welcome Email -->
                                <div>
                                    <label class="medical-form-label">Notifications</label>
                                    <div class="medical-content-card p-4">
                                        <div class="flex items-center">
                                            <input type="checkbox" name="send_welcome_email" id="sendWelcomeEmail" value="1" checked
                                                   class="rounded border-redolence-green/30 text-redolence-green focus:ring-redolence-green/20 focus:ring-2">
                                            <span class="ml-3 text-sm font-medium text-redolence-navy">Send welcome email to patient</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Medical Notes Section -->
                            <div class="mt-8">
                                <div class="flex items-center mb-4">
                                    <div class="p-2 rounded-lg bg-gradient-to-br from-gray-500 to-gray-600 text-white mr-3">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </div>
                                    <label class="medical-form-label mb-0">Medical Notes</label>
                                </div>
                                <textarea name="notes" rows="4"
                                          class="medical-form-input w-full"
                                          placeholder="Any additional notes about the patient's medical history, preferences, or special requirements..."></textarea>
                            </div>

                            <!-- Medical Submit Buttons -->
                            <div class="mt-8 flex flex-col sm:flex-row gap-4">
                                <button type="submit"
                                        class="medical-btn-primary flex-1 justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Create Patient Record
                                </button>
                                <a href="<?= getBasePath() ?>/admin/customers"
                                   class="medical-btn-secondary flex-1 justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('customerForm');
    const password = document.getElementById('customerPassword');
    const confirmPassword = document.getElementById('confirmPassword');

    // Password confirmation validation
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);

    // Form validation
    form.addEventListener('submit', function(e) {
        const requiredFields = ['name', 'email', 'password', 'confirm_password'];
        let isValid = true;

        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Check password match
        if (password.value !== confirmPassword.value) {
            isValid = false;
            confirmPassword.classList.add('border-red-500');
            alert('Passwords do not match.');
        }

        // Check email format
        const emailField = document.querySelector('[name="email"]');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            isValid = false;
            emailField.classList.add('border-red-500');
            alert('Please enter a valid email address.');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
        }
    });

    // Auto-generate password option
    const generatePasswordBtn = document.createElement('button');
    generatePasswordBtn.type = 'button';
    generatePasswordBtn.className = 'mt-2 text-sm text-blue-400 hover:text-blue-300';
    generatePasswordBtn.textContent = 'Generate secure password';
    generatePasswordBtn.addEventListener('click', function() {
        const generatedPassword = generateSecurePassword();
        password.value = generatedPassword;
        confirmPassword.value = generatedPassword;
        validatePasswords();
    });
    
    password.parentNode.appendChild(generatePasswordBtn);
});

function generateSecurePassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
