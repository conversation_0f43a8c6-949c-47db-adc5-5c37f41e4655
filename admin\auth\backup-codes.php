<?php
/**
 * Admin 2FA Backup Codes Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/admin/login.php');
}

$adminId = $_SESSION['user_id'];
$message = '';
$messageType = '';
$showCodes = false;
$backupCodes = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';

        // Set 2FA verification flag to prevent logout during operations
        $_SESSION['is_2fa_verified'] = true;

        switch ($action) {
            case 'generate_backup_codes':
                // Generate new backup codes
                $newCodes = generateBackupCodes(10);
                $result = storeAdminBackupCodes($adminId, $newCodes);

                if ($result['success']) {
                    $backupCodes = $newCodes;
                    $showCodes = true;
                    $message = 'New backup codes generated successfully. Please save them in a secure location.';
                    $messageType = 'success';
                } else {
                    throw new Exception($result['error']);
                }
                break;

            case 'download_codes':
                if (isset($_SESSION['backup_codes_download'])) {
                    $codes = $_SESSION['backup_codes_download'];
                    unset($_SESSION['backup_codes_download']);

                    header('Content-Type: text/plain');
                    header('Content-Disposition: attachment; filename="flix-backup-codes-' . date('Y-m-d') . '.txt"');

                    echo "Flix Salon & SPA - 2FA Backup Codes\n";
                    echo "Generated: " . date('Y-m-d H:i:s') . "\n";
                    echo "Admin: " . $_SESSION['user_name'] . "\n";
                    echo str_repeat("=", 50) . "\n\n";
                    echo "IMPORTANT: Save these codes in a secure location.\n";
                    echo "Each code can only be used once.\n\n";

                    foreach ($codes as $i => $code) {
                        echo ($i + 1) . ". " . $code . "\n";
                    }

                    echo "\n" . str_repeat("=", 50) . "\n";
                    echo "Keep these codes secure and accessible only to you.\n";
                    exit;
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
        error_log("Backup codes error for admin $adminId: " . $e->getMessage());
    }
}

// Get current 2FA settings
$twoFASettings = getAdmin2FASettings($adminId);
$remainingCodes = getRemainingBackupCodesCount($adminId);

// Store codes for download if they were just generated
if ($showCodes && !empty($backupCodes)) {
    $_SESSION['backup_codes_download'] = $backupCodes;
}

$pageTitle = "Backup Codes Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Backup Codes CSS -->
<style>
.medical-backup-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.08);
}

.medical-backup-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    transition: left 0.6s ease;
}

.medical-backup-card:hover::before {
    left: 100%;
}

.medical-backup-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px rgba(73, 167, 92, 0.12);
}

.medical-code-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.medical-code-item {
    background: rgba(248, 250, 252, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: bold;
    color: #1e3a8a;
    transition: all 0.3s ease;
}

.medical-code-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(73, 167, 92, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.15);
}

.medical-status-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.medical-status-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-btn-primary-enhanced {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.medical-btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary-enhanced {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 0.75rem 1.5rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.medical-btn-secondary-enhanced:hover {
    background: rgba(73, 167, 92, 0.1);
    border-color: #49A75C;
    transform: translateY(-1px);
}

.medical-alert-enhanced {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.medical-alert-success {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
    border-color: #16a34a;
}

.medical-alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}

.medical-icon-enhanced {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="medical-backup-card p-8 mb-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="medical-icon-enhanced">
                                    <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-3xl font-bold text-redolence-navy">Backup Codes Management</h1>
                                    <p class="mt-2 text-gray-600">Manage your two-factor authentication backup codes</p>
                                </div>
                            </div>
                            <div>
                                <a href="<?= getBasePath() ?>/admin/profile" class="medical-btn-secondary-enhanced">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                    Back to Profile
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="medical-alert-enhanced medical-alert-<?= $messageType ?>">
                            <?php if ($messageType === 'success'): ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            <?php else: ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                </svg>
                            <?php endif; ?>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Current Status -->
                    <div class="medical-backup-card p-8 mb-8">
                        <h2 class="text-2xl font-bold text-redolence-navy mb-6 flex items-center">
                            <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                            Current Status
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="medical-status-card text-center">
                                <div class="text-sm font-semibold text-gray-600 mb-2">2FA Status</div>
                                <div class="text-2xl font-bold <?php echo $twoFASettings['is_enabled'] ? 'text-green-600' : 'text-red-600'; ?>">
                                    <?php echo $twoFASettings['is_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                </div>
                            </div>
                            <div class="medical-status-card text-center">
                                <div class="text-sm font-semibold text-gray-600 mb-2">Backup Codes</div>
                                <div class="text-2xl font-bold <?php echo $twoFASettings['backup_codes_enabled'] ? 'text-green-600' : 'text-gray-500'; ?>">
                                    <?php echo $twoFASettings['backup_codes_enabled'] ? 'Generated' : 'Not Generated'; ?>
                                </div>
                            </div>
                            <div class="medical-status-card text-center">
                                <div class="text-sm font-semibold text-gray-600 mb-2">Remaining Codes</div>
                                <div class="text-2xl font-bold text-redolence-green">
                                    <?php echo $remainingCodes; ?> / 10
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Codes Display -->
                    <?php if ($showCodes && !empty($backupCodes)): ?>
                        <div class="medical-backup-card p-8 mb-8">
                            <div class="flex items-start mb-6">
                                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-redolence-navy mb-2">Your New Backup Codes</h3>
                                    <p class="text-gray-600">
                                        Save these codes in a secure location. Each code can only be used once and won't be shown again.
                                    </p>
                                </div>
                            </div>

                            <div class="medical-code-grid mb-6">
                                <?php foreach ($backupCodes as $i => $code): ?>
                                    <div class="medical-code-item">
                                        <span class="text-gray-500 text-sm"><?php echo $i + 1; ?>.</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($code); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="flex flex-col sm:flex-row gap-4">
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="download_codes">
                                    <button type="submit" class="medical-btn-primary-enhanced">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                        Download as Text File
                                    </button>
                                </form>
                                <button onclick="copyAllCodes()" class="medical-btn-secondary-enhanced">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                    Copy All Codes
                                </button>
                                <button onclick="printBackupCodes()" class="medical-btn-secondary-enhanced">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                                    </svg>
                                    Print Codes
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Generate New Codes -->
                    <div class="medical-backup-card p-8">
                        <h2 class="text-2xl font-bold text-redolence-navy mb-6 flex items-center">
                            <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            Generate Backup Codes
                        </h2>

                        <?php if ($twoFASettings['backup_codes_enabled']): ?>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-red-800 font-semibold text-lg mb-2">Important Warning</h4>
                                        <p class="text-red-700">
                                            Generating new backup codes will invalidate all existing codes. Make sure to save the new codes securely before proceeding.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="flex flex-col sm:flex-row items-start justify-between gap-6">
                            <div class="flex-1">
                                <form method="POST" onsubmit="return confirmGeneration()">
                                    <input type="hidden" name="action" value="generate_backup_codes">
                                    <button type="submit" class="medical-btn-primary-enhanced">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                        Generate New Backup Codes
                                    </button>
                                </form>
                            </div>

                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md">
                                <h4 class="text-blue-800 font-semibold mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Backup Code Information
                                </h4>
                                <ul class="text-blue-700 text-sm space-y-2">
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Allow access if you lose your primary 2FA device
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Each code can only be used once
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Store them in a secure location
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Generate new codes if compromised
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function confirmGeneration() {
    <?php if ($twoFASettings['backup_codes_enabled']): ?>
    return confirm('This will invalidate all existing backup codes. Are you sure you want to generate new ones?');
    <?php else: ?>
    return confirm('Generate 10 new backup codes for your account?');
    <?php endif; ?>
}

function copyAllCodes() {
    const codes = <?php echo json_encode($backupCodes ?? []); ?>;
    const text = codes.map((code, index) => `${index + 1}. ${code}`).join('\n');

    navigator.clipboard.writeText(text).then(() => {
        showNotification('All backup codes copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('All backup codes copied to clipboard!', 'success');
    });
}

function printBackupCodes() {
    window.print();
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.style.transform = 'translateX(full)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>