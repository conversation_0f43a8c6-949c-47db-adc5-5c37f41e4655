<?php
/**
 * Rooms Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/room_functions.php';

// Require medical admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createRoom($_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Room created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'update':
            $result = updateRoom($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Room updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'delete':
            $result = deleteRoom($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Room deleted successfully!';
            } else {
                $_SESSION['delete_error'] = json_encode($result);
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/rooms');
}

// Get filters
$filters = [
    'status' => $_GET['status'] ?? '',
    'type' => $_GET['type'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// Get all rooms with filters
$rooms = getAllRooms($filters);

// Get room types and statuses for filters
$roomTypes = getRoomTypes();
$roomStatuses = getRoomStatuses();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Rooms Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Rooms Management CSS -->
<style>
/* Medical Rooms Management Specific Styles */
.medical-rooms-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-rooms-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-rooms-card:hover::before {
    left: 100%;
}

.medical-rooms-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-room-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-room-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-room-item:hover::before {
    transform: scaleX(1);
}

.medical-room-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-room-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.room-type-treatment {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.room-type-consultation {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.room-type-procedure {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.room-type-recovery {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.room-type-vip {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-status-available {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-status-unavailable {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-status-maintenance {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

@media (max-width: 768px) {
    .medical-rooms-grid {
        grid-template-columns: 1fr;
    }

    .medical-rooms-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-rooms-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Treatment Rooms
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive facility and room management system</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= count($rooms) ?> Total Rooms
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Room Assignment
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openCreateModal()"
                                        class="medical-btn-primary text-lg px-6 py-3">
                                    + Add New Room
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Rooms</h2>
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>"
                                       placeholder="Search rooms..."
                                       class="medical-form-input w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Room Type</label>
                                <select name="type" class="medical-form-input w-full">
                                    <option value="">All Types</option>
                                    <?php foreach ($roomTypes as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $filters['type'] === $value ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input w-full">
                                    <option value="">All Statuses</option>
                                    <?php foreach ($roomStatuses as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $filters['status'] === $value ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button type="submit" class="w-full medical-btn-primary">
                                    Apply Filters
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Medical Rooms Grid -->
                    <?php if (empty($rooms)): ?>
                        <div class="medical-rooms-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Treatment Rooms Found</h3>
                                <p class="text-gray-600 mb-6">Get started by creating your first treatment room to manage appointments and facilities.</p>
                                <button onclick="openCreateModal()" class="medical-btn-primary">
                                    Create Your First Room
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="medical-rooms-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                            <?php foreach ($rooms as $room): ?>
                                <div class="medical-room-item">
                                    <!-- Room Header -->
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex-1">
                                            <h3 class="text-xl font-bold text-redolence-navy mb-1">
                                                <?= htmlspecialchars($room['name']) ?>
                                            </h3>
                                            <?php if ($room['description']): ?>
                                                <p class="text-gray-600 text-sm">
                                                    <?= htmlspecialchars($room['description']) ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex gap-2 ml-4">
                                            <button onclick="openEditModal('<?= $room['id'] ?>')"
                                                    class="medical-btn-secondary text-sm px-3 py-1">
                                                Edit
                                            </button>
                                            <button onclick="deleteRoom('<?= $room['id'] ?>', '<?= htmlspecialchars($room['name']) ?>')"
                                                    class="medical-btn-danger text-sm px-3 py-1">
                                                Delete
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Room Details -->
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Room Type</p>
                                            <span class="medical-room-type-badge room-type-<?= strtolower($room['type']) ?>">
                                                <?= htmlspecialchars($roomTypes[$room['type']] ?? $room['type']) ?>
                                            </span>
                                        </div>
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Capacity</p>
                                            <p class="text-redolence-navy font-bold">
                                                <?= htmlspecialchars($room['capacity']) ?> person<?= $room['capacity'] > 1 ? 's' : '' ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Room Status & Date -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Status</p>
                                            <span class="medical-room-type-badge medical-status-<?= strtolower($room['status']) ?>">
                                                <?= htmlspecialchars($roomStatuses[$room['status']] ?? $room['status']) ?>
                                            </span>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Created</p>
                                            <p class="text-gray-600 text-sm">
                                                <?= date('M j, Y', strtotime($room['created_at'])) ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Medical Room Modal -->
<div id="roomModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm hidden z-50 transition-all duration-300">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="medical-card max-w-lg w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
            <!-- Compact Modal Header -->
            <div class="relative p-6 border-b border-redolence-green/10 bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-2 rounded-lg bg-gradient-to-br from-redolence-green to-redolence-blue text-white mr-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 id="modalTitle" class="text-xl font-bold text-redolence-navy">Add New Room</h3>
                            <p class="text-xs text-gray-600">Configure room details</p>
                        </div>
                    </div>
                    <button onclick="closeModal()" class="p-2 rounded-lg text-gray-400 hover:text-redolence-green hover:bg-redolence-green/10 transition-all duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Compact Form Content -->
            <div class="p-6">
                <form id="roomForm" method="POST">
                    <input type="hidden" name="action" id="formAction" value="create">
                    <input type="hidden" name="id" id="roomId" value="">

                    <div class="space-y-4">
                        <div>
                            <label class="medical-form-label">Room Name *</label>
                            <input type="text" name="name" id="roomName" required
                                   class="medical-form-input w-full"
                                   placeholder="e.g., Treatment Room A">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="medical-form-label">Room Type *</label>
                                <select name="type" id="roomType" required class="medical-form-input w-full">
                                    <?php foreach ($roomTypes as $value => $label): ?>
                                        <option value="<?= $value ?>"><?= htmlspecialchars($label) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div>
                                <label class="medical-form-label">Status</label>
                                <select name="status" id="roomStatus" class="medical-form-input w-full">
                                    <?php foreach ($roomStatuses as $value => $label): ?>
                                        <option value="<?= $value ?>"><?= htmlspecialchars($label) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="medical-form-label">Capacity</label>
                            <input type="number" name="capacity" id="roomCapacity" min="1" value="1"
                                   class="medical-form-input w-full"
                                   placeholder="Maximum patients">
                        </div>

                        <div>
                            <label class="medical-form-label">Description</label>
                            <textarea name="description" id="roomDescription" rows="3"
                                      class="medical-form-input w-full resize-none"
                                      placeholder="Room features and equipment..."></textarea>
                        </div>
                    </div>

                    <!-- Compact Action Buttons -->
                    <div class="flex gap-3 mt-6 pt-4 border-t border-redolence-green/10">
                        <button type="button" onclick="closeModal()" class="medical-btn-secondary flex-1">
                            Cancel
                        </button>
                        <button type="submit" class="medical-btn-primary flex-1">
                            <span id="submitButtonText">Save Room</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Room data for editing
const roomsData = <?= json_encode($rooms) ?>;

function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add New Room';
    document.getElementById('modalTitle').nextElementSibling.textContent = 'Configure room details';
    document.getElementById('formAction').value = 'create';
    document.getElementById('roomId').value = '';
    document.getElementById('submitButtonText').textContent = 'Create Room';
    document.getElementById('roomForm').reset();

    // Show modal with animation
    const modal = document.getElementById('roomModal');
    const modalContent = document.getElementById('modalContent');
    modal.classList.remove('hidden');

    // Trigger animation
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
}

function openEditModal(roomId) {
    const room = roomsData.find(r => r.id === roomId);
    if (!room) return;

    document.getElementById('modalTitle').textContent = 'Edit Room';
    document.getElementById('modalTitle').nextElementSibling.textContent = 'Update room details';
    document.getElementById('formAction').value = 'update';
    document.getElementById('roomId').value = room.id;
    document.getElementById('submitButtonText').textContent = 'Update Room';
    document.getElementById('roomName').value = room.name;
    document.getElementById('roomType').value = room.type;
    document.getElementById('roomCapacity').value = room.capacity;
    document.getElementById('roomStatus').value = room.status;
    document.getElementById('roomDescription').value = room.description || '';

    // Show modal with animation
    const modal = document.getElementById('roomModal');
    const modalContent = document.getElementById('modalContent');
    modal.classList.remove('hidden');

    // Trigger animation
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
}

function closeModal() {
    const modal = document.getElementById('roomModal');
    const modalContent = document.getElementById('modalContent');

    // Animate out
    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');

    // Hide modal after animation
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

function deleteRoom(roomId, roomName) {
    if (confirm(`Are you sure you want to delete "${roomName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${roomId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('roomModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Handle delete errors
<?php if (isset($_SESSION['delete_error'])): ?>
    const deleteError = <?= $_SESSION['delete_error'] ?>;
    if (deleteError && !deleteError.success && deleteError.booking_count) {
        alert(`Cannot delete room: ${deleteError.error}\n\nThis room has ${deleteError.booking_count} active or future booking(s).`);
    }
    <?php unset($_SESSION['delete_error']); ?>
<?php endif; ?>
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
