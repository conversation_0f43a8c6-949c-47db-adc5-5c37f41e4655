<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ManagerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager',
                'role' => 'manager',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
    }
}
