<div class="p-6 space-y-6">
    
    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if(session()->has('success')): ?>
        <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>

    
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Appointments Calendar</h1>
            <p class="text-gray-600 mt-1">Visual calendar view for appointment management</p>
        </div>
        <a href="<?php echo e(route('manager.appointments.index')); ?>"
            class="inline-flex items-center gap-2 px-4 py-2 bg-white border border-[#EFEFEF] text-[#2C2C34] rounded-lg hover:bg-[#F7E9E6] transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
            List View
        </a>
    </div>

    
    <div class="bg-white rounded-xl shadow-sm border border-[#EFEFEF] p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            
            <div class="flex items-center gap-4">
                
                <div class="inline-flex rounded-lg border border-[#EFEFEF] p-1 bg-gray-50">
                    <button wire:click="changeView('month')"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors <?php echo e($view === 'month' ? 'bg-white text-[#B48B5B] shadow-sm' : 'text-gray-600 hover:text-gray-900'); ?>">
                        Month
                    </button>
                    <button wire:click="changeView('week')"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors <?php echo e($view === 'week' ? 'bg-white text-[#B48B5B] shadow-sm' : 'text-gray-600 hover:text-gray-900'); ?>">
                        Week
                    </button>
                    <button wire:click="changeView('day')"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors <?php echo e($view === 'day' ? 'bg-white text-[#B48B5B] shadow-sm' : 'text-gray-600 hover:text-gray-900'); ?>">
                        Day
                    </button>
                </div>

                
                <div class="flex items-center gap-2">
                    <button wire:click="previousPeriod" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>

                    <div class="text-center min-w-[200px]">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <?php echo e(\Carbon\Carbon::parse($currentDate)->format($view === 'day' ? 'F d, Y' : 'F Y')); ?>

                        </h2>
                    </div>

                    <button wire:click="nextPeriod" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>

                    <button wire:click="goToToday"
                        class="ml-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-colors">
                        Today
                    </button>
                </div>
            </div>

            
            <div class="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-end">
                
                <select wire:model.live="statusFilter"
                    class="px-3 py-2 border border-[#EFEFEF] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#B48B5B]">
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="no_show">No Show</option>
                </select>

                
                <select wire:model.live="staffFilter"
                    class="px-3 py-2 border border-[#EFEFEF] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#B48B5B]">
                    <option value="all">All Staff</option>
                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                </select>

                
                <button onclick="showCreateModal()"
                    class="px-4 py-2 bg-[#B48B5B] hover:bg-[#9A7449] text-white rounded-lg font-medium transition-colors flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    New Appointment
                </button>
            </div>
        </div>

        
        <div class="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4">
            <div class="bg-blue-50 rounded-lg p-3 border border-blue-100">
                <div class="text-2xl font-bold text-blue-600"><?php echo e($stats['total']); ?></div>
                <div class="text-xs text-blue-600">Total</div>
            </div>
            <div class="bg-yellow-50 rounded-lg p-3 border border-yellow-100">
                <div class="text-2xl font-bold text-yellow-600"><?php echo e($stats['pending']); ?></div>
                <div class="text-xs text-yellow-600">Pending</div>
            </div>
            <div class="bg-green-50 rounded-lg p-3 border border-green-100">
                <div class="text-2xl font-bold text-green-600"><?php echo e($stats['confirmed']); ?></div>
                <div class="text-xs text-green-600">Confirmed</div>
            </div>
            <div class="bg-purple-50 rounded-lg p-3 border border-purple-100">
                <div class="text-2xl font-bold text-purple-600"><?php echo e($stats['completed']); ?></div>
                <div class="text-xs text-purple-600">Completed</div>
            </div>
            <div class="bg-red-50 rounded-lg p-3 border border-red-100">
                <div class="text-2xl font-bold text-red-600"><?php echo e($stats['cancelled']); ?></div>
                <div class="text-xs text-red-600">Cancelled</div>
            </div>
        </div>
    </div>

    
    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($view === 'month'): ?>
        
        <div class="bg-white rounded-xl shadow-sm border border-[#EFEFEF] overflow-hidden">
            
            <div class="grid grid-cols-7 bg-gray-50 border-b border-[#EFEFEF]">
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-3 text-center text-sm font-semibold text-gray-700"><?php echo e($day); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>

            
            <div class="grid grid-cols-7">
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $calendarDays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div wire:click="selectDate('<?php echo e($day['date']); ?>')" ondrop="handleDrop(event, '<?php echo e($day['date']); ?>')"
                        ondragover="handleDragOver(event)"
                        class="min-h-[120px] border-b border-r border-[#EFEFEF] p-2 cursor-pointer hover:bg-gray-50 transition-colors
                                                                                        <?php echo e(!$day['isCurrentMonth'] ? 'bg-gray-50' : ''); ?>

                                                                                        <?php echo e($day['isToday'] ? 'bg-blue-50' : ''); ?>

                                                                                        <?php echo e($selectedDate === $day['date'] ? 'ring-2 ring-[#B48B5B] ring-inset' : ''); ?>">

                        <div
                            class="text-sm font-medium mb-1
                                                                                            <?php echo e(!$day['isCurrentMonth'] ? 'text-gray-400' : ''); ?>

                                                                                            <?php echo e($day['isToday'] ? 'text-blue-600' : 'text-gray-700'); ?>">
                            <?php echo e($day['day']); ?>

                        </div>

                        
                        <div class="space-y-1">
                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $this->getAppointmentsForDate($day['date']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div draggable="true" ondragstart="handleDragStart(event, <?php echo e($appointment->id); ?>)"
                                    onclick="event.stopPropagation(); showViewModal(<?php echo e($appointment->id); ?>)"
                                    class="text-xs p-1 rounded cursor-move hover:shadow-md transition-shadow
                                                                                                                                <?php echo e($appointment->status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-l-2 border-yellow-500' : ''); ?>

                                                                                                                                <?php echo e($appointment->status === 'confirmed' ? 'bg-green-100 text-green-700 border-l-2 border-green-500' : ''); ?>

                                                                                                                                <?php echo e($appointment->status === 'completed' ? 'bg-purple-100 text-purple-700 border-l-2 border-purple-500' : ''); ?>

                                                                                                                                <?php echo e($appointment->status === 'cancelled' ? 'bg-red-100 text-red-700 border-l-2 border-red-500' : ''); ?>

                                                                                                                                <?php echo e($appointment->status === 'no_show' ? 'bg-gray-100 text-gray-700 border-l-2 border-gray-500' : ''); ?>">
                                    <div class="font-semibold truncate"><?php echo e(substr($appointment->start_time, 0, 5)); ?>

                                        <?php echo e($appointment->client->name); ?>

                                    </div>
                                    <div class="truncate"><?php echo e($appointment->service->name); ?></div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>
        </div>

    <?php elseif($view === 'week'): ?>
        
        <div class="bg-white rounded-xl shadow-sm border border-[#EFEFEF] overflow-hidden">
            
            <div class="grid grid-cols-8 bg-gray-50 border-b border-[#EFEFEF]">
                <div class="p-3 text-sm font-semibold text-gray-700 border-r border-[#EFEFEF]">Time</div>
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $weekDays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-3 text-center border-r border-[#EFEFEF] last:border-r-0">
                        <div class="text-sm font-semibold <?php echo e($day['isToday'] ? 'text-blue-600' : 'text-gray-700'); ?>">
                            <?php echo e($day['dayName']); ?>

                        </div>
                        <div class="text-xs <?php echo e($day['isToday'] ? 'text-blue-600' : 'text-gray-500'); ?>">
                            <?php echo e($day['day']); ?>

                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>

            
            <div class="overflow-y-auto max-h-[600px]">
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($hour = 8; $hour <= 20; $hour++): ?>
                    <div class="grid grid-cols-8 border-b border-[#EFEFEF]">
                        <div class="p-2 text-xs text-gray-500 border-r border-[#EFEFEF] bg-gray-50">
                            <?php echo e(sprintf('%02d:00', $hour)); ?>

                        </div>

                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $weekDays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div ondrop="handleDrop(event, '<?php echo e($day['date']); ?>', '<?php echo e(sprintf('%02d:00', $hour)); ?>')"
                                ondragover="handleDragOver(event)"
                                class="min-h-[60px] p-1 border-r border-[#EFEFEF] hover:bg-gray-50 cursor-pointer relative">

                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $this->getAppointmentsForDate($day['date']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $startHour = (int) substr($appointment->start_time, 0, 2);
                                        if ($startHour === $hour) {
                                    ?>
                                    <div draggable="true" ondragstart="handleDragStart(event, <?php echo e($appointment->id); ?>)"
                                        onclick="showViewModal(<?php echo e($appointment->id); ?>)"
                                        class="text-xs p-2 rounded mb-1 cursor-move hover:shadow-md transition-shadow
                                                                                                                                                                    <?php echo e($appointment->status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-l-2 border-yellow-500' : ''); ?>

                                                                                                                                                                    <?php echo e($appointment->status === 'confirmed' ? 'bg-green-100 text-green-700 border-l-2 border-green-500' : ''); ?>

                                                                                                                                                                    <?php echo e($appointment->status === 'completed' ? 'bg-purple-100 text-purple-700 border-l-2 border-purple-500' : ''); ?>

                                                                                                                                                                    <?php echo e($appointment->status === 'cancelled' ? 'bg-red-100 text-red-700 border-l-2 border-red-500' : ''); ?>">
                                        <div class="font-semibold"><?php echo e(substr($appointment->start_time, 0, 5)); ?></div>
                                        <div class="truncate"><?php echo e($appointment->client->name); ?></div>
                                        <div class="truncate text-[10px]"><?php echo e($appointment->service->name); ?></div>
                                    </div>
                                    <?php
                                        }
                                    ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </div>
                <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>
        </div>

    <?php else: ?>
        
        <div class="bg-white rounded-xl shadow-sm border border-[#EFEFEF] overflow-hidden">
            <div class="p-4 bg-gray-50 border-b border-[#EFEFEF]">
                <h3 class="text-lg font-semibold text-gray-900">
                    <?php echo e(\Carbon\Carbon::parse($currentDate)->format('l, F d, Y')); ?>

                </h3>
            </div>

            <div class="overflow-y-auto max-h-[700px]">
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($hour = 8; $hour <= 20; $hour++): ?>
                    <div class="flex border-b border-[#EFEFEF]">
                        <div class="w-20 p-3 text-sm text-gray-500 bg-gray-50 border-r border-[#EFEFEF]">
                            <?php echo e(sprintf('%02d:00', $hour)); ?>

                        </div>

                        <div ondrop="handleDrop(event, '<?php echo e($currentDate); ?>', '<?php echo e(sprintf('%02d:00', $hour)); ?>')"
                            ondragover="handleDragOver(event)" class="flex-1 min-h-[80px] p-3 hover:bg-gray-50 cursor-pointer">

                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $this->getAppointmentsForDate($currentDate); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $startHour = (int) substr($appointment->start_time, 0, 2);
                                    if ($startHour === $hour) {
                                ?>
                                <div draggable="true" ondragstart="handleDragStart(event, <?php echo e($appointment->id); ?>)"
                                    onclick="showViewModal(<?php echo e($appointment->id); ?>)"
                                    class="p-3 rounded-lg mb-2 cursor-move hover:shadow-lg transition-all
                                                                                                                                    <?php echo e($appointment->status === 'pending' ? 'bg-yellow-100 border-l-4 border-yellow-500' : ''); ?>

                                                                                                                                    <?php echo e($appointment->status === 'confirmed' ? 'bg-green-100 border-l-4 border-green-500' : ''); ?>

                                                                                                                                    <?php echo e($appointment->status === 'completed' ? 'bg-purple-100 border-l-4 border-purple-500' : ''); ?>

                                                                                                                                    <?php echo e($appointment->status === 'cancelled' ? 'bg-red-100 border-l-4 border-red-500' : ''); ?>">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="font-semibold text-gray-900">
                                                <?php echo e(substr($appointment->start_time, 0, 5)); ?> -
                                                <?php echo e(substr($appointment->end_time, 0, 5)); ?>

                                            </div>
                                            <div class="text-sm font-medium text-gray-700 mt-1">
                                                <?php echo e($appointment->client->name); ?>

                                            </div>
                                            <div class="text-sm text-gray-600">
                                                <?php echo e($appointment->service->name); ?>

                                            </div>
                                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($appointment->staff): ?>
                                                <div class="text-xs text-gray-500 mt-1">
                                                    Staff: <?php echo e($appointment->staff->name); ?>

                                                </div>
                                            <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                        </div>
                                        <div>
                                            <?php if (isset($component)) { $__componentOriginal4af424b90c762a172adad803e3194c97 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4af424b90c762a172adad803e3194c97 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.status-badge','data' => ['status' => $appointment->status]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($appointment->status)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $attributes = $__attributesOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__attributesOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $component = $__componentOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__componentOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                    }
                                ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                    </div>
                <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>
        </div>
    <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>

    
    <div class="bg-white rounded-xl shadow-sm border border-[#EFEFEF] p-4">
        <h4 class="text-sm font-semibold text-gray-700 mb-3">Status Legend</h4>
        <div class="flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                <span class="text-sm text-gray-600">Pending</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded"></div>
                <span class="text-sm text-gray-600">Confirmed</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-purple-500 rounded"></div>
                <span class="text-sm text-gray-600">Completed</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-red-500 rounded"></div>
                <span class="text-sm text-gray-600">Cancelled</span>
            </div>
            <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-gray-500 rounded"></div>
                <span class="text-sm text-gray-600">No Show</span>
            </div>
        </div>
    </div>

    
    <div id="create-modal" class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none; opacity: 0; transition: opacity 0.3s ease-out;">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="hideCreateModal()"></div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
                style="transform: translateY(20px); transition: transform 0.3s ease-out;">
                <form wire:submit.prevent="createAppointment">
                    <div class="bg-white px-6 pt-5 pb-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">New Appointment</h3>
                            <button type="button" onclick="hideCreateModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Client *</label>
                                <select wire:model="client_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    <option value="">Select Client</option>
                                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($client->id); ?>"><?php echo e($client->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                </select>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-600 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Service *</label>
                                <select wire:model="service_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    <option value="">Select Service</option>
                                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($service->id); ?>"><?php echo e($service->name); ?>

                                            (<?php echo e($service->formatted_price); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                </select>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['service_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-600 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Staff</label>
                                <select wire:model="staff_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                    <option value="">Unassigned</option>
                                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
                                <input type="date" wire:model="appointment_date"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['appointment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-600 text-xs"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
                                <input type="time" wire:model="start_time"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent">
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-600 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>

                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                                <textarea wire:model="notes" rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B48B5B] focus:border-transparent"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
                        <button type="button" onclick="hideCreateModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-[#B48B5B] text-white rounded-lg hover:bg-[#9A7449]">
                            Create Appointment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
    <div id="view-modal" class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none; opacity: 0; transition: opacity 0.3s ease-out;">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="hideViewModal()"></div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
                style="transform: translateY(20px); transition: transform 0.3s ease-out;">
                <div class="bg-white px-6 pt-5 pb-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Appointment Details</h3>
                        <button type="button" onclick="hideViewModal()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div id="appointment-details-content"></div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex justify-between">
                    <div class="flex gap-2">
                        <button onclick="updateStatusFromView('confirmed')"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm">
                            Confirm
                        </button>
                        <button onclick="updateStatusFromView('completed')"
                            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm">
                            Complete
                        </button>
                        <button onclick="updateStatusFromView('cancelled')"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm">
                            Cancel
                        </button>
                    </div>
                    <button type="button" onclick="hideViewModal()"
                        class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let appointmentsData = <?php echo json_encode($appointments, 15, 512) ?>;
    let currentViewAppointmentId = null;

    function formatCurrency(amount) {
        const numeric = Number(amount) || 0;
        return numeric.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }) + ' TSH';
    }

    function showCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function showViewModal(id) {
        currentViewAppointmentId = id;
        const appointment = appointmentsData.find(a => a.id === id);
        if (!appointment) return;

        const content = `
            <div class="space-y-4">
	                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Client</label>
                        <div class="text-base font-semibold text-gray-900">${appointment.client.name}</div>
                        <div class="text-sm text-gray-600">${appointment.client.email}</div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Status</label>
                        <div class="mt-1">
                            <span class="px-3 py-1 text-xs font-semibold rounded-full
                                ${appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''}
                                ${appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' : ''}
                                ${appointment.status === 'completed' ? 'bg-purple-100 text-purple-800' : ''}
                                ${appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' : ''}
                                ${appointment.status === 'no_show' ? 'bg-gray-100 text-gray-800' : ''}">
                                ${appointment.status.replace('_', ' ').toUpperCase()}
                            </span>
                        </div>
                    </div>
                </div>

	                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
	                    <div>
	                        <label class="text-sm font-medium text-gray-500">Service</label>
	                        <div class="text-base font-semibold text-gray-900">${appointment.service.name}</div>
	                        <div class="text-sm text-gray-600">${formatCurrency(appointment.total_amount)} • ${appointment.duration} min</div>
	                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Staff</label>
                        <div class="text-base font-semibold text-gray-900">
                            ${appointment.staff ? appointment.staff.name : 'Unassigned'}
                        </div>
                    </div>
                </div>

	                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Date</label>
                        <div class="text-base font-semibold text-gray-900">
                            ${new Date(appointment.appointment_date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                        </div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Time</label>
                        <div class="text-base font-semibold text-gray-900">
                            ${appointment.start_time.substring(0, 5)} - ${appointment.end_time.substring(0, 5)}
                        </div>
                    </div>
                </div>

                ${appointment.notes ? `
                    <div>
                        <label class="text-sm font-medium text-gray-500">Notes</label>
                        <div class="text-base text-gray-900 bg-gray-50 p-3 rounded-lg mt-1">
                            ${appointment.notes}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        document.getElementById('appointment-details-content').innerHTML = content;

        const modal = document.getElementById('view-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideViewModal() {
        const modal = document.getElementById('view-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function updateStatusFromView(status) {
        if (currentViewAppointmentId) {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').updateStatus(currentViewAppointmentId, status);
            hideViewModal();
        }
    }

    // Drag and Drop functionality (Feature 14: Drag-and-Drop Rescheduling)
    let draggedAppointmentId = null;

    function handleDragStart(event, appointmentId) {
        draggedAppointmentId = appointmentId;
        event.dataTransfer.effectAllowed = 'move';
        event.target.style.opacity = '0.5';
    }

    function handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
        event.currentTarget.classList.add('bg-blue-100');
    }

    function handleDrop(event, newDate, newTime = null) {
        event.preventDefault();
        event.currentTarget.classList.remove('bg-blue-100');

        if (draggedAppointmentId) {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').moveAppointment(draggedAppointmentId, newDate, newTime);
            draggedAppointmentId = null;
        }
    }

    // Event listeners
    document.addEventListener('livewire:init', () => {
        Livewire.on('appointment-created', () => {
            hideCreateModal();
        });
    });

    // Keyboard shortcuts (Feature 15: Keyboard Navigation)
    document.addEventListener('keydown', (e) => {
        // N key - New appointment
        if (e.key === 'n' && !e.ctrlKey && !e.metaKey && !e.target.matches('input, textarea')) {
            showCreateModal();
        }
        // Escape key - Close modals
        if (e.key === 'Escape') {
            hideCreateModal();
            hideViewModal();
        }
        // Arrow keys - Navigate dates
        if (e.key === 'ArrowLeft' && !e.target.matches('input, textarea')) {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').previousPeriod();
        }
        if (e.key === 'ArrowRight' && !e.target.matches('input, textarea')) {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').nextPeriod();
        }
        // T key - Today
        if (e.key === 't' && !e.target.matches('input, textarea')) {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').goToToday();
        }
    });
</script><?php /**PATH C:\laragon\www\Instyle\resources\views\livewire\manager\appointments\calendar.blade.php ENDPATH**/ ?>