<?php
/**
 * FAQ Management - Admin Panel
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Get all active services for the service selector
$services = $database->fetchAll("SELECT id, name, category, price FROM services WHERE is_active = 1 ORDER BY category, name");

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'create':
                // Insert FAQ
                $stmt = $database->query(
                    "INSERT INTO faqs (category, question, answer, display_order, is_active, service_link_text) VALUES (?, ?, ?, ?, ?, ?)",
                    [
                        cleanText($_POST['category']),
                        cleanText($_POST['question']),
                        cleanText($_POST['answer']),
                        (int)$_POST['display_order'],
                        isset($_POST['is_active']) ? 1 : 0,
                        cleanText($_POST['service_link_text'] ?? null)
                    ]
                );

                // Get the inserted FAQ ID
                $faqId = $database->lastInsertId();

                // Handle service links
                if (!empty($_POST['linked_services']) && is_array($_POST['linked_services'])) {
                    foreach ($_POST['linked_services'] as $serviceId) {
                        $database->query(
                            "INSERT INTO faq_services (id, faq_id, service_id) VALUES (UUID(), ?, ?)",
                            [$faqId, $serviceId]
                        );
                    }
                }

                echo json_encode(['success' => true, 'message' => 'FAQ created successfully']);
                exit;
                
            case 'update':
                // Update FAQ
                $stmt = $database->query(
                    "UPDATE faqs SET category = ?, question = ?, answer = ?, display_order = ?, is_active = ?, service_link_text = ? WHERE id = ?",
                    [
                        cleanText($_POST['category']),
                        cleanText($_POST['question']),
                        cleanText($_POST['answer']),
                        (int)$_POST['display_order'],
                        isset($_POST['is_active']) ? 1 : 0,
                        cleanText($_POST['service_link_text'] ?? null),
                        (int)$_POST['id']
                    ]
                );

                // Update service links - first delete existing ones
                $database->query("DELETE FROM faq_services WHERE faq_id = ?", [(int)$_POST['id']]);

                // Add new service links
                if (!empty($_POST['linked_services']) && is_array($_POST['linked_services'])) {
                    foreach ($_POST['linked_services'] as $serviceId) {
                        $database->query(
                            "INSERT INTO faq_services (id, faq_id, service_id) VALUES (UUID(), ?, ?)",
                            [(int)$_POST['id'], $serviceId]
                        );
                    }
                }

                echo json_encode(['success' => true, 'message' => 'FAQ updated successfully']);
                exit;
                
            case 'delete':
                $stmt = $database->query("DELETE FROM faqs WHERE id = ?", [(int)$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'FAQ deleted successfully']);
                exit;
                
            case 'get':
                $faq = $database->fetch("SELECT * FROM faqs WHERE id = ?", [(int)$_POST['id']]);

                // Decode HTML entities for admin editing
                if ($faq) {
                    $faq['question'] = html_entity_decode($faq['question'], ENT_QUOTES, 'UTF-8');
                    $faq['answer'] = html_entity_decode($faq['answer'], ENT_QUOTES, 'UTF-8');
                    $faq['service_link_text'] = html_entity_decode($faq['service_link_text'] ?? '', ENT_QUOTES, 'UTF-8');
                }

                // Get linked services
                $linkedServices = $database->fetchAll(
                    "SELECT s.id, s.name FROM services s
                     INNER JOIN faq_services fs ON s.id = fs.service_id
                     WHERE fs.faq_id = ? AND s.is_active = 1",
                    [(int)$_POST['id']]
                );

                $faq['linked_services'] = array_column($linkedServices, 'id');
                $faq['linked_services_names'] = array_column($linkedServices, 'name');

                echo json_encode(['success' => true, 'data' => $faq], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// Pagination settings
$itemsPerPage = 6;
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$selectedCategory = isset($_GET['category']) ? $_GET['category'] : '';

// Get all categories for navigation
$categories = $database->fetchAll("SELECT DISTINCT category FROM faqs ORDER BY category");

// Check if faq_services table exists (migration might not be applied yet)
$tableExists = false;
try {
    $database->query("SELECT 1 FROM faq_services LIMIT 1");
    $tableExists = true;
} catch (Exception $e) {
    // Table doesn't exist, use fallback queries
}

// Get FAQs with or without linked services based on table existence
if ($tableExists) {
    // Get FAQs with linked services based on selected category or all FAQs
    if ($selectedCategory) {
        // Get FAQs for specific category with pagination
        $offset = ($currentPage - 1) * $itemsPerPage;
        $faqs = $database->fetchAll(
            "SELECT f.*,
             GROUP_CONCAT(s.name SEPARATOR ', ') as linked_service_names,
             COUNT(fs.service_id) as service_count
             FROM faqs f
             LEFT JOIN faq_services fs ON f.id = fs.faq_id
             LEFT JOIN services s ON fs.service_id = s.id AND s.is_active = 1
             WHERE f.category = ?
             GROUP BY f.id
             ORDER BY f.display_order, f.id
             LIMIT ? OFFSET ?",
            [$selectedCategory, $itemsPerPage, $offset]
        );

        // Get total count for pagination
        $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs WHERE category = ?", [$selectedCategory])['count'];
    } else {
        // Get all FAQs with pagination
        $offset = ($currentPage - 1) * $itemsPerPage;
        $faqs = $database->fetchAll(
            "SELECT f.*,
             GROUP_CONCAT(s.name SEPARATOR ', ') as linked_service_names,
             COUNT(fs.service_id) as service_count
             FROM faqs f
             LEFT JOIN faq_services fs ON f.id = fs.faq_id
             LEFT JOIN services s ON fs.service_id = s.id AND s.is_active = 1
             GROUP BY f.id
             ORDER BY f.category, f.display_order, f.id
             LIMIT ? OFFSET ?",
            [$itemsPerPage, $offset]
        );

        // Get total count for pagination
        $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
    }
} else {
    // Fallback queries without service linking (for before migration)
    if ($selectedCategory) {
        // Get FAQs for specific category with pagination
        $offset = ($currentPage - 1) * $itemsPerPage;
        $faqs = $database->fetchAll(
            "SELECT *, '' as linked_service_names, 0 as service_count FROM faqs WHERE category = ? ORDER BY display_order, id LIMIT ? OFFSET ?",
            [$selectedCategory, $itemsPerPage, $offset]
        );

        // Get total count for pagination
        $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs WHERE category = ?", [$selectedCategory])['count'];
    } else {
        // Get all FAQs with pagination
        $offset = ($currentPage - 1) * $itemsPerPage;
        $faqs = $database->fetchAll(
            "SELECT *, '' as linked_service_names, 0 as service_count FROM faqs ORDER BY category, display_order, id LIMIT ? OFFSET ?",
            [$itemsPerPage, $offset]
        );

        // Get total count for pagination
        $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
    }
}

// Calculate pagination info
$totalPages = ceil($totalItems / $itemsPerPage);

// Group FAQs by category for display
$faqsByCategory = [];
foreach ($faqs as $faq) {
    $faqsByCategory[$faq['category']][] = $faq;
}

// Get all FAQs for statistics (not paginated)
$allFaqs = $database->fetchAll("SELECT * FROM faqs");
$allFaqsByCategory = [];
foreach ($allFaqs as $faq) {
    $allFaqsByCategory[$faq['category']][] = $faq;
}

$pageTitle = "FAQ Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical FAQ Management CSS -->
<style>
/* Medical FAQ Management Specific Styles */
.medical-faq-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-faq-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-faq-card:hover::before {
    left: 100%;
}

.medical-faq-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-faq-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-faq-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-faq-item:hover::before {
    transform: scaleX(1);
}

.medical-faq-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-faq-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.faq-type-general {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.faq-type-booking {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.faq-type-payment {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.faq-type-safety {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.medical-status-active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-status-inactive {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-faq-grid {
        grid-template-columns: 1fr;
    }

    .medical-faq-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-faq-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    FAQ
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive FAQ management and content organization</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= count($allFaqs) ?> Total FAQs
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        <?= count($allFaqsByCategory) ?> Categories
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openCreateModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    + Add New FAQ
                                </button>
                            </div>
                        </div>
                    </div>

                    <?php if (!$tableExists): ?>
                    <!-- Migration Notice -->
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6 mb-8 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-yellow-800">Database Migration Required</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>The FAQ-to-Services linking feature requires a database migration. Service linking functionality is currently disabled.</p>
                                    <div class="mt-4">
                                        <a href="../../basic_faq_migration.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                            </svg>
                                            Apply Migration Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total FAQs</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= count($allFaqs) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Categories</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= count($allFaqsByCategory) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Active</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= count(array_filter($allFaqs, fn($f) => $f['is_active'])) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-red-100 to-red-200">
                                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Inactive</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= count(array_filter($allFaqs, fn($f) => !$f['is_active'])) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter by Category</h2>
                        <div class="flex flex-wrap gap-3">
                            <a href="?<?= http_build_query(array_merge($_GET, ['category' => '', 'page' => 1])) ?>" 
                               class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors <?= empty($selectedCategory) ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                All Categories
                                <span class="ml-2 bg-white/20 text-xs px-2 py-1 rounded-full"><?= count($allFaqs) ?></span>
                            </a>
                            
                            <?php foreach ($categories as $category): ?>
                                <?php $categoryCount = count(array_filter($allFaqs, fn($f) => $f['category'] === $category['category'])); ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['category' => $category['category'], 'page' => 1])) ?>" 
                                   class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize <?= $selectedCategory === $category['category'] ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                    <?= htmlspecialchars($category['category']) ?>
                                    <span class="ml-2 bg-white/20 text-xs px-2 py-1 rounded-full"><?= $categoryCount ?></span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Medical FAQ Grid -->
                    <?php if (empty($faqs)): ?>
                        <div class="medical-faq-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">
                                    <?php if ($selectedCategory): ?>
                                        No FAQs Found in <?= ucfirst(htmlspecialchars($selectedCategory)) ?> Category
                                    <?php else: ?>
                                        No FAQs Found
                                    <?php endif; ?>
                                </h3>
                                <p class="text-gray-600 mb-6">
                                    <?php if ($selectedCategory): ?>
                                        There are no FAQs in the <?= htmlspecialchars($selectedCategory) ?> category yet.
                                    <?php else: ?>
                                        Get started by creating your first FAQ
                                    <?php endif; ?>
                                </p>
                                <button onclick="openCreateModal()" class="medical-btn-primary">
                                    Create First FAQ
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($faqsByCategory as $category => $categoryFaqs): ?>
                            <div class="medical-faq-card p-8 mb-8">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-redolence-navy capitalize">
                                        <?= htmlspecialchars($category) ?> Questions
                                    </h3>
                                    <span class="medical-faq-type-badge faq-type-<?= strtolower($category) ?>">
                                        <?= count($categoryFaqs) ?> FAQ<?= count($categoryFaqs) > 1 ? 's' : '' ?>
                                    </span>
                                </div>
                                
                                <div class="space-y-4">
                                    <?php foreach ($categoryFaqs as $faq): ?>
                                        <div class="medical-faq-item">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center mb-2">
                                                        <span class="medical-faq-type-badge <?= $faq['is_active'] ? 'medical-status-active' : 'medical-status-inactive' ?> mr-3">
                                                            <?= $faq['is_active'] ? 'Active' : 'Inactive' ?>
                                                        </span>
                                                        <span class="text-xs text-gray-500">Order: <?= $faq['display_order'] ?></span>
                                                    </div>
                                                    <h4 class="text-lg font-bold text-redolence-navy mb-2">
                                                        <?= htmlspecialchars($faq['question']) ?>
                                                    </h4>
                                                    <p class="text-gray-600 text-sm line-clamp-2">
                                                        <?= htmlspecialchars(substr($faq['answer'], 0, 150)) ?>...
                                                    </p>

                                                    <?php if ($faq['service_count'] > 0): ?>
                                                        <div class="mt-3 p-2 bg-redolence-green/10 rounded-lg">
                                                            <div class="flex items-center text-xs text-redolence-green">
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                                                </svg>
                                                                <span class="font-medium">Linked to <?= $faq['service_count'] ?> service<?= $faq['service_count'] > 1 ? 's' : '' ?>:</span>
                                                            </div>
                                                            <p class="text-xs text-gray-600 mt-1 truncate">
                                                                <?= htmlspecialchars($faq['linked_service_names'] ?: 'No services linked') ?>
                                                            </p>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="text-xs text-gray-500 mt-2">
                                                        Updated: <?= date('M j, Y', strtotime($faq['updated_at'])) ?>
                                                    </div>
                                                </div>
                                                <div class="flex gap-2 ml-4">
                                                    <button onclick="editFAQ(<?= $faq['id'] ?>)" 
                                                            class="medical-btn-secondary text-sm px-3 py-1">
                                                        Edit
                                                    </button>
                                                    <button onclick="deleteFAQ(<?= $faq['id'] ?>, '<?= htmlspecialchars($faq['question'], ENT_QUOTES) ?>')" 
                                                            class="medical-btn-danger text-sm px-3 py-1">
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-faq-card p-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing <span class="font-semibold text-redolence-navy"><?= ($currentPage - 1) * $itemsPerPage + 1 ?></span> to
                                    <span class="font-semibold text-redolence-navy"><?= min($currentPage * $itemsPerPage, $totalItems) ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalItems ?></span> results
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($currentPage > 1): ?>
                                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage - 1])) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $currentPage ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($currentPage < $totalPages): ?>
                                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage + 1])) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Create/Edit FAQ Modal -->
<div id="faqModal" class="fixed inset-0 bg-black bg-opacity-60 hidden items-center justify-center z-50 backdrop-blur-sm">
    <div class="bg-white max-w-5xl w-full mx-4 max-h-[95vh] overflow-y-auto rounded-2xl shadow-2xl border-2 border-gray-100">
        <div class="px-8 py-6 border-b border-gray-200">
            <h3 id="modalTitle" class="text-2xl font-bold text-redolence-navy">Add New FAQ</h3>
        </div>
        
        <form id="faqForm" class="p-8 space-y-6">
            <input type="hidden" id="faqId" name="id">
            <input type="hidden" id="formAction" name="action" value="create">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="category" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Category</label>
                    <select id="category" name="category" required class="medical-form-input w-full">
                        <option value="">Select Category</option>
                        <option value="general">General</option>
                        <option value="booking">Booking</option>
                        <option value="payment">Payment</option>
                        <option value="safety">Safety</option>
                    </select>
                </div>
                
                <div>
                    <label for="display_order" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Display Order</label>
                    <input type="number" id="display_order" name="display_order" min="0" value="0" class="medical-form-input w-full">
                </div>
            </div>
            
            <div>
                <label for="question" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Question</label>
                <input type="text" id="question" name="question" required class="medical-form-input w-full" placeholder="Enter the question">
            </div>
            
            <div>
                <label for="answer" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Answer</label>
                <textarea id="answer" name="answer" rows="6" required class="medical-form-input w-full resize-none" placeholder="Enter the detailed answer"></textarea>
            </div>

            <!-- Service Linking Section -->
            <?php if ($tableExists): ?>
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-lg font-semibold text-redolence-navy mb-4">Link to Services (Optional)</h4>
                <p class="text-sm text-gray-600 mb-4">Select services that are related to this FAQ. These will appear as clickable links on the public FAQ page.</p>

                <div class="mb-4">
                    <label for="service_link_text" class="block text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Service Link Text</label>
                    <input type="text" id="service_link_text" name="service_link_text" class="medical-form-input w-full" placeholder="e.g., 'Learn more about this treatment' or 'Book this service'">
                    <p class="text-xs text-gray-500 mt-1">Custom text for service links (optional - defaults to 'Learn More')</p>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Related Services</label>
                    <div class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <?php
                        $currentCategory = '';
                        foreach ($services as $service):
                            if ($currentCategory !== $service['category']):
                                if ($currentCategory !== '') echo '</div>';
                                $currentCategory = $service['category'];
                                echo '<div class="mb-4">';
                                echo '<h5 class="font-semibold text-redolence-navy mb-2 capitalize">' . htmlspecialchars($currentCategory) . '</h5>';
                            endif;
                        ?>
                            <div class="flex items-center mb-2">
                                <input type="checkbox"
                                       id="service_<?= $service['id'] ?>"
                                       name="linked_services[]"
                                       value="<?= $service['id'] ?>"
                                       class="h-4 w-4 text-redolence-green focus:ring-redolence-green border-gray-300 rounded">
                                <label for="service_<?= $service['id'] ?>" class="ml-2 text-sm text-gray-700 flex-1">
                                    <?= htmlspecialchars($service['name']) ?>
                                    <?php if ($service['price']): ?>
                                        <span class="text-redolence-green font-medium">- TSH <?= number_format($service['price']) ?></span>
                                    <?php endif; ?>
                                </label>
                            </div>
                        <?php
                        endforeach;
                        if ($currentCategory !== '') echo '</div>';
                        ?>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Select multiple services if this FAQ applies to several treatments</p>
                </div>
            </div>
            <?php else: ?>
            <div class="border-t border-gray-200 pt-6">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                        <span class="text-gray-600 font-medium">Service Linking Feature</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-2">Service linking is not available until the database migration is applied.</p>
                    <a href="../../basic_faq_migration.php" class="inline-flex items-center mt-3 text-sm text-blue-600 hover:text-blue-500">
                        Apply migration to enable this feature
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" checked class="h-4 w-4 text-redolence-green focus:ring-redolence-green border-gray-300 rounded">
                <label for="is_active" class="ml-2 block text-sm text-gray-700">Active (visible on website)</label>
            </div>
        </form>
        
        <div class="px-8 py-6 border-t border-gray-200 flex justify-end space-x-4">
            <button onclick="closeFAQModal()" class="medical-btn-secondary">
                Cancel
            </button>
            <button onclick="saveFAQ()" class="medical-btn-primary">
                <span id="saveButtonText">Save FAQ</span>
            </button>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

<script>
// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add New FAQ';
    document.getElementById('formAction').value = 'create';
    document.getElementById('saveButtonText').textContent = 'Save FAQ';
    document.getElementById('faqForm').reset();
    document.getElementById('is_active').checked = true;

    // Clear all service checkboxes
    const serviceCheckboxes = document.querySelectorAll('input[name="linked_services[]"]');
    serviceCheckboxes.forEach(checkbox => checkbox.checked = false);

    document.getElementById('faqModal').classList.remove('hidden');
    document.getElementById('faqModal').classList.add('flex');
}

function closeFAQModal() {
    document.getElementById('faqModal').classList.add('hidden');
    document.getElementById('faqModal').classList.remove('flex');
}

function editFAQ(id) {
    // Fetch FAQ data
    fetch('', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get&id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const faq = data.data;
            document.getElementById('modalTitle').textContent = 'Edit FAQ';
            document.getElementById('formAction').value = 'update';
            document.getElementById('saveButtonText').textContent = 'Update FAQ';
            document.getElementById('faqId').value = faq.id;
            document.getElementById('category').value = faq.category;
            document.getElementById('question').value = faq.question;
            document.getElementById('answer').value = faq.answer;
            document.getElementById('display_order').value = faq.display_order;
            document.getElementById('is_active').checked = faq.is_active == 1;
            document.getElementById('service_link_text').value = faq.service_link_text || '';

            // Clear all service checkboxes first
            const serviceCheckboxes = document.querySelectorAll('input[name="linked_services[]"]');
            serviceCheckboxes.forEach(checkbox => checkbox.checked = false);

            // Check the linked services
            if (faq.linked_services && faq.linked_services.length > 0) {
                faq.linked_services.forEach(serviceId => {
                    const checkbox = document.getElementById('service_' + serviceId);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
            
            document.getElementById('faqModal').classList.remove('hidden');
            document.getElementById('faqModal').classList.add('flex');
        } else {
            showMessage('Error loading FAQ data', 'error');
        }
    })
    .catch(error => {
        showMessage('Error loading FAQ data', 'error');
    });
}

function saveFAQ() {
    const form = document.getElementById('faqForm');
    const formData = new FormData(form);

    // Convert FormData to URLSearchParams
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        params.append(key, value);
    }

    // Handle service checkboxes - ensure they're included even if unchecked
    const serviceCheckboxes = document.querySelectorAll('input[name="linked_services[]"]');
    const checkedServices = Array.from(serviceCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value);

    // Remove any existing linked_services entries and add the checked ones
    params.delete('linked_services[]');
    checkedServices.forEach(serviceId => {
        params.append('linked_services[]', serviceId);
    });
    
    fetch('', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closeFAQModal();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('Error saving FAQ', 'error');
    });
}

function deleteFAQ(id, question) {
    if (confirm(`Are you sure you want to delete this FAQ?\n\n"${question}"`)) {
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete&id=${id}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('Error deleting FAQ', 'error');
        });
    }
}

function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    
    messageDiv.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
    messageDiv.textContent = message;
    
    container.appendChild(messageDiv);
    
    // Animate in
    setTimeout(() => {
        messageDiv.classList.remove('translate-x-full');
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        messageDiv.classList.add('translate-x-full');
        setTimeout(() => {
            container.removeChild(messageDiv);
        }, 300);
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('faqModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeFAQModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeFAQModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>