<?php
/**
 * Get Gallery Item Data - AJAX Endpoint
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/gallery_functions.php';

/**
 * Helper function to get the correct image URL
 * Handles both file paths and external URLs
 */
function getImageUrl($imagePath) {
    if (empty($imagePath)) {
        return '';
    }

    // If it's already a full URL, return as is
    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
        return $imagePath;
    }

    // If it's a file path, prepend the base path
    return getBasePath() . '/' . ltrim($imagePath, '/');
}

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid gallery item ID']);
    exit;
}

$id = (int)$_GET['id'];

try {
    // Get gallery item by ID (including inactive items for admin)
    global $database;
    $item = $database->fetch(
        "SELECT * FROM gallery_items WHERE id = ?",
        [$id]
    );
    
    if (!$item) {
        http_response_code(404);
        echo json_encode(['error' => 'Gallery item not found']);
        exit;
    }
    
    // Prepare response data
    $response = [
        'success' => true,
        'data' => [
            'id' => $item['id'],
            'title' => $item['title'],
            'category' => $item['category'],
            'treatment' => $item['treatment'],
            'description' => $item['description'],
            'duration' => $item['duration'],
            'sessions' => $item['sessions'],
            'before_image' => $item['before_image'],
            'after_image' => $item['after_image'],
            'image' => $item['image'],
            'sort_order' => $item['sort_order'],
            'is_active' => $item['is_active'],
            'created_at' => $item['created_at'],
            'updated_at' => $item['updated_at']
        ]
    ];
    
    // Add image URLs if images exist
    if ($item['before_image']) {
        $response['data']['before_image_url'] = getImageUrl($item['before_image']);
    }
    if ($item['after_image']) {
        $response['data']['after_image_url'] = getImageUrl($item['after_image']);
    }
    if ($item['image']) {
        $response['data']['image_url'] = getImageUrl($item['image']);
    }
    if ($item['after_image']) {
        $response['data']['after_image_url'] = getBasePath() . '/' . $item['after_image'];
    }
    if ($item['image']) {
        $response['data']['image_url'] = getBasePath() . '/' . $item['image'];
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
