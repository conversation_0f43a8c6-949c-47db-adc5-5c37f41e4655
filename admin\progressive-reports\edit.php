<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/admin/progressive-reports');
    exit;
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $status = $_POST['status'] ?? '';
    
    if (empty($title)) {
        $message = 'Report title is required.';
        $messageType = 'error';
    } else {
        $updateData = [
            'title' => $title,
            'description' => $description,
            'status' => $status
        ];
        
        $result = $progressiveReport->update($reportId, $updateData);
        
        if ($result) {
            $message = 'Progressive report updated successfully.';
            $messageType = 'success';
            // Refresh report data
            $report = $progressiveReport->getById($reportId);
        } else {
            $message = 'Failed to update progressive report. Please try again.';
            $messageType = 'error';
        }
    }
}

$pageTitle = "Edit Progressive Report - Medical Admin";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Progressive Reports CSS -->
<style>
.medical-edit-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.08);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-edit-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.medical-form-section {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.medical-form-section:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-input-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
}

.medical-input-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-select-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    cursor: pointer;
}

.medical-select-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-textarea-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    resize: vertical;
    min-height: 120px;
}

.medical-textarea-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-label-enhanced {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

.medical-help-text-enhanced {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

.medical-btn-primary-enhanced {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
    position: relative;
    overflow: hidden;
}

.medical-btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary-enhanced {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 1rem 2rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.medical-btn-secondary-enhanced:hover {
    background: rgba(73, 167, 92, 0.1);
    border-color: #49A75C;
    transform: translateY(-1px);
}

.medical-icon-enhanced {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.medical-alert-enhanced {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
}

.medical-alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}

.medical-alert-success {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
    border-color: #16a34a;
}

.patient-info-card {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(46, 139, 87, 0.05));
    border: 1px solid rgba(73, 167, 92, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.patient-avatar {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Back Navigation -->
                    <div class="mb-6">
                        <a href="<?= getBasePath() ?>/admin/progressive-reports/view.php?id=<?= $reportId ?>" 
                           class="inline-flex items-center text-sm font-medium text-redolence-blue hover:text-blue-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Back to Report
                        </a>
                    </div>

                    <!-- Page Header -->
                    <div class="medical-edit-container p-8 mb-8">
                        <div class="flex items-center mb-6">
                            <div class="medical-icon-enhanced">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">Edit Progressive Report</h1>
                                <p class="mt-2 text-gray-600">Update report details and configuration</p>
                            </div>
                        </div>
                        
                        <!-- Patient Info Card -->
                        <div class="patient-info-card">
                            <div class="flex items-center">
                                <div class="patient-avatar">
                                    <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                                </div>
                                <div>
                                    <h3 class="text-xl font-semibold text-redolence-navy"><?= htmlspecialchars($report['client_name']) ?></h3>
                                    <p class="text-redolence-green font-medium"><?= htmlspecialchars($report['client_email']) ?></p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-2">
                                        <span><?= number_format($report['total_entries']) ?> entries</span>
                                        <span>•</span>
                                        <span>Created <?= date('M j, Y', strtotime($report['created_at'])) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                        <div class="mb-6">
                            <div class="medical-alert-enhanced medical-alert-<?= $messageType ?>">
                                <div class="flex items-center">
                                    <?php if ($messageType === 'error'): ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php endif; ?>
                                    <?= htmlspecialchars($message) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Edit Form -->
                    <div class="medical-edit-container p-8">
                        <form method="POST" class="space-y-8" id="editReportForm">
                            <!-- Report Details Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Report Configuration
                                </h3>
                                
                                <!-- Report Title -->
                                <div class="mb-6">
                                    <label for="title" class="medical-label-enhanced">
                                        Report Title <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           name="title" 
                                           id="title" 
                                           required
                                           value="<?= htmlspecialchars($report['title']) ?>"
                                           class="medical-input-enhanced"
                                           placeholder="Progressive Treatment Report">
                                    <p class="medical-help-text-enhanced">A descriptive title that identifies this report</p>
                                </div>

                                <!-- Description -->
                                <div class="mb-6">
                                    <label for="description" class="medical-label-enhanced">
                                        Treatment Plan & Goals
                                    </label>
                                    <textarea name="description" 
                                              id="description" 
                                              rows="4" 
                                              class="medical-textarea-enhanced"
                                              placeholder="Describe the treatment objectives, expected outcomes, and goals..."><?= htmlspecialchars($report['description'] ?? '') ?></textarea>
                                    <p class="medical-help-text-enhanced">Description of treatment plan, goals, and expected outcomes</p>
                                </div>

                                <!-- Status -->
                                <div class="mb-6">
                                    <label for="status" class="medical-label-enhanced">
                                        Report Status
                                    </label>
                                    <select name="status" id="status" class="medical-select-enhanced">
                                        <option value="ACTIVE" <?= $report['status'] === 'ACTIVE' ? 'selected' : '' ?>>Active</option>
                                        <option value="COMPLETED" <?= $report['status'] === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                                        <option value="ARCHIVED" <?= $report['status'] === 'ARCHIVED' ? 'selected' : '' ?>>Archived</option>
                                    </select>
                                    <p class="medical-help-text-enhanced">Current status of this progressive report</p>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="medical-form-section">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Changes will be saved immediately
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <a href="<?= getBasePath() ?>/admin/progressive-reports/view.php?id=<?= $reportId ?>" 
                                           class="medical-btn-secondary-enhanced">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                            Cancel
                                        </a>
                                        <button type="submit" class="medical-btn-primary-enhanced" id="updateReportBtn">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                            </svg>
                                            <span id="btnText">Update Report</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Enhanced Edit Form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editReportForm');
    const updateBtn = document.getElementById('updateReportBtn');
    const btnText = document.getElementById('btnText');
    
    // Form submission handler
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading state
            updateBtn.disabled = true;
            btnText.textContent = 'Updating...';
            updateBtn.style.opacity = '0.7';
            
            // Add spinner
            const spinner = document.createElement('div');
            spinner.className = 'inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin';
            btnText.parentNode.insertBefore(spinner, btnText);
        });
    }
    
    // Input animations
    const inputs = document.querySelectorAll('.medical-input-enhanced, .medical-select-enhanced, .medical-textarea-enhanced');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 8px 25px rgba(73, 167, 92, 0.15)';
        });
        
        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
    
    // Auto-resize textarea
    const descriptionInput = document.getElementById('description');
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
