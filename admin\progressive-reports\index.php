<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Initialize progressive report handler
$progressiveReport = new ProgressiveReport();

// Handle search and filters
$search = $_GET['search'] ?? '';
// Ensure proper URL decoding for search terms
$search = urldecode($search);
$status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;

// Get progressive reports
$result = $progressiveReport->getAll($page, $limit, $search, $status);
$reports = $result['reports'] ?? [];
$totalPages = $result['total_pages'] ?? 1;
$total = $result['total'] ?? 0;

$pageTitle = "Progressive Reports - Medical Admin";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-glass border border-redolence-green/20 rounded-2xl p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <div class="flex items-center">
                                    <div class="medical-icon mr-4">
                                        <svg class="h-8 w-8 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h1 class="text-3xl font-bold text-redolence-navy">Progressive Reports</h1>
                                        <p class="mt-2 text-gray-600">Comprehensive patient treatment progress tracking</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/create.php"
                                   class="medical-btn-primary inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    New Report
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="medical-glass border border-redolence-blue/20 rounded-xl p-6 mb-8">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <label for="search" class="sr-only">Search reports</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                    <input type="text" name="search" id="search" 
                                           value="<?= htmlspecialchars($search) ?>"
                                           class="medical-input pl-10" 
                                           placeholder="Search by patient name, email, or report title...">
                                </div>
                            </div>
                            <div class="sm:w-48">
                                <select name="status" class="medical-select">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                                    <option value="ACTIVE" <?= $status === 'ACTIVE' ? 'selected' : '' ?>>Active</option>
                                    <option value="COMPLETED" <?= $status === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                                    <option value="ARCHIVED" <?= $status === 'ARCHIVED' ? 'selected' : '' ?>>Archived</option>
                                </select>
                            </div>
                            <button type="submit" class="medical-btn-secondary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"/>
                                </svg>
                                Filter
                            </button>
                        </form>
                    </div>

                    <!-- Results Summary -->
                    <div class="mb-6">
                        <p class="text-sm text-gray-600">
                            Showing <?= count($reports) ?> of <?= number_format($total) ?> progressive reports
                            <?php if ($search): ?>
                                for "<strong><?= htmlspecialchars($search) ?></strong>"
                            <?php endif; ?>
                        </p>
                    </div>

                    <!-- Progressive Reports Table -->
                    <div class="medical-glass border border-gray-200 rounded-xl overflow-hidden">
                        <?php if (empty($reports)): ?>
                            <div class="text-center py-12">
                                <div class="medical-icon mx-auto mb-4">
                                    <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-redolence-navy mb-2">No Progressive Reports Found</h3>
                                <p class="text-gray-500 mb-6">Get started by creating your first progressive report.</p>
                                <a href="<?= getBasePath() ?>/admin/progressive-reports/create.php" 
                                   class="medical-btn-primary">
                                    Create First Report
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="medical-table-header">
                                        <tr>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Patient</th>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Report Title</th>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Entries</th>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Last Updated</th>
                                            <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-100">
                                        <?php foreach ($reports as $report): ?>
                                            <tr class="medical-table-row">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10">
                                                            <div class="patient-avatar h-10 w-10">
                                                                <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                                                            </div>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-semibold text-redolence-navy">
                                                                <?= htmlspecialchars($report['client_name']) ?>
                                                            </div>
                                                            <div class="text-sm text-gray-500">
                                                                <?= htmlspecialchars($report['client_email']) ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="text-sm font-medium text-redolence-navy">
                                                        <?= htmlspecialchars($report['title']) ?>
                                                    </div>
                                                    <?php if ($report['description']): ?>
                                                        <div class="text-sm text-gray-500 truncate max-w-xs">
                                                            <?= htmlspecialchars(substr($report['description'], 0, 60)) ?>...
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="medical-status-badge medical-status-<?= strtolower($report['status']) ?>">
                                                        <?= ucfirst(strtolower($report['status'])) ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 text-redolence-green mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                        </svg>
                                                        <?= number_format($report['total_entries']) ?>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?= date('M j, Y', strtotime($report['updated_at'])) ?>
                                                    <?php if ($report['last_entry_date']): ?>
                                                        <div class="text-xs text-gray-400">
                                                            Last entry: <?= date('M j', strtotime($report['last_entry_date'])) ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div class="flex items-center space-x-2">
                                                        <a href="<?= getBasePath() ?>/admin/progressive-reports/view.php?id=<?= $report['id'] ?>"
                                                           class="medical-action-btn medical-action-view text-xs">
                                                            View
                                                        </a>
                                                        <a href="<?= getBasePath() ?>/admin/progressive-reports/edit.php?id=<?= $report['id'] ?>"
                                                           class="medical-action-btn medical-action-edit text-xs">
                                                            Edit
                                                        </a>
                                                        <a href="<?= getBasePath() ?>/admin/progressive-reports/export-pdf.php?id=<?= $report['id'] ?>"
                                                           class="medical-action-btn medical-action-secondary text-xs" target="_blank" title="Export as PDF">
                                                            PDF
                                                        </a>
                                                        <button onclick="deleteReport('<?= $report['id'] ?>')"
                                                                class="medical-action-btn medical-action-delete text-xs">
                                                            Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <div class="medical-pagination">
                                    <div class="flex items-center justify-between px-6 py-4 border-t border-gray-200">
                                        <div class="flex-1 flex justify-between sm:hidden">
                                            <?php if ($page > 1): ?>
                                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                                   class="medical-btn-secondary">Previous</a>
                                            <?php endif; ?>
                                            <?php if ($page < $totalPages): ?>
                                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                                   class="medical-btn-secondary">Next</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                            <div>
                                                <p class="text-sm text-gray-700">
                                                    Showing page <span class="font-medium"><?= $page ?></span> of 
                                                    <span class="font-medium"><?= $totalPages ?></span>
                                                </p>
                                            </div>
                                            <div>
                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                                           class="<?= $i === $page ? 'medical-pagination-active' : 'medical-pagination-link' ?>">
                                                            <?= $i ?>
                                                        </a>
                                                    <?php endfor; ?>
                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function deleteReport(reportId) {
    if (confirm('Are you sure you want to delete this progressive report? This action cannot be undone and will also delete all associated entries.')) {
        fetch(`<?= getBasePath() ?>/api/admin/progressive-reports.php`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: reportId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting report: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting report');
        });
    }
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
