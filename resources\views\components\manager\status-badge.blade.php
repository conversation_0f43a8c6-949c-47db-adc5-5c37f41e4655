@props([
    'status' => 'pending',
    'size' => 'md'
])

@php
$statusConfig = [
    'confirmed' => [
        'text' => 'Confirmed',
        'classes' => 'text-emerald-700 bg-emerald-50 border-emerald-200'
    ],
    'pending' => [
        'text' => 'Pending',
        'classes' => 'text-[#FA2964] bg-pink-50 border-pink-200'
    ],
    'completed' => [
        'text' => 'Completed',
        'classes' => 'text-emerald-600 bg-emerald-50 border-emerald-200'
    ],
    'cancelled' => [
        'text' => 'Cancelled',
        'classes' => 'text-red-600 bg-red-50 border-red-200'
    ],
    'no-show' => [
        'text' => 'No Show',
        'classes' => 'text-[#8B5D66] bg-[#F7E9E6] border-[#8B5D66]/20'
    ],
    'no_show' => [
        'text' => 'No Show',
        'classes' => 'text-[#8B5D66] bg-[#F7E9E6] border-[#8B5D66]/20'
    ],
    'in-progress' => [
        'text' => 'In Progress',
        'classes' => 'text-[#E98CA5] bg-[#F7E9E6] border-[#E98CA5]/30'
    ]
];

$config = $statusConfig[strtolower($status)] ?? $statusConfig['pending'];

$sizeClasses = [
    'sm' => 'px-2 py-0.5 text-xs',
    'md' => 'px-2.5 py-1 text-xs',
    'lg' => 'px-3 py-1.5 text-sm'
];
@endphp

<span {{ $attributes->merge(['class' => 'inline-flex items-center font-medium border rounded-full ' . $config['classes'] . ' ' . $sizeClasses[$size]]) }}>
    {{ $slot->isEmpty() ? $config['text'] : $slot }}
</span>
