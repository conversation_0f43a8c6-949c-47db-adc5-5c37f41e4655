<?php
/**
 * Admin Packages Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createPackage($_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Package created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'update':
            $result = updatePackage($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Package updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'delete':
            $result = deletePackage($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Package deleted successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/packages');
}

// Get packages with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 9;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== '') {
    $whereClause .= " AND is_active = ?";
    $params[] = (int)$status;
}

$packages = $database->fetchAll(
    "SELECT p.id, p.name, p.description, p.price, p.image, p.package_duration,
            p.is_active, p.created_at, p.updated_at,
            COUNT(DISTINCT b.id) as total_bookings,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_revenue
     FROM packages p
     LEFT JOIN bookings b ON p.id = b.package_id
     $whereClause
     GROUP BY p.id, p.name, p.description, p.price, p.image, p.package_duration,
              p.is_active, p.created_at, p.updated_at
     ORDER BY p.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalPackages = $database->fetch(
    "SELECT COUNT(*) as count FROM packages $whereClause",
    $params
)['count'];

$totalPages = ceil($totalPackages / $limit);

// Get all services for package creation
$allServices = getActiveServices();

// Get package statistics
$stats = getPackageStats();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Packages Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Packages Management CSS -->
<style>
/* Medical Packages Management Specific Styles */
.medical-packages-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-packages-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-packages-card:hover::before {
    left: 100%;
}

.medical-packages-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-package-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-package-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-package-item:hover::before {
    transform: scaleX(1);
}

.medical-package-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-package-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.package-status-active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.package-status-inactive {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.package-savings-badge {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-packages-grid {
        grid-template-columns: 1fr;
    }

    .medical-packages-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-packages-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Treatment Packages
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Create and manage service packages with special pricing and bundled treatments</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $stats['total'] ?> Total Packages
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Package Builder
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openCreateModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    + Create Package
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Packages</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Active Packages</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['active']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Revenue</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= formatCurrency($stats['total_revenue']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-purple-100 to-purple-200">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Avg. Savings</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= formatCurrency($stats['avg_savings']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Packages</h2>
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                       placeholder="Search packages..." 
                                       class="medical-form-input w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input w-full">
                                    <option value="">All Status</option>
                                    <option value="1" <?= $status === '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= $status === '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="flex items-end gap-3">
                                <button type="submit" class="medical-btn-primary">
                                    Apply Filters
                                </button>
                                <?php if ($search || $status !== ''): ?>
                                    <a href="<?= getBasePath() ?>/admin/packages" class="medical-btn-secondary">
                                        Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>

                    <!-- Medical Packages Grid -->
                    <?php if (empty($packages)): ?>
                        <div class="medical-packages-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Treatment Packages Found</h3>
                                <p class="text-gray-600 mb-6">Get started by creating your first treatment package with bundled services and special pricing.</p>
                                <button onclick="openCreateModal()" class="medical-btn-primary">
                                    Create First Package
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="medical-packages-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                            <?php foreach ($packages as $package): ?>
                                <?php 
                                $packageServices = getPackageServices($package['id']);
                                $originalPrice = array_sum(array_column($packageServices, 'price'));
                                $savings = $originalPrice - $package['price'];
                                $discountPercent = $originalPrice > 0 ? round(($savings / $originalPrice) * 100) : 0;
                                
                                // Calculate total duration from services
                                $servicesDuration = array_sum(array_column($packageServices, 'duration'));
                                // Use package_duration if set, otherwise use calculated duration
                                $totalDuration = $package['package_duration'] > 0 ? $package['package_duration'] : $servicesDuration;
                                ?>
                                <div class="medical-package-item">
                                    <!-- Package Image -->
                                    <?php if (!empty($package['image'])): ?>
                                        <?php
                                        // Determine image URL - check if it's a URL or uploaded file
                                        $imageUrl = filter_var($package['image'], FILTER_VALIDATE_URL)
                                            ? $package['image']
                                            : getBasePath() . '/uploads/packages/' . $package['image'];
                                        ?>
                                        <div class="h-48 bg-gray-200 overflow-hidden rounded-t-lg">
                                            <img src="<?= htmlspecialchars($imageUrl) ?>"
                                                 alt="<?= htmlspecialchars($package['name']) ?>"
                                                 class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                                                 onerror="this.parentElement.style.display='none'">
                                        </div>
                                    <?php endif; ?>

                                    <div class="p-6">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex-1">
                                                <h3 class="text-xl font-bold text-redolence-navy mb-2">
                                                    <?= htmlspecialchars($package['name']) ?>
                                                </h3>
                                                <div class="flex gap-2 mb-2">
                                                    <span class="medical-package-type-badge <?= $package['is_active'] ? 'package-status-active' : 'package-status-inactive' ?>">
                                                        <?= $package['is_active'] ? 'Active' : 'Inactive' ?>
                                                    </span>
                                                    <?php if ($discountPercent > 0): ?>
                                                        <span class="medical-package-type-badge package-savings-badge">
                                                            Save <?= $discountPercent ?>%
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if ($package['description']): ?>
                                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                                <?= htmlspecialchars($package['description']) ?>
                                            </p>
                                        <?php endif; ?>

                                        <!-- Services List -->
                                        <div class="mb-4">
                                            <h4 class="text-sm font-semibold text-redolence-green uppercase tracking-wide mb-2">Included Services:</h4>
                                            <div class="space-y-1">
                                                <?php foreach (array_slice($packageServices, 0, 3) as $service): ?>
                                                    <div class="flex justify-between text-sm text-gray-600">
                                                        <span><?= htmlspecialchars($service['name']) ?></span>
                                                        <span class="font-semibold text-redolence-navy">
                                                            <?php if ($service['price'] > 0): ?>
                                                                <?= formatCurrency($service['price']) ?>
                                                            <?php else: ?>
                                                                Custom
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                <?php endforeach; ?>
                                                <?php if (count($packageServices) > 3): ?>
                                                    <div class="text-sm text-redolence-green font-semibold">
                                                        +<?= count($packageServices) - 3 ?> more services
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Pricing -->
                                        <div class="mb-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <?php if ($savings > 0): ?>
                                                        <div class="text-sm text-gray-500 line-through">
                                                            <?= formatCurrency($originalPrice) ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="text-2xl font-bold text-redolence-green">
                                                        <?= formatCurrency($package['price']) ?>
                                                    </div>
                                                </div>
                                                <?php if ($savings > 0): ?>
                                                    <div class="text-right">
                                                        <div class="text-sm text-green-600 font-semibold">Save</div>
                                                        <div class="text-lg font-bold text-green-600">
                                                            <?= formatCurrency($savings) ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Stats -->
                                        <div class="grid grid-cols-3 gap-3 mb-4 text-center">
                                            <div class="p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                                                <div class="text-lg font-bold text-redolence-navy"><?= number_format($package['total_bookings']) ?></div>
                                                <div class="text-xs text-gray-600 font-semibold uppercase tracking-wide">Bookings</div>
                                            </div>
                                            <div class="p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                                                <div class="text-lg font-bold text-redolence-green"><?= formatCurrency($package['total_revenue']) ?></div>
                                                <div class="text-xs text-gray-600 font-semibold uppercase tracking-wide">Revenue</div>
                                            </div>
                                            <div class="p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                                                <div class="text-lg font-bold text-purple-600">
                                                    <?php if ($totalDuration > 0): ?>
                                                        <?= $totalDuration ?> min
                                                    <?php else: ?>
                                                        Custom
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-xs text-gray-600 font-semibold uppercase tracking-wide">Duration</div>
                                            </div>
                                        </div>

                                        <!-- Actions -->
                                        <div class="flex gap-2">
                                            <button onclick="editPackage('<?= $package['id'] ?>')" 
                                                    class="flex-1 medical-btn-secondary text-sm px-3 py-2">
                                                Edit
                                            </button>
                                            <button onclick="viewPackage('<?= $package['id'] ?>')" 
                                                    class="medical-btn-primary text-sm px-3 py-2">
                                                View
                                            </button>
                                            <button onclick="deletePackage('<?= $package['id'] ?>', '<?= htmlspecialchars($package['name']) ?>')" 
                                                    class="medical-btn-danger text-sm px-3 py-2">
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-packages-card p-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing <span class="font-semibold text-redolence-navy"><?= $offset + 1 ?></span> to
                                    <span class="font-semibold text-redolence-navy"><?= min($offset + $limit, $totalPackages) ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalPackages ?></span> results
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $page ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Create/Edit Package Modal -->
<div id="packageModal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
    <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 w-full max-w-6xl max-h-screen overflow-y-auto">
        <div class="px-8 py-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 id="modalTitle" class="text-2xl font-bold text-redolence-navy">Create Package</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <form id="packageForm" method="POST" enctype="multipart/form-data" class="p-8">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="packageId">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column -->
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Package Name *</label>
                        <input type="text" name="name" id="packageName" required class="medical-form-input w-full">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Description</label>
                        <textarea name="description" id="packageDescription" rows="3" class="medical-form-input w-full resize-none"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Package Price (TSH) *</label>
                        <input type="number" name="price" id="packagePrice" step="1" min="0" required class="medical-form-input w-full">
                        <p class="text-xs text-gray-500 mt-2">Set a discounted price for the package (whole numbers only)</p>
                    </div>

                    <div>
                        <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Package Duration (minutes)</label>
                        <input type="number" name="package_duration" id="packageDuration" step="15" min="0" class="medical-form-input w-full">
                        <p class="text-xs text-gray-500 mt-2">Optional: Set overall duration for the entire package</p>
                    </div>

                    <div>
                        <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Package Image</label>
                        <!-- Image Option Toggle -->
                        <div class="flex gap-4 mb-3">
                            <label class="flex items-center">
                                <input type="radio" name="image_option" value="url" id="imageOptionUrl" checked
                                       class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                                <span class="ml-2 text-sm text-gray-700">Image URL</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="image_option" value="upload" id="imageOptionUpload"
                                       class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                                <span class="ml-2 text-sm text-gray-700">Upload Image</span>
                            </label>
                        </div>

                        <!-- URL Input -->
                        <div id="imageUrlSection">
                            <input type="url" name="image_url" id="packageImageUrl"
                                   placeholder="https://images.unsplash.com/photo-**********-138dadb4c035?w=400"
                                   class="medical-form-input w-full">
                            <p class="text-xs text-gray-500 mt-2">Enter a direct image URL</p>
                            <div id="urlValidationMessage" class="text-xs mt-1 hidden"></div>
                        </div>

                        <!-- File Upload -->
                        <div id="imageUploadSection" class="hidden">
                            <div class="flex items-center justify-center w-full">
                                <label for="packageImageFile" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg class="w-8 h-8 mb-4 text-gray-400" fill="none" viewBox="0 0 20 16">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                        </svg>
                                        <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (MAX. 5MB)</p>
                                    </div>
                                    <input id="packageImageFile" name="image_file" type="file" accept="image/*" class="hidden" />
                                </label>
                            </div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3 hidden">
                            <img id="previewImg" src="" alt="Package preview" class="w-full h-32 object-cover rounded-lg border-2 border-gray-200">
                            <button type="button" onclick="clearImagePreview()" class="mt-2 text-xs text-red-500 hover:text-red-700">Remove Image</button>
                        </div>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" id="packageActive" value="1" checked
                                   class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                            <span class="ml-2 text-sm text-gray-700">Package is active</span>
                        </label>
                    </div>
                </div>
                
                <!-- Right Column - Services Selection -->
                <div class="space-y-6">
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <label class="block text-sm font-semibold text-redolence-green uppercase tracking-wide">Package Services *</label>
                            <div class="flex gap-2">
                                <button type="button" onclick="toggleServiceType('catalog')" id="catalogServicesBtn" 
                                        class="px-3 py-1 text-xs rounded-full medical-btn-primary">
                                    From Catalog
                                </button>
                                <button type="button" onclick="toggleServiceType('manual')" id="manualServicesBtn" 
                                        class="px-3 py-1 text-xs rounded-full medical-btn-secondary">
                                    Manual Entry
                                </button>
                            </div>
                        </div>
                        
                        <!-- Catalog Services -->
                        <div id="catalogServicesSection" class="bg-gray-50 rounded-lg p-4 max-h-80 overflow-y-auto border border-gray-200">
                            <div class="space-y-2">
                                <?php foreach ($allServices as $service): ?>
                                    <label class="flex items-center p-3 hover:bg-white rounded-lg transition-colors border border-transparent hover:border-gray-200">
                                        <input type="checkbox" name="services[]" value="<?= $service['id'] ?>" 
                                               data-price="<?= $service['price'] ?>" data-type="catalog"
                                               class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green service-checkbox">
                                        <div class="ml-3 flex-1">
                                            <div class="text-sm font-semibold text-redolence-navy"><?= htmlspecialchars($service['name']) ?></div>
                                            <div class="text-xs text-gray-600">
                                                <?php if ($service['price'] > 0): ?>
                                                    <?= formatCurrency($service['price']) ?>
                                                    <?php if ($service['duration'] > 0): ?> • <?php endif; ?>
                                                <?php endif; ?>
                                                <?php if ($service['duration'] > 0): ?>
                                                    <?= $service['duration'] ?> min
                                                <?php endif; ?>
                                                <?php if ($service['price'] == 0 && $service['duration'] == 0): ?>
                                                    Custom Service
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Manual Services Entry -->
                        <div id="manualServicesSection" class="bg-gray-50 rounded-lg p-4 hidden border border-gray-200">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <h4 class="text-sm font-semibold text-redolence-navy">Custom Services</h4>
                                    <button type="button" onclick="addManualService()" 
                                            class="medical-btn-primary text-xs px-3 py-1">
                                        Add Service
                                    </button>
                                </div>
                                
                                <div id="manualServicesList" class="space-y-3">
                                    <!-- Manual services will be added here dynamically -->
                                </div>
                                
                                <div class="text-xs text-gray-500 text-center py-2">
                                    Click "Add Service" to manually enter custom services for this package
                                </div>
                            </div>
                        </div>
                        
                        <!-- Price Calculator -->
                        <div class="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                            <div class="flex justify-between text-sm text-gray-700 mb-2">
                                <span>Original Price:</span>
                                <span id="originalPrice" class="font-semibold">TSH 0</span>
                            </div>
                            <div class="flex justify-between text-sm text-gray-700 mb-2">
                                <span>Package Price:</span>
                                <span id="packagePriceDisplay" class="font-semibold">TSH 0</span>
                            </div>
                            <div class="flex justify-between text-sm font-bold text-green-600">
                                <span>Savings:</span>
                                <span id="savingsAmount">TSH 0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-4 mt-8 pt-6 border-t border-gray-200">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Save Package
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Create Package';
    document.getElementById('formAction').value = 'create';
    document.getElementById('packageForm').reset();
    document.getElementById('packageActive').checked = true;

    // Reset image options to URL by default
    document.getElementById('imageOptionUrl').checked = true;
    toggleImageOption();
    clearImagePreview();

    // Clear manual services and reset counter
    document.getElementById('manualServicesList').innerHTML = '';
    manualServiceCounter = 0;

    // Clear all service checkboxes
    document.querySelectorAll('.service-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset to catalog services tab
    toggleServiceType('catalog');

    updatePriceCalculator();
    document.getElementById('packageModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('packageModal').classList.add('hidden');
}

function editPackage(packageId) {
    // Implementation for editing packages
    alert('Edit package functionality would be implemented here');
}

function viewPackage(packageId) {
    window.location.href = `<?= getBasePath() ?>/admin/packages/view.php?id=${packageId}`;
}

function deletePackage(packageId, packageName) {
    if (confirm(`Are you sure you want to delete "${packageName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${packageId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Price calculator
function updatePriceCalculator() {
    let originalPrice = 0;

    // Calculate price from catalog services
    const checkboxes = document.querySelectorAll('.service-checkbox:checked');
    checkboxes.forEach(checkbox => {
        originalPrice += parseInt(checkbox.dataset.price);
    });

    // Calculate price from manual services (only include if price is specified)
    const manualPrices = document.querySelectorAll('.manual-service-price');
    manualPrices.forEach(input => {
        const price = parseInt(input.value) || 0;
        if (price > 0) {
            originalPrice += price;
        }
    });

    const packagePrice = parseInt(document.getElementById('packagePrice').value) || 0;
    const savings = Math.max(0, originalPrice - packagePrice);

    document.getElementById('originalPrice').textContent = formatCurrency(originalPrice);
    document.getElementById('packagePriceDisplay').textContent = formatCurrency(packagePrice);
    document.getElementById('savingsAmount').textContent = formatCurrency(savings);
}

// Event listeners
document.querySelectorAll('.service-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updatePriceCalculator);
});

document.getElementById('packagePrice').addEventListener('input', updatePriceCalculator);

function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

// Manual Services Functions
let manualServiceCounter = 0;

function addManualService() {
    manualServiceCounter++;
    const serviceId = 'manual_' + manualServiceCounter;
    
    const serviceHtml = `
        <div class="bg-white rounded-lg p-4 border border-gray-200" id="service_${serviceId}">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                <div>
                    <label class="block text-xs font-semibold text-redolence-green mb-1 uppercase tracking-wide">Service Name *</label>
                    <input type="text" name="manual_services[${serviceId}][name]" required
                           class="medical-form-input w-full text-sm"
                           placeholder="e.g., VIP Consultation">
                </div>
                <div>
                    <label class="block text-xs font-semibold text-redolence-green mb-1 uppercase tracking-wide">Price (TSH)</label>
                    <input type="number" name="manual_services[${serviceId}][price]" min="0" step="1"
                           class="medical-form-input w-full text-sm manual-service-price"
                           placeholder="Optional" onchange="updatePriceCalculator()">
                </div>
                <div>
                    <label class="block text-xs font-semibold text-redolence-green mb-1 uppercase tracking-wide">Duration (min)</label>
                    <input type="number" name="manual_services[${serviceId}][duration]" min="1" step="1"
                           class="medical-form-input w-full text-sm"
                           placeholder="Optional">
                </div>
                <div class="flex items-end">
                    <button type="button" onclick="removeManualService('${serviceId}')" 
                            class="w-full medical-btn-danger text-xs px-2 py-1">
                        Remove
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <label class="block text-xs font-semibold text-redolence-green mb-1 uppercase tracking-wide">Description (optional)</label>
                <input type="text" name="manual_services[${serviceId}][description]"
                       class="medical-form-input w-full text-sm"
                       placeholder="Brief description of the service">
            </div>
        </div>
    `;
    
    document.getElementById('manualServicesList').insertAdjacentHTML('beforeend', serviceHtml);
    updatePriceCalculator();
}

function removeManualService(serviceId) {
    document.getElementById('service_' + serviceId).remove();
    updatePriceCalculator();
}

// Service Type Toggle Functions
function toggleServiceType(type) {
    const catalogBtn = document.getElementById('catalogServicesBtn');
    const manualBtn = document.getElementById('manualServicesBtn');
    const catalogSection = document.getElementById('catalogServicesSection');
    const manualSection = document.getElementById('manualServicesSection');

    if (type === 'catalog') {
        catalogBtn.className = 'px-3 py-1 text-xs rounded-full medical-btn-primary';
        manualBtn.className = 'px-3 py-1 text-xs rounded-full medical-btn-secondary';
        catalogSection.classList.remove('hidden');
        manualSection.classList.add('hidden');
    } else {
        manualBtn.className = 'px-3 py-1 text-xs rounded-full medical-btn-primary';
        catalogBtn.className = 'px-3 py-1 text-xs rounded-full medical-btn-secondary';
        manualSection.classList.remove('hidden');
        catalogSection.classList.add('hidden');
    }
}

// Image option toggle functionality
function toggleImageOption() {
    const urlOption = document.getElementById('imageOptionUrl');
    const uploadOption = document.getElementById('imageOptionUpload');
    const urlSection = document.getElementById('imageUrlSection');
    const uploadSection = document.getElementById('imageUploadSection');

    if (urlOption.checked) {
        urlSection.classList.remove('hidden');
        uploadSection.classList.add('hidden');
        document.getElementById('packageImageFile').value = '';
    } else {
        urlSection.classList.add('hidden');
        uploadSection.classList.remove('hidden');
        document.getElementById('packageImageUrl').value = '';
    }

    clearImagePreview();
}

// Image preview functionality
function updateImagePreview(imageUrl) {
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (imageUrl && isValidImageUrl(imageUrl)) {
        previewImg.src = imageUrl;
        imagePreview.classList.remove('hidden');
    } else {
        imagePreview.classList.add('hidden');
        previewImg.src = '';
    }
}

function clearImagePreview() {
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    imagePreview.classList.add('hidden');
    previewImg.src = '';
}

function isValidImageUrl(url) {
    try {
        new URL(url);
        return /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url) ||
               /images\.unsplash\.com/.test(url) ||
               /source\.unsplash\.com/.test(url) ||
               /cdn\./.test(url) ||
               /imgur\.com/.test(url);
    } catch {
        return false;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add image option toggle event listeners
    const imageOptionUrl = document.getElementById('imageOptionUrl');
    const imageOptionUpload = document.getElementById('imageOptionUpload');
    if (imageOptionUrl && imageOptionUpload) {
        imageOptionUrl.addEventListener('change', toggleImageOption);
        imageOptionUpload.addEventListener('change', toggleImageOption);
    }

    // Add image URL preview event listener
    const packageImageUrl = document.getElementById('packageImageUrl');
    if (packageImageUrl) {
        packageImageUrl.addEventListener('input', function() {
            updateImagePreview(this.value);
        });
    }

    // Add file upload preview event listener
    const packageImageFile = document.getElementById('packageImageFile');
    if (packageImageFile) {
        packageImageFile.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imagePreview = document.getElementById('imagePreview');
                    const previewImg = document.getElementById('previewImg');
                    previewImg.src = e.target.result;
                    imagePreview.classList.remove('hidden');
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Close modal on backdrop click
document.getElementById('packageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>