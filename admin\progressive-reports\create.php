<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Initialize database connection
global $database;

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $progressiveReport = new ProgressiveReport();
    
    $clientId = $_POST['client_id'] ?? '';
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    
    if (empty($clientId)) {
        $message = 'Please select a patient.';
        $messageType = 'error';
    } else {
        $result = $progressiveReport->create($clientId, $title, $description, $_SESSION['user_id']);

        if ($result) {
            header('Location: ' . getBasePath() . '/admin/progressive-reports/view.php?id=' . $result['id']);
            exit;
        } else {
            $message = 'Failed to create progressive report. Please try again.';
            $messageType = 'error';
        }
    }
}

// Get pre-selected customer ID if provided
$preSelectedClientId = $_GET['client_id'] ?? '';

// Get all customers for the dropdown
$customers = $database->fetchAll("
    SELECT id, name, email, phone
    FROM users
    WHERE role = 'CUSTOMER'
    ORDER BY name ASC
");

$pageTitle = "Create Progressive Report - Medical Admin";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Progressive Reports CSS -->
<style>
.medical-create-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.08);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-create-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #49A75C, #2E8B57, #49A75C);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.medical-form-section {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.medical-form-section:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.medical-input-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
}

.medical-input-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-select-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    cursor: pointer;
}

.medical-select-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-textarea-enhanced {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    resize: vertical;
    min-height: 120px;
}

.medical-textarea-enhanced:focus {
    outline: none;
    border-color: #49A75C;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    background: rgba(255, 255, 255, 1);
}

.medical-label-enhanced {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

.medical-help-text-enhanced {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

.medical-btn-primary-enhanced {
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
    position: relative;
    overflow: hidden;
}

.medical-btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-primary-enhanced:active {
    transform: translateY(0);
}

.medical-btn-secondary-enhanced {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 1rem 2rem;
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.medical-btn-secondary-enhanced:hover {
    background: rgba(73, 167, 92, 0.1);
    border-color: #49A75C;
    transform: translateY(-1px);
}

.medical-icon-enhanced {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.medical-alert-enhanced {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
}

.medical-alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}

.medical-alert-success {
    background: rgba(73, 167, 92, 0.1);
    color: #16a34a;
    border-color: #16a34a;
}

.medical-progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.medical-progress-steps::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: 2rem;
    right: 2rem;
    height: 2px;
    background: rgba(73, 167, 92, 0.2);
    z-index: 1;
}

.medical-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.medical-step-circle {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #49A75C, #2E8B57);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 10px rgba(73, 167, 92, 0.3);
}

.medical-step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e3a8a;
    text-align: center;
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Back Navigation -->
                    <div class="mb-6">
                        <a href="<?= getBasePath() ?>/admin/progressive-reports" 
                           class="inline-flex items-center text-sm font-medium text-redolence-blue hover:text-blue-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            Back to Progressive Reports
                        </a>
                    </div>

                    <!-- Page Header -->
                    <div class="medical-create-container p-8 mb-8">
                        <div class="flex items-center mb-6">
                            <div class="medical-icon-enhanced">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">Create Progressive Report</h1>
                                <p class="mt-2 text-gray-600">Start comprehensive treatment progress tracking for your patient</p>
                            </div>
                        </div>

                        <!-- Progress Steps -->
                        <div class="medical-progress-steps">
                            <div class="medical-step">
                                <div class="medical-step-circle">1</div>
                                <div class="medical-step-label">Select Patient</div>
                            </div>
                            <div class="medical-step">
                                <div class="medical-step-circle">2</div>
                                <div class="medical-step-label">Report Details</div>
                            </div>
                            <div class="medical-step">
                                <div class="medical-step-circle">3</div>
                                <div class="medical-step-label">Create & Start</div>
                            </div>
                        </div>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                        <div class="mb-6">
                            <div class="medical-alert-enhanced medical-alert-<?= $messageType ?>">
                                <div class="flex items-center">
                                    <?php if ($messageType === 'error'): ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    <?php endif; ?>
                                    <?= htmlspecialchars($message) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Create Form -->
                    <div class="medical-create-container p-8">
                        <form method="POST" class="space-y-8" id="progressiveReportForm">
                            <!-- Patient Selection Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    Patient Selection
                                </h3>
                                <div class="medical-input-group">
                                    <label for="client_id" class="medical-label-enhanced">
                                        Select Patient <span class="text-red-500">*</span>
                                    </label>
                                    <select name="client_id" id="client_id" required class="medical-select-enhanced">
                                        <option value="">Choose a patient to start their progressive report...</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?= $customer['id'] ?>"
                                                    data-email="<?= htmlspecialchars($customer['email']) ?>"
                                                    data-phone="<?= htmlspecialchars($customer['phone'] ?? '') ?>"
                                                    <?= (isset($_POST['client_id']) && $_POST['client_id'] === $customer['id']) || ($preSelectedClientId === $customer['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($customer['name']) ?> - <?= htmlspecialchars($customer['email']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="medical-help-text-enhanced">This will create a comprehensive treatment tracking system for the selected patient</p>
                                </div>

                                <!-- Patient Info Preview -->
                                <div id="patientPreview" class="hidden mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                    <h4 class="font-semibold text-blue-900 mb-2">Selected Patient:</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-blue-600 font-medium">Name:</span>
                                            <span id="previewName" class="ml-2"></span>
                                        </div>
                                        <div>
                                            <span class="text-blue-600 font-medium">Email:</span>
                                            <span id="previewEmail" class="ml-2"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Report Details Section -->
                            <div class="medical-form-section">
                                <h3 class="text-xl font-semibold text-redolence-navy mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Report Configuration
                                </h3>

                                <!-- Report Title -->
                                <div class="medical-input-group">
                                    <label for="title" class="medical-label-enhanced">
                                        Report Title
                                    </label>
                                    <input type="text"
                                           name="title"
                                           id="title"
                                           value="<?= htmlspecialchars($_POST['title'] ?? 'Progressive Treatment Report') ?>"
                                           class="medical-input-enhanced"
                                           placeholder="Progressive Treatment Report">
                                    <p class="medical-help-text-enhanced">A descriptive title that will identify this report in the system</p>
                                </div>

                                <!-- Description -->
                                <div class="medical-input-group">
                                    <label for="description" class="medical-label-enhanced">
                                        Treatment Plan & Goals
                                    </label>
                                    <textarea name="description"
                                              id="description"
                                              rows="4"
                                              class="medical-textarea-enhanced"
                                              placeholder="Describe the treatment objectives, expected outcomes, and any specific goals for this patient's progressive report..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                                    <p class="medical-help-text-enhanced">Optional description of the treatment plan, goals, and expected outcomes</p>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="medical-form-section">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Once created, you can immediately start adding treatment entries
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <a href="<?= getBasePath() ?>/admin/progressive-reports"
                                           class="medical-btn-secondary-enhanced">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                            Cancel
                                        </a>
                                        <button type="submit" class="medical-btn-primary-enhanced" id="createReportBtn">
                                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                            </svg>
                                            <span id="btnText">Create Progressive Report</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-8 medical-glass border border-blue-200 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-redolence-navy mb-4">About Progressive Reports</h3>
                        <div class="space-y-3 text-sm text-gray-600">
                            <p>• Progressive reports track a patient's treatment journey over multiple appointments</p>
                            <p>• Each report is tied to a specific patient and can contain multiple treatment entries</p>
                            <p>• Both admin and staff can add entries to document treatments and progress</p>
                            <p>• Reports help maintain comprehensive patient records and track treatment effectiveness</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Enhanced Progressive Report Creation
document.addEventListener('DOMContentLoaded', function() {
    const clientSelect = document.getElementById('client_id');
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const patientPreview = document.getElementById('patientPreview');
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const form = document.getElementById('progressiveReportForm');
    const createBtn = document.getElementById('createReportBtn');
    const btnText = document.getElementById('btnText');

    // Patient selection handler
    if (clientSelect) {
        clientSelect.addEventListener('change', function() {
            if (this.value) {
                const selectedOption = this.options[this.selectedIndex];
                const patientName = selectedOption.text.split(' - ')[0];
                const patientEmail = selectedOption.getAttribute('data-email');

                // Auto-populate title based on selected patient
                if (titleInput.value === 'Progressive Treatment Report' || titleInput.value === '') {
                    titleInput.value = `Progressive Treatment Report - ${patientName}`;
                }

                // Auto-populate description if empty
                if (descriptionInput.value === '') {
                    descriptionInput.value = `Comprehensive treatment progress tracking for ${patientName}. This report will document all treatments, procedures, and patient progress over time.`;
                }

                // Show patient preview
                previewName.textContent = patientName;
                previewEmail.textContent = patientEmail;
                patientPreview.classList.remove('hidden');

                // Add visual feedback
                selectedOption.parentElement.style.borderColor = '#49A75C';
                selectedOption.parentElement.style.boxShadow = '0 0 0 3px rgba(73, 167, 92, 0.1)';

            } else {
                // Hide patient preview
                patientPreview.classList.add('hidden');

                // Reset title if it was auto-generated
                if (titleInput.value.includes('Progressive Treatment Report - ')) {
                    titleInput.value = 'Progressive Treatment Report';
                }

                // Clear description if it was auto-generated
                if (descriptionInput.value.includes('Comprehensive treatment progress tracking for')) {
                    descriptionInput.value = '';
                }
            }
        });
    }

    // Form submission handler
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading state
            createBtn.disabled = true;
            btnText.textContent = 'Creating Report...';
            createBtn.style.opacity = '0.7';

            // Add spinner
            const spinner = document.createElement('div');
            spinner.className = 'inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin';
            btnText.parentNode.insertBefore(spinner, btnText);

            // Validate form
            if (!clientSelect.value) {
                e.preventDefault();

                // Reset button
                createBtn.disabled = false;
                btnText.textContent = 'Create Progressive Report';
                createBtn.style.opacity = '1';
                spinner.remove();

                // Show error
                clientSelect.style.borderColor = '#dc2626';
                clientSelect.focus();

                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'medical-alert-enhanced medical-alert-error mt-4';
                errorDiv.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        Please select a patient before creating the progressive report.
                    </div>
                `;

                // Insert error message
                const firstSection = document.querySelector('.medical-form-section');
                firstSection.parentNode.insertBefore(errorDiv, firstSection.nextSibling);

                // Remove error message after 5 seconds
                setTimeout(() => {
                    errorDiv.remove();
                    clientSelect.style.borderColor = '';
                }, 5000);

                return false;
            }
        });
    }

    // Input animations
    const inputs = document.querySelectorAll('.medical-input-enhanced, .medical-select-enhanced, .medical-textarea-enhanced');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 8px 25px rgba(73, 167, 92, 0.15)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Auto-resize textarea
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
