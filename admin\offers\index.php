<?php
require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'],
                        'discount' => floatval($_POST['discount']),
                        'code' => strtoupper(trim($_POST['code'])),
                        'valid_from' => $_POST['valid_from'],
                        'valid_to' => $_POST['valid_to'],
                        'max_usage' => !empty($_POST['max_usage']) ? intval($_POST['max_usage']) : null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'image' => $_POST['image'] ?? null
                    ];
                    
                    createOffer($data);
                    $message = 'Offer created successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'update':
                    $id = $_POST['offer_id'];
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'],
                        'discount' => floatval($_POST['discount']),
                        'code' => strtoupper(trim($_POST['code'])),
                        'valid_from' => $_POST['valid_from'],
                        'valid_to' => $_POST['valid_to'],
                        'max_usage' => !empty($_POST['max_usage']) ? intval($_POST['max_usage']) : null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'image' => $_POST['image'] ?? null
                    ];
                    
                    updateOffer($id, $data);
                    $message = 'Offer updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'delete':
                    $id = $_POST['offer_id'];
                    deleteOffer($id);
                    $message = 'Offer deleted successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'toggle_status':
                    $id = $_POST['offer_id'];
                    $offer = getOfferById($id);
                    updateOffer($id, ['is_active' => $offer['is_active'] ? 0 : 1]);
                    $message = 'Offer status updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// Get offers
$offers = getAllOffers($search, $status, $limit, $offset);
$totalOffers = count(getAllOffers($search, $status));
$totalPages = ceil($totalOffers / $limit);

// Get statistics
$stats = getOfferStatistics();

$pageTitle = "Offers Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Offers Management CSS -->
<style>
/* Medical Offers Management Specific Styles */
.medical-offers-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-offers-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-offers-card:hover::before {
    left: 100%;
}

.medical-offers-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-offer-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-offer-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-offer-item:hover::before {
    transform: scaleX(1);
}

.medical-offer-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-stats-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.medical-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.1);
}

.medical-offer-active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-offer-inactive {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-offer-expired {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

@media (max-width: 768px) {
    .medical-offers-grid {
        grid-template-columns: 1fr;
    }

    .medical-offers-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-offers-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Promotional
                                    <span class="text-redolence-green">Offers</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive discount and promotional campaign management</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= count($offers) ?> Active Campaigns
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Analytics
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    + Create Offer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Offers</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $stats['total_offers'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Active Offers</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $stats['active_offers'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Usage</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $stats['total_usage'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-red-100 to-red-200">
                                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Discount Given</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= formatCurrency($stats['total_discount_given']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-offers-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Offers</h2>
                        <form method="GET" class="flex flex-col sm:flex-row gap-6">
                            <div class="flex-1">
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                       placeholder="Search offers by title, code, or description..."
                                       class="medical-form-input w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="flex items-end gap-3">
                                <button type="submit" class="medical-btn-primary">
                                    Apply Filters
                                </button>
                                <?php if ($search || $status !== 'all'): ?>
                                    <a href="<?= getBasePath() ?>/admin/offers" class="medical-btn-secondary">
                                        Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>

                    <!-- Medical Offers Grid -->
                    <?php if (empty($offers)): ?>
                        <div class="medical-offers-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Promotional Offers Found</h3>
                                <p class="text-gray-600 mb-6">Create your first promotional offer to attract customers and boost sales.</p>
                                <button onclick="openModal()" class="medical-btn-primary">
                                    Create Your First Offer
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="medical-offers-grid grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                            <?php foreach ($offers as $offer): ?>
                                <div class="medical-offer-item">
                                    <!-- Offer Header -->
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex items-center flex-1">
                                            <?php if ($offer['image']): ?>
                                                <img class="h-12 w-12 rounded-lg object-cover mr-4" src="<?= htmlspecialchars($offer['image']) ?>" alt="">
                                            <?php else: ?>
                                                <div class="h-12 w-12 rounded-lg bg-gradient-to-br from-redolence-green to-redolence-blue flex items-center justify-center mr-4">
                                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                    </svg>
                                                </div>
                                            <?php endif; ?>
                                            <div class="flex-1">
                                                <h3 class="text-lg font-bold text-redolence-navy mb-1">
                                                    <?= htmlspecialchars($offer['title']) ?>
                                                </h3>
                                                <p class="text-gray-600 text-sm">
                                                    <?= htmlspecialchars($offer['description']) ?>
                                                </p>
                                            </div>
                                        </div>
                                        <?php
                                        $isExpired = strtotime($offer['valid_to']) < time();
                                        $statusClass = $offer['is_active'] && !$isExpired ? 'medical-offer-active' : ($isExpired ? 'medical-offer-expired' : 'medical-offer-inactive');
                                        $statusText = $offer['is_active'] && !$isExpired ? 'Active' : ($isExpired ? 'Expired' : 'Inactive');
                                        ?>
                                        <span class="medical-room-type-badge <?= $statusClass ?> ml-4">
                                            <?= $statusText ?>
                                        </span>
                                    </div>

                                    <!-- Offer Details -->
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Discount Code</p>
                                            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800">
                                                <?= htmlspecialchars($offer['code']) ?>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Discount</p>
                                            <p class="text-2xl font-bold text-redolence-green">
                                                <?= $offer['discount'] ?>%
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Valid Period & Usage -->
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Valid Period</p>
                                            <p class="text-redolence-navy font-semibold text-sm">
                                                <?= date('M j, Y', strtotime($offer['valid_from'])) ?>
                                            </p>
                                            <p class="text-gray-600 text-sm">
                                                to <?= date('M j, Y', strtotime($offer['valid_to'])) ?>
                                            </p>
                                        </div>
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Usage</p>
                                            <p class="text-redolence-navy font-bold">
                                                <?= $offer['usage_count'] ?><?= $offer['max_usage'] ? '/' . $offer['max_usage'] : '' ?>
                                            </p>
                                            <p class="text-gray-600 text-sm">
                                                <?= $offer['max_usage'] ? 'times used' : 'unlimited' ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                                        <div class="flex items-center space-x-3">
                                            <button onclick="editOffer('<?= $offer['id'] ?>')"
                                                    class="medical-btn-primary text-sm px-4 py-2">
                                                Edit
                                            </button>
                                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to <?= $offer['is_active'] ? 'deactivate' : 'activate' ?> this offer?')">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="offer_id" value="<?= $offer['id'] ?>">
                                                <button type="submit" class="medical-btn-secondary text-sm px-4 py-2">
                                                    <?= $offer['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                </button>
                                            </form>
                                        </div>
                                        <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this offer? This action cannot be undone.')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="offer_id" value="<?= $offer['id'] ?>">
                                            <button type="submit" class="medical-btn-danger text-sm px-3 py-2">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-offers-card p-6 mt-8">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing <span class="font-semibold text-redolence-navy"><?= $offset + 1 ?></span> to
                                    <span class="font-semibold text-redolence-navy"><?= min($offset + $limit, $totalOffers) ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalOffers ?></span> results
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $page ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Offer Modal -->
<div id="offerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="modalTitle" class="text-xl font-semibold text-white">Add New Offer</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="offerForm" method="POST" class="space-y-6">
                    <input type="hidden" id="action" name="action" value="create">
                    <input type="hidden" id="offer_id" name="offer_id" value="">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Offer Title *</label>
                            <input type="text" id="title" name="title" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                            <textarea id="description" name="description" rows="3" required
                                      class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                        </div>

                        <div>
                            <label for="discount" class="block text-sm font-medium text-gray-300 mb-2">Discount (%) *</label>
                            <input type="number" id="discount" name="discount" min="0" max="100" step="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="code" class="block text-sm font-medium text-gray-300 mb-2">Promo Code *</label>
                            <input type="text" id="code" name="code" required style="text-transform: uppercase;"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="valid_from" class="block text-sm font-medium text-gray-300 mb-2">Valid From *</label>
                            <input type="date" id="valid_from" name="valid_from" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="valid_to" class="block text-sm font-medium text-gray-300 mb-2">Valid To *</label>
                            <input type="date" id="valid_to" name="valid_to" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="max_usage" class="block text-sm font-medium text-gray-300 mb-2">Max Usage (Optional)</label>
                            <input type="number" id="max_usage" name="max_usage" min="1"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Image URL (Optional)</label>
                            <input type="url" id="image" name="image"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal()"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <span id="submitText">Create Offer</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function openModal(offer = null) {
    const modal = document.getElementById('offerModal');
    const form = document.getElementById('offerForm');
    const modalTitle = document.getElementById('modalTitle');
    const submitText = document.getElementById('submitText');
    const action = document.getElementById('action');
    const offerId = document.getElementById('offer_id');

    if (offer) {
        // Edit mode
        modalTitle.textContent = 'Edit Offer';
        submitText.textContent = 'Update Offer';
        action.value = 'update';
        offerId.value = offer.id;

        // Fill form fields
        document.getElementById('title').value = offer.title;
        document.getElementById('description').value = offer.description;
        document.getElementById('discount').value = offer.discount;
        document.getElementById('code').value = offer.code;
        document.getElementById('valid_from').value = offer.valid_from.split(' ')[0];
        document.getElementById('valid_to').value = offer.valid_to.split(' ')[0];
        document.getElementById('max_usage').value = offer.max_usage || '';
        document.getElementById('image').value = offer.image || '';
        document.getElementById('is_active').checked = offer.is_active == 1;
    } else {
        // Create mode
        modalTitle.textContent = 'Add New Offer';
        submitText.textContent = 'Create Offer';
        action.value = 'create';
        offerId.value = '';
        form.reset();
        document.getElementById('is_active').checked = true;
    }

    modal.classList.remove('hidden');
}

function closeModal() {
    const modal = document.getElementById('offerModal');
    modal.classList.add('hidden');
}

function editOffer(offerId) {
    // Fetch offer data from server
    fetch(`<?= getBasePath() ?>/api/admin/offers/get.php?id=${offerId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(offer => {
            openModal(offer);
        })
        .catch(error => {
            console.error('Error fetching offer:', error);
            alert('Error loading offer data: ' + error.message);
        });
}

// Auto-generate promo code
document.getElementById('title').addEventListener('input', function() {
    const title = this.value;
    const codeField = document.getElementById('code');

    if (title && !codeField.value) {
        // Generate code from title
        const code = title.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().substring(0, 10);
        codeField.value = code;
    }
});

// Close modal when clicking outside
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('valid_from').min = today;
    document.getElementById('valid_to').min = today;
});

// Update valid_to minimum when valid_from changes
document.getElementById('valid_from').addEventListener('change', function() {
    document.getElementById('valid_to').min = this.value;
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
