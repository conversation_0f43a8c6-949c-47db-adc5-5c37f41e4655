<?php
/**
 * Admin Contact Messages Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/contact_functions.php';

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selectedIds = $_POST['selected_messages'] ?? [];
    
    if (!empty($selectedIds) && !empty($action)) {
        $result = bulkUpdateContactMessages($selectedIds, $action);
        
        if ($result) {
            $actionText = [
                'mark_read' => 'marked as read',
                'archive' => 'archived',
                'delete' => 'deleted'
            ];
            
            $_SESSION['success_message'] = count($selectedIds) . ' message(s) ' . ($actionText[$action] ?? 'updated') . ' successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to perform bulk action.';
        }
        
        redirect('/admin/contact-messages');
    }
}

// Get filters
$filters = [
    'status' => $_GET['status'] ?? '',
    'search' => $_GET['search'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'subject' => $_GET['subject'] ?? ''
];

$page = (int)($_GET['page'] ?? 1);
$limit = 20;

// Get contact messages
$result = getContactMessages($filters, $page, $limit);
$messages = $result['messages'];
$totalPages = $result['pages'];
$totalMessages = $result['total'];

// Get statistics
$stats = getContactMessageStats();

// Get unique subjects for filter
$subjects = getContactSubjects();

$pageTitle = "Contact Messages";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Contact Messages Management CSS -->
<style>
/* Medical Contact Messages Management Specific Styles */
.medical-contact-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-contact-card:hover::before {
    left: 100%;
}

.medical-contact-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-message-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-message-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-message-item:hover::before {
    transform: scaleX(1);
}

.medical-message-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-new {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-read {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.status-replied {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-archived {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-contact-grid {
        grid-template-columns: 1fr;
    }

    .medical-contact-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-contact-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Contact Messages
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive customer inquiry and message management system</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= number_format($stats['total']) ?> Total Messages
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Communication Hub
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Messages</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">New Messages</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['new']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Replied</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['replied']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-purple-100 to-purple-200">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Today</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['today']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Messages</h2>
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input w-full">
                                    <option value="">All Statuses</option>
                                    <option value="NEW" <?= $filters['status'] === 'NEW' ? 'selected' : '' ?>>New</option>
                                    <option value="read" <?= $filters['status'] === 'read' ? 'selected' : '' ?>>Read</option>
                                    <option value="REPLIED" <?= $filters['status'] === 'REPLIED' ? 'selected' : '' ?>>Replied</option>
                                    <option value="ARCHIVED" <?= $filters['status'] === 'ARCHIVED' ? 'selected' : '' ?>>Archived</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Subject</label>
                                <select name="subject" class="medical-form-input w-full">
                                    <option value="">All Subjects</option>
                                    <?php foreach ($subjects as $subject): ?>
                                        <option value="<?= htmlspecialchars($subject['subject']) ?>" <?= $filters['subject'] === $subject['subject'] ? 'selected' : '' ?>>
                                            <?= ucfirst(htmlspecialchars($subject['subject'])) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">From Date</label>
                                <input type="date" name="date_from" value="<?= htmlspecialchars($filters['date_from']) ?>" class="medical-form-input w-full">
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">To Date</label>
                                <input type="date" name="date_to" value="<?= htmlspecialchars($filters['date_to']) ?>" class="medical-form-input w-full">
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <div class="flex gap-2">
                                    <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>"
                                           placeholder="Search messages..." class="flex-1 medical-form-input">
                                    <button type="submit" class="medical-btn-primary px-4 py-2">
                                        🔍
                                    </button>
                                </div>
                            </div>
                        </form>

                        <?php if (array_filter($filters)): ?>
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <a href="<?= getBasePath() ?>/admin/contact-messages" class="text-redolence-green hover:text-redolence-blue text-sm font-semibold">
                                    ✖ Clear Filters
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Messages Display -->
                    <?php if (!empty($messages)): ?>
                        <form method="POST" id="bulkForm">
                            <!-- Bulk Actions -->
                            <div class="medical-contact-card p-6 mb-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                                            <span class="ml-2 text-sm font-semibold text-redolence-navy">Select All</span>
                                        </label>
                                        
                                        <select name="bulk_action" id="bulkAction" class="medical-form-input text-sm">
                                            <option value="">Bulk Actions</option>
                                            <option value="mark_read">Mark as Read</option>
                                            <option value="archive">Archive</option>
                                            <option value="delete">Delete</option>
                                        </select>
                                        
                                        <button type="submit" id="bulkSubmit" class="medical-btn-primary text-sm px-4 py-2 disabled:opacity-50" disabled>
                                            Apply
                                        </button>
                                    </div>
                                    
                                    <div class="text-sm text-gray-600">
                                        Showing <?= count($messages) ?> of <?= number_format($totalMessages) ?> messages
                                    </div>
                                </div>
                            </div>

                            <!-- Messages Grid -->
                            <div class="space-y-4 mb-8">
                                <?php foreach ($messages as $message): ?>
                                    <div class="medical-message-item p-6">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-start gap-4 flex-1">
                                                <input type="checkbox" name="selected_messages[]" value="<?= $message['id'] ?>" 
                                                       class="message-checkbox rounded border-gray-300 text-redolence-green focus:ring-redolence-green mt-1">
                                                
                                                <div class="flex-1">
                                                    <div class="flex items-center gap-3 mb-3">
                                                        <span class="medical-status-badge status-<?= strtolower($message['status']) ?>">
                                                            <?= htmlspecialchars($message['status']) ?>
                                                        </span>
                                                        <span class="text-sm text-gray-500">
                                                            <?= date('M j, Y g:i A', strtotime($message['created_at'])) ?>
                                                        </span>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <h3 class="text-lg font-bold text-redolence-navy mb-1">
                                                            <?= htmlspecialchars($message['name']) ?>
                                                        </h3>
                                                        <p class="text-sm text-gray-600"><?= htmlspecialchars($message['email']) ?></p>
                                                        <?php if ($message['phone']): ?>
                                                            <p class="text-sm text-gray-600"><?= htmlspecialchars($message['phone']) ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <?php if ($message['subject']): ?>
                                                        <div class="mb-3">
                                                            <span class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Subject:</span>
                                                            <span class="text-sm text-redolence-navy font-semibold ml-2"><?= ucfirst(htmlspecialchars($message['subject'])) ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="text-gray-700">
                                                        <?= htmlspecialchars(substr($message['message'], 0, 200)) ?><?= strlen($message['message']) > 200 ? '...' : '' ?>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex flex-col gap-2 ml-4">
                                                <button type="button" onclick="event.stopPropagation(); viewMessage('<?= $message['id'] ?>')"
                                                        class="medical-btn-secondary text-xs px-3 py-1">
                                                    👁 View
                                                </button>

                                                <?php if ($message['status'] === 'NEW'): ?>
                                                    <button type="button" onclick="event.stopPropagation(); markAsRead('<?= $message['id'] ?>')"
                                                            class="medical-btn-primary text-xs px-3 py-1">
                                                        ✓ Read
                                                    </button>
                                                <?php endif; ?>

                                                <?php if ($message['status'] !== 'REPLIED'): ?>
                                                    <button type="button" onclick="event.stopPropagation(); replyToMessage('<?= $message['id'] ?>')"
                                                            class="medical-btn-primary text-xs px-3 py-1">
                                                        📧 Reply
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-xs text-green-600 font-semibold px-3 py-1 bg-green-100 rounded-lg">
                                                        ✓ Replied
                                                    </span>
                                                <?php endif; ?>

                                                <button type="button" onclick="event.stopPropagation(); deleteMessage('<?= $message['id'] ?>')"
                                                        class="medical-btn-danger text-xs px-3 py-1">
                                                    🗑 Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="medical-contact-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Messages Found</h3>
                                <p class="text-gray-600">
                                    <?= array_filter($filters) ? 'Try adjusting your filters to see more results.' : 'No contact messages have been received yet.' ?>
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-contact-card p-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing page <span class="font-semibold text-redolence-navy"><?= $page ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalPages ?></span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?<?= http_build_query(array_merge($filters, ['page' => $page - 1])) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $page ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?<?= http_build_query(array_merge($filters, ['page' => $page + 1])) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div id="successMessage" class="fixed top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-2xl shadow-lg z-50 border-2 border-green-300">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?= htmlspecialchars($_SESSION['success_message']) ?>
        </div>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div id="errorMessage" class="fixed top-4 right-4 bg-gradient-to-r from-red-500 to-rose-500 text-white px-6 py-3 rounded-2xl shadow-lg z-50 border-2 border-red-300">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?= htmlspecialchars($_SESSION['error_message']) ?>
        </div>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Enhanced View Message Modal -->
<div id="viewMessageModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 backdrop-blur-sm" tabindex="-1">
    <div class="flex items-center justify-center min-h-screen p-4" onclick="closeModal('viewMessageModal')">
        <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 max-w-4xl w-full max-h-[90vh] overflow-y-auto" onclick="event.stopPropagation()">
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-redolence-navy">Message Details</h3>
                    <button type="button" onclick="closeModal('viewMessageModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div id="messageContent" class="p-8">
                <!-- Message content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Reply Modal -->
<div id="replyModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 backdrop-blur-sm" tabindex="-1">
    <div class="flex items-center justify-center min-h-screen p-4" onclick="closeModal('replyModal')">
        <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 max-w-2xl w-full" onclick="event.stopPropagation()">
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-redolence-navy">Reply to Message</h3>
                    <button type="button" onclick="closeModal('replyModal')" class="text-gray-400 hover:text-redolence-green transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <form id="replyForm" class="p-8 space-y-6">
                <input type="hidden" id="replyMessageId" name="message_id">

                <div id="originalMessagePreview" class="hidden">
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Original Message</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-gray-700 text-sm max-h-32 overflow-y-auto">
                        <div id="originalMessageContent"></div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Reply Message</label>
                    <textarea id="replyMessage" name="reply_message" rows="6" required
                              class="medical-form-input w-full resize-none"
                              placeholder="Type your reply here..."></textarea>
                    <p class="text-xs text-gray-500 mt-2">This message will be sent directly to the customer's email address.</p>
                </div>

                <div class="p-4 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-blue-800">
                            <p class="font-semibold mb-2">Email Reply Information:</p>
                            <ul class="text-xs space-y-1">
                                <li>• The reply will be sent directly to the customer's email</li>
                                <li>• The original message will be included for context</li>
                                <li>• The message status will be automatically updated to "REPLIED"</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button" onclick="closeModal('replyModal')" class="medical-btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="medical-btn-primary">
                        📧 Send Reply
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success/error messages
    setTimeout(() => {
        const successMsg = document.getElementById('successMessage');
        const errorMsg = document.getElementById('errorMessage');
        if (successMsg) successMsg.style.display = 'none';
        if (errorMsg) errorMsg.style.display = 'none';
    }, 5000);

    // Bulk actions functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const bulkAction = document.getElementById('bulkAction');
    const bulkSubmit = document.getElementById('bulkSubmit');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            messageCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Individual checkbox change
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
        const hasSelection = checkedBoxes.length > 0;
        const hasAction = bulkAction.value !== '';

        bulkSubmit.disabled = !(hasSelection && hasAction);

        // Update select all checkbox state
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedBoxes.length === messageCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < messageCheckboxes.length;
        }
    }

    // Bulk action change
    if (bulkAction) {
        bulkAction.addEventListener('change', updateBulkActions);
    }

    // Bulk form submission
    const bulkForm = document.getElementById('bulkForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
            const action = bulkAction.value;

            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one message.');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('Please select an action.');
                return;
            }

            const actionText = {
                'mark_read': 'mark as read',
                'archive': 'archive',
                'delete': 'permanently delete'
            };

            let confirmText = `Are you sure you want to ${actionText[action]} ${checkedBoxes.length} message(s)?`;
            
            if (action === 'delete') {
                confirmText += '\n\nThis action cannot be undone!';
            }

            if (!confirm(confirmText)) {
                e.preventDefault();
            }
        });
    }
});

// View message function
function viewMessage(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php?action=get&id=${messageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.message;
                const content = document.getElementById('messageContent');

                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">From</h4>
                                <div class="text-redolence-navy">
                                    <div class="font-bold text-lg">${escapeHtml(message.name)}</div>
                                    <div class="text-gray-600">${escapeHtml(message.email)}</div>
                                    ${message.phone ? `<div class="text-gray-600 text-sm">${escapeHtml(message.phone)}</div>` : ''}
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Details</h4>
                                <div class="text-redolence-navy">
                                    <div><span class="text-gray-600">Subject:</span> <span class="font-semibold">${message.subject ? escapeHtml(message.subject) : 'No subject'}</span></div>
                                    <div><span class="text-gray-600">Status:</span> <span class="font-semibold capitalize">${escapeHtml(message.status.toLowerCase())}</span></div>
                                    <div><span class="text-gray-600">Date:</span> <span class="font-semibold">${new Date(message.created_at).toLocaleString()}</span></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Message</h4>
                            <div class="bg-gray-50 rounded-lg p-6 text-gray-800 whitespace-pre-wrap border border-gray-200">${escapeHtml(message.message)}</div>
                        </div>

                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            ${message.status !== 'REPLIED' ? `
                                <button onclick="closeModal('viewMessageModal'); replyToMessage('${message.id}')"
                                        class="medical-btn-primary">
                                    📧 Reply
                                </button>
                            ` : ''}

                            <button onclick="updateMessageStatus('${message.id}', 'read')"
                                    class="medical-btn-secondary">
                                👁 Mark as Read
                            </button>
                        </div>
                    </div>
                `;

                document.getElementById('viewMessageModal').classList.remove('hidden');
                document.getElementById('viewMessageModal').focus();
            } else {
                alert('Failed to load message details.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load message details.');
        });
}

// Reply to message function
function replyToMessage(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php?action=get&id=${messageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.message;
                
                document.getElementById('replyMessageId').value = messageId;
                document.getElementById('replyMessage').value = '';
                
                const originalPreview = document.getElementById('originalMessagePreview');
                const originalContent = document.getElementById('originalMessageContent');
                
                originalContent.innerHTML = `
                    <div class="mb-2">
                        <strong>From:</strong> ${escapeHtml(message.name)} (${escapeHtml(message.email)})
                    </div>
                    <div class="mb-2">
                        <strong>Subject:</strong> ${message.subject ? escapeHtml(message.subject) : 'No subject'}
                    </div>
                    <div class="mb-2">
                        <strong>Date:</strong> ${new Date(message.created_at).toLocaleString()}
                    </div>
                    <div>
                        <strong>Message:</strong><br>
                        <div class="mt-1 pl-2 border-l-2 border-gray-400 italic">
                            ${escapeHtml(message.message)}
                        </div>
                    </div>
                `;
                
                originalPreview.classList.remove('hidden');
                document.getElementById('replyModal').classList.remove('hidden');

                setTimeout(() => {
                    document.getElementById('replyMessage').focus();
                }, 100);
            } else {
                showNotification('Failed to load message details for reply', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to load message details for reply', 'error');
        });
}

// Mark message as read function
function markAsRead(messageId) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            id: messageId,
            status: 'READ'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Message marked as read successfully!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Failed to mark message as read.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to mark message as read.', 'error');
    });
}

// Update message status
function updateMessageStatus(messageId, status) {
    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            id: messageId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update message status.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to update message status.');
    });
}

// Delete message function
function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this contact message?\n\nThis action cannot be undone and will permanently remove the customer\'s message from the system.')) {
        fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete',
                id: messageId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Contact message deleted successfully!', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification('Failed to delete message: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to delete message. Please try again.', 'error');
        });
    }
}

// Close modal function
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    
    if (modalId === 'replyModal') {
        document.getElementById('originalMessagePreview').classList.add('hidden');
        document.getElementById('replyMessage').value = '';
    }
}

// Reply form submission
document.getElementById('replyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const messageId = document.getElementById('replyMessageId').value;
    const replyMessage = document.getElementById('replyMessage').value;
    const submitButton = this.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;

    if (!replyMessage.trim()) {
        showNotification('Please enter a reply message.', 'error');
        return;
    }

    submitButton.disabled = true;
    submitButton.innerHTML = '📧 Sending Email...';

    fetch(`<?= getBasePath() ?>/api/admin/contact-messages.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'reply',
            id: messageId,
            reply_message: replyMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('replyModal');
            showNotification('✉️ ' + data.message, 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification('❌ Failed to send reply: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('❌ Failed to send reply. Please check your internet connection.', 'error');
    })
    .finally(() => {
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    });
});

// Show notification function
function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `notification-toast fixed top-4 right-4 px-6 py-3 rounded-2xl shadow-lg z-50 flex items-center transition-all duration-300 transform translate-x-full border-2`;

    if (type === 'success') {
        notification.className += ' bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300';
        notification.innerHTML = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>${message}`;
    } else if (type === 'error') {
        notification.className += ' bg-gradient-to-r from-red-500 to-rose-500 text-white border-red-300';
        notification.innerHTML = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>${message}`;
    } else {
        notification.className += ' bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-300';
        notification.innerHTML = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>${message}`;
    }

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = ['viewMessageModal', 'replyModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal && !modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>