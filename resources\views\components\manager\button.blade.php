@props([
    'variant' => 'primary',
    'size' => 'md',
    'icon' => null,
    'iconPosition' => 'left'
])

@php
$variantClasses = [
    'primary' => 'bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-white hover:shadow-md border-transparent',
    'secondary' => 'bg-white text-[#E98CA5] border-[#E98CA5] hover:bg-[#F7E9E6]',
    'outline' => 'bg-transparent text-[#4A4A52] border-[#EFEFEF] hover:border-[#E98CA5] hover:text-[#E98CA5]',
    'ghost' => 'bg-transparent text-[#4A4A52] border-transparent hover:bg-[#F7E9E6]',
    'danger' => 'bg-red-600 text-white hover:bg-red-700 border-transparent'
];

$sizeClasses = [
    'sm' => 'px-3 py-1.5 text-sm',
    'md' => 'px-4 py-2 text-sm',
    'lg' => 'px-6 py-3 text-base'
];

$baseClasses = 'inline-flex items-center justify-center gap-2 font-medium rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:ring-offset-2';
@endphp

<button {{ $attributes->except('variant')->merge(['class' => $baseClasses . ' ' . $variantClasses[$variant] . ' ' . $sizeClasses[$size]]) }}>
    @if($icon && $iconPosition === 'left')
        <flux:icon :icon="$icon" variant="outline" class="w-4 h-4" />
    @endif
    
    {{ $slot }}
    
    @if($icon && $iconPosition === 'right')
        <flux:icon :icon="$icon" variant="outline" class="w-4 h-4" />
    @endif
</button>
