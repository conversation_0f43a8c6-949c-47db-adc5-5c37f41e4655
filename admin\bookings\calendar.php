<?php
/**
 * Admin Bookings Calendar View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get current month and year
$currentMonth = (int)($_GET['month'] ?? date('n'));
$currentYear = (int)($_GET['year'] ?? date('Y'));

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = date('n');
}
if ($currentYear < 2020 || $currentYear > 2030) {
    $currentYear = date('Y');
}

// Get first and last day of the month
$firstDay = mktime(0, 0, 0, $currentMonth, 1, $currentYear);
$lastDay = mktime(0, 0, 0, $currentMonth + 1, 0, $currentYear);
$daysInMonth = date('t', $firstDay);
$startDayOfWeek = date('w', $firstDay); // 0 = Sunday

// Get bookings for the month
$bookings = $database->fetchAll(
    "SELECT b.*, u.name as customer_name, s.name as service_name, st.name as staff_name
     FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     WHERE YEAR(b.date) = ? AND MONTH(b.date) = ?
     GROUP BY b.id
     ORDER BY b.date, b.start_time",
    [$currentYear, $currentMonth]
);

// Group bookings by date
$bookingsByDate = [];
foreach ($bookings as $booking) {
    $day = (int)date('j', strtotime($booking['date']));
    if (!isset($bookingsByDate[$day])) {
        $bookingsByDate[$day] = [];
    }
    $bookingsByDate[$day][] = $booking;
}

$pageTitle = "Bookings Calendar";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Calendar Management CSS -->
<style>
/* Medical Calendar Specific Styles */
.medical-calendar-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.medical-calendar-header {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
    position: relative;
    overflow: hidden;
}

.medical-calendar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.medical-nav-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
}

.medical-calendar-grid {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
    border: 2px solid rgba(73, 167, 92, 0.1);
}

.medical-calendar-day-header {
    background: linear-gradient(135deg, #5894d2 0%, #6ba3e0 100%);
    color: white;
    padding: 16px;
    text-align: center;
    font-weight: 700;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.medical-calendar-day {
    min-height: 120px;
    border: 1px solid rgba(73, 167, 92, 0.1);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.medical-calendar-day:hover {
    background: linear-gradient(145deg, rgba(73, 167, 92, 0.05), rgba(73, 167, 92, 0.02));
    border-color: rgba(73, 167, 92, 0.2);
    transform: scale(1.02);
    z-index: 10;
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.15);
}

.medical-calendar-day.other-month {
    background: linear-gradient(145deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.5));
    color: #9ca3af;
}

.medical-calendar-day.today {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
    border: 2px solid #49a75c;
}

.medical-calendar-day-number {
    position: absolute;
    top: 8px;
    left: 12px;
    font-weight: 700;
    font-size: 16px;
    color: #374151;
}

.medical-calendar-day.today .medical-calendar-day-number {
    color: #49a75c;
    background: rgba(73, 167, 92, 0.1);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    left: 8px;
    top: 8px;
}

.medical-booking-item {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
    border: 1px solid rgba(73, 167, 92, 0.2);
    border-radius: 8px;
    padding: 4px 8px;
    margin: 2px 4px;
    font-size: 11px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
    cursor: pointer;
}

.medical-booking-item:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.2), rgba(73, 167, 92, 0.1));
    transform: translateY(-1px);
}

.medical-booking-item.confirmed {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border-color: rgba(59, 130, 246, 0.3);
    color: #1e40af;
}

.medical-booking-item.pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    border-color: rgba(245, 158, 11, 0.3);
    color: #92400e;
}

.medical-booking-item.completed {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    border-color: rgba(16, 185, 129, 0.3);
    color: #065f46;
}

.medical-booking-item.cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-color: rgba(239, 68, 68, 0.3);
    color: #991b1b;
}

.medical-btn-calendar {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.3);
    position: relative;
    overflow: hidden;
}

.medical-btn-calendar:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(73, 167, 92, 0.4);
}

.medical-btn-calendar-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    color: #374151;
    border: 2px solid rgba(73, 167, 92, 0.2);
    padding: 10px 20px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.medical-btn-calendar-secondary:hover {
    background: linear-gradient(135deg, #49a75c, #5cb85c);
    color: white;
    border-color: #49a75c;
    transform: translateY(-1px);
}

.medical-day-view-modal {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-calendar-container mb-8">
                        <div class="medical-calendar-header p-8">
                            <div class="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-white mb-2">Appointment Calendar</h1>
                                    <p class="text-white/80 text-lg"><?= date('F Y', $firstDay) ?></p>
                                    <p class="text-white/60 text-sm">Medical appointment scheduling overview</p>
                                </div>
                                <div class="mt-6 sm:mt-0 flex flex-col sm:flex-row gap-3">
                                    <a href="<?= getBasePath() ?>/admin/bookings" class="medical-btn-calendar-secondary text-center">
                                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                        List View
                                    </a>
                                    <a href="<?= getBasePath() ?>/admin/bookings/create" class="medical-btn-calendar">
                                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        New Appointment
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Calendar Navigation -->
                    <div class="medical-nav-container p-6 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center gap-6 mb-4 sm:mb-0">
                                <a href="?month=<?= $currentMonth == 1 ? 12 : $currentMonth - 1 ?>&year=<?= $currentMonth == 1 ? $currentYear - 1 : $currentYear ?>"
                                   class="flex items-center text-gray-600 hover:text-redolence-green transition-colors font-medium">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    Previous Month
                                </a>
                                <div class="text-center">
                                    <h2 class="text-2xl font-bold text-redolence-navy">
                                        <?= date('F Y', $firstDay) ?>
                                    </h2>
                                    <p class="text-gray-500 text-sm mt-1">
                                        <?= $daysInMonth ?> days • <?= count($bookings) ?> appointments
                                    </p>
                                </div>
                                <a href="?month=<?= $currentMonth == 12 ? 1 : $currentMonth + 1 ?>&year=<?= $currentMonth == 12 ? $currentYear + 1 : $currentYear ?>"
                                   class="flex items-center text-gray-600 hover:text-redolence-green transition-colors font-medium">
                                    Next Month
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                            <div class="flex items-center gap-3">
                                <a href="?month=<?= date('n') ?>&year=<?= date('Y') ?>"
                                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-redolence-green to-green-500 text-white rounded-xl font-semibold hover:from-green-600 hover:to-green-700 transition-all">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Current Month
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Calendar Grid -->
                    <div class="medical-calendar-grid">
                        <!-- Medical Calendar Header -->
                        <div class="grid grid-cols-7">
                            <div class="medical-calendar-day-header">Sunday</div>
                            <div class="medical-calendar-day-header">Monday</div>
                            <div class="medical-calendar-day-header">Tuesday</div>
                            <div class="medical-calendar-day-header">Wednesday</div>
                            <div class="medical-calendar-day-header">Thursday</div>
                            <div class="medical-calendar-day-header">Friday</div>
                            <div class="medical-calendar-day-header">Saturday</div>
                        </div>

                        <!-- Medical Calendar Body -->
                        <div class="grid grid-cols-7">
                            <?php
                            // Add empty cells for days before the first day of the month
                            for ($i = 0; $i < $startDayOfWeek; $i++) {
                                echo '<div class="medical-calendar-day other-month"></div>';
                            }

                            // Add cells for each day of the month
                            for ($day = 1; $day <= $daysInMonth; $day++) {
                                $isToday = ($day == date('j') && $currentMonth == date('n') && $currentYear == date('Y'));
                                $dayBookings = $bookingsByDate[$day] ?? [];
                                $dateString = sprintf('%04d-%02d-%02d', $currentYear, $currentMonth, $day);
                                $todayClass = $isToday ? ' today' : '';

                                echo '<div class="medical-calendar-day' . $todayClass . '" onclick="openDayView(\'' . $dateString . '\')">';
                                echo '<div class="medical-calendar-day-number">' . $day . '</div>';

                                if (count($dayBookings) > 0) {
                                    echo '<div class="absolute top-8 right-8 bg-gradient-to-r from-redolence-green to-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">' . count($dayBookings) . '</div>';
                                }

                                echo '<div class="mt-8 space-y-1 px-2">';

                                // Show bookings for this day (max 3 for better visibility)
                                $displayedBookings = array_slice($dayBookings, 0, 3);
                                foreach ($displayedBookings as $booking) {
                                    $statusClass = 'medical-booking-item ' . strtolower($booking['status']);

                                    echo '<div class="' . $statusClass . '" onclick="event.stopPropagation(); viewBooking(\'' . $booking['id'] . '\')" title="' . htmlspecialchars($booking['customer_name']) . ' - ' . htmlspecialchars($booking['service_name']) . '">';
                                    echo '<div class="flex items-center justify-between">';
                                    echo '<span class="font-bold">' . date('g:i A', strtotime($booking['start_time'])) . '</span>';
                                    echo '</div>';
                                    echo '<div class="truncate font-medium">' . htmlspecialchars($booking['customer_name']) . '</div>';
                                    echo '</div>';
                                }

                                // Show "more" indicator if there are more bookings
                                if (count($dayBookings) > 3) {
                                    echo '<div class="medical-booking-item" style="background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(156, 163, 175, 0.05)); border-color: rgba(156, 163, 175, 0.3); color: #6b7280;">';
                                    echo '<div class="text-center font-bold">+' . (count($dayBookings) - 3) . ' more</div>';
                                    echo '</div>';
                                }

                                echo '</div>';
                                echo '</div>';
                            }

                            // Add empty cells to complete the last week
                            $totalCells = $startDayOfWeek + $daysInMonth;
                            $remainingCells = 7 - ($totalCells % 7);
                            if ($remainingCells < 7) {
                                for ($i = 0; $i < $remainingCells; $i++) {
                                    echo '<div class="h-32 bg-gray-50 border-r border-b border-gray-200"></div>';
                                }
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Medical Legend -->
                    <div class="medical-calendar-container p-8 mt-8">
                        <h3 class="text-2xl font-bold text-redolence-navy mb-6">Status Legend & Instructions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold text-redolence-navy mb-4">Appointment Status</h4>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                    <div class="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">⏳</span>
                                        </div>
                                        <span class="text-sm font-medium text-yellow-700">Pending</span>
                                    </div>
                                    <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                        <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">✓</span>
                                        </div>
                                        <span class="text-sm font-medium text-blue-700">Confirmed</span>
                                    </div>
                                    <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                                        <div class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">🔄</span>
                                        </div>
                                        <span class="text-sm font-medium text-purple-700">In Progress</span>
                                    </div>
                                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">✅</span>
                                        </div>
                                        <span class="text-sm font-medium text-green-700">Completed</span>
                                    </div>
                                    <div class="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                                        <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">❌</span>
                                        </div>
                                        <span class="text-sm font-medium text-red-700">Cancelled</span>
                                    </div>
                                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                                        <div class="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">👻</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700">No Show</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-redolence-navy mb-4">How to Use</h4>
                                <div class="space-y-3">
                                    <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-redolence-green/10 to-green-100 rounded-lg">
                                        <svg class="w-5 h-5 text-redolence-green mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <span class="text-sm text-redolence-navy">Click on any day to view detailed appointments</span>
                                    </div>
                                    <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                                        <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-sm text-blue-700">Click on an appointment to view full details</span>
                                    </div>
                                    <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                                        <svg class="w-5 h-5 text-purple-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-sm text-purple-700">Green badge shows number of appointments per day</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Medical Day View Modal -->
<div id="dayViewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] backdrop-blur-sm">
    <div class="medical-day-view-modal w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
        <!-- Medical Modal Header -->
        <div class="bg-gradient-to-r from-redolence-green to-green-500 p-6 relative overflow-hidden">
            <div class="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
            <div class="relative z-10 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-white" id="dayViewTitle">Daily Appointments</h2>
                        <p class="text-white/80 text-sm" id="dayViewSubtitle">Loading appointment details...</p>
                    </div>
                </div>
                <button onclick="closeDayView()" class="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Medical Modal Content -->
        <div class="p-8 overflow-y-auto max-h-[calc(90vh-200px)] bg-gradient-to-br from-white to-gray-50" id="dayViewContent">
            <div class="text-center py-16">
                <div class="w-16 h-16 bg-gradient-to-r from-redolence-green to-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                    <svg class="w-8 h-8 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-redolence-navy mb-2">Loading Appointments</h3>
                <p class="text-gray-500">Please wait while we fetch the appointment details...</p>
            </div>
        </div>

        <!-- Medical Modal Footer -->
        <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <div class="flex gap-3">
                    <button onclick="createNewBooking()" class="medical-btn-calendar">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Appointment
                    </button>
                    <button onclick="refreshDayView()" class="medical-btn-calendar-secondary">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
                <button onclick="closeDayView()" class="medical-btn-calendar-secondary">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modern Notification System -->
<div id="notificationContainer" class="fixed top-4 right-4 z-[80] space-y-2"></div>

<script>
// Global variables
let currentViewDate = null;

// Modern Notification System
function showNotification(message, type = 'success', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');

    // Set notification styles based on type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>`;
            break;
        case 'error':
            bgColor = 'bg-red-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
            break;
        default:
            bgColor = 'bg-gray-600';
            textColor = 'text-white';
            icon = '';
    }

    notification.className = `${bgColor} ${textColor} px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex-shrink-0">${icon}</div>
        <div class="flex-1">
            <p class="font-medium">${message}</p>
        </div>
        <button onclick="removeNotification(this.parentElement)" class="flex-shrink-0 ml-4 text-white hover:text-gray-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
        notification.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }

    return notification;
}

function removeNotification(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

function viewBooking(bookingId) {
    window.location.href = `<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}`;
}

function createNewBooking() {
    const url = new URL('<?= getBasePath() ?>/admin/bookings/create', window.location.origin);
    if (currentViewDate) {
        url.searchParams.set('date', currentViewDate);
    }
    window.location.href = url.toString();
}

// Day View Modal Functions
function openDayView(date) {
    console.log('📅 Opening day view for:', date);
    currentViewDate = date;

    // Show modal
    document.getElementById('dayViewModal').classList.remove('hidden');

    // Update title
    const dateObj = new Date(date + 'T00:00:00');
    const formattedDate = dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    document.getElementById('dayViewTitle').textContent = `Bookings for ${formattedDate}`;
    document.getElementById('dayViewSubtitle').textContent = 'Loading bookings...';

    // Load bookings for the day
    loadDayBookings(date);
}

function closeDayView() {
    document.getElementById('dayViewModal').classList.add('hidden');
    currentViewDate = null;
}

function refreshDayView() {
    if (currentViewDate) {
        loadDayBookings(currentViewDate);
    }
}

function loadDayBookings(date) {
    fetch(`<?= getBasePath() ?>/api/admin/day-bookings.php?date=${date}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDayBookings(data.bookings, data.summary, date);
            } else {
                document.getElementById('dayViewContent').innerHTML = `
                    <div class="text-center py-16">
                        <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-red-700 mb-2">Error Loading Appointments</h3>
                        <p class="text-red-600 mb-6">${data.error}</p>
                        <button onclick="loadDayBookings('${date}')" class="medical-btn-calendar">Retry</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('dayViewContent').innerHTML = `
                <div class="text-center py-16">
                    <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-red-700 mb-2">Connection Error</h3>
                    <p class="text-red-600 mb-6">Unable to load appointments. Please check your connection.</p>
                    <button onclick="loadDayBookings('${date}')" class="medical-btn-calendar">Retry</button>
                </div>
            `;
        });
}

function displayDayBookings(bookings, summary, date) {
    console.log('📋 Displaying bookings for', date, ':', bookings.length, 'bookings');

    // Update subtitle with summary
    const revenueText = summary.completed_bookings > 0
        ? `Revenue from ${summary.completed_bookings} completed: ${formatCurrency(summary.total_revenue)}`
        : 'No completed bookings yet';
    const subtitle = `${summary.total_bookings} booking${summary.total_bookings !== 1 ? 's' : ''} • ${revenueText}`;
    document.getElementById('dayViewSubtitle').textContent = subtitle;

    if (bookings.length === 0) {
        document.getElementById('dayViewContent').innerHTML = `
            <div class="text-center py-16">
                <div class="w-20 h-20 bg-gradient-to-r from-redolence-green to-green-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-redolence-navy mb-3">No Appointments Scheduled</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">This day is completely free. Perfect opportunity to schedule new patient appointments and maximize your availability.</p>
                <button onclick="createNewBooking()" class="medical-btn-calendar">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Schedule First Appointment
                </button>
            </div>
        `;
        return;
    }

    // Create medical summary cards
    let summaryHtml = `
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="medical-appointment-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-blue-700">Total Appointments</p>
                        <p class="text-2xl font-bold text-blue-900">${summary.total_bookings}</p>
                    </div>
                </div>
            </div>

            <div class="medical-appointment-card bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-green-700">Total Revenue</p>
                        <p class="text-2xl font-bold text-green-900">${formatCurrency(summary.total_revenue)}</p>
                        ${summary.completed_bookings > 0 ? `<p class="text-xs text-green-600">${summary.completed_bookings} completed</p>` : '<p class="text-xs text-green-600">No completed yet</p>'}
                    </div>
                </div>
            </div>

            <div class="medical-appointment-card bg-gradient-to-br from-yellow-50 to-amber-100 border-yellow-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-yellow-700">Pending</p>
                        <p class="text-2xl font-bold text-yellow-900">${summary.status_counts.PENDING || 0}</p>
                    </div>
                </div>
            </div>

            <div class="medical-appointment-card bg-gradient-to-br from-emerald-50 to-green-100 border-emerald-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-emerald-700">Completed</p>
                        <p class="text-2xl font-bold text-emerald-900">${summary.status_counts.COMPLETED || 0}</p>
                        <p class="text-xs text-emerald-600">Revenue generating</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Create bookings list
    let bookingsHtml = '<div class="space-y-4">';

    bookings.forEach(booking => {
        const statusInfo = getStatusInfo(booking.status);
        const serviceName = booking.service_name || booking.package_name || 'Unknown Service';
        const serviceType = booking.service_name ? 'Service' : 'Package';

        bookingsHtml += `
            <div class="medical-appointment-card hover:shadow-lg transition-all duration-200 cursor-pointer transform hover:scale-[1.02]" onclick="viewBooking('${booking.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-14 h-14 ${statusInfo.bgColor} rounded-xl flex items-center justify-center shadow-lg">
                                <span class="text-white font-bold text-lg">${statusInfo.icon}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-3">
                                <h3 class="text-xl font-bold text-redolence-navy truncate">${booking.customer_name}</h3>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.badgeClass}">
                                    ${booking.status}
                                </span>
                            </div>
                            <div class="mt-2 flex items-center space-x-6 text-sm text-gray-600">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    ${formatTime(booking.start_time)} - ${formatTime(booking.end_time)}
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    ${booking.staff_name || 'Unassigned'}
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    ${serviceName} (${serviceType})
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="text-lg font-semibold text-salon-gold">${formatCurrency(booking.total_amount)}</div>
                            ${booking.points_used > 0 ? `<div class="text-xs text-gray-400">${booking.points_used} points used</div>` : ''}
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
                ${booking.notes ? `<div class="mt-4 text-sm text-gray-600 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200"><strong class="text-blue-700">Notes:</strong> ${booking.notes}</div>` : ''}
            </div>
        `;
    });

    bookingsHtml += '</div>';

    document.getElementById('dayViewContent').innerHTML = summaryHtml + bookingsHtml;
}

// Helper functions
function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

function formatTime(timeString) {
    const time = new Date('2000-01-01 ' + timeString);
    return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function getStatusInfo(status) {
    const statusMap = {
        'PENDING': {
            icon: '⏳',
            bgColor: 'bg-yellow-600',
            badgeClass: 'bg-yellow-100 text-yellow-800'
        },
        'CONFIRMED': {
            icon: '✓',
            bgColor: 'bg-blue-600',
            badgeClass: 'bg-blue-100 text-blue-800'
        },
        'IN_PROGRESS': {
            icon: '🔄',
            bgColor: 'bg-purple-600',
            badgeClass: 'bg-purple-100 text-purple-800'
        },
        'COMPLETED': {
            icon: '✅',
            bgColor: 'bg-green-600',
            badgeClass: 'bg-green-100 text-green-800'
        },
        'CANCELLED': {
            icon: '❌',
            bgColor: 'bg-red-600',
            badgeClass: 'bg-red-100 text-red-800'
        },
        'NO_SHOW': {
            icon: '👻',
            bgColor: 'bg-gray-600',
            badgeClass: 'bg-gray-100 text-gray-800'
        },
        'EXPIRED': {
            icon: '⏰',
            bgColor: 'bg-orange-600',
            badgeClass: 'bg-orange-100 text-orange-800'
        }
    };

    return statusMap[status] || {
        icon: '❓',
        bgColor: 'bg-gray-600',
        badgeClass: 'bg-gray-100 text-gray-800'
    };
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDayView();
    }
});

// Close modal on backdrop click
document.getElementById('dayViewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDayView();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
