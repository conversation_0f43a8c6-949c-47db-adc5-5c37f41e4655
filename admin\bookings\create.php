<?php
/**
 * Admin Create Booking
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/booking_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = createBooking($_POST);

    if ($result['success']) {
        $_SESSION['success'] = 'Booking created successfully!';
        redirect('/admin/bookings/view.php?id=' . $result['id']);
    } else {
        $_SESSION['error'] = $result['error'];
    }
}

// Get customers, services, and staff for form
$customers = $database->fetchAll("SELECT id, name, email FROM users WHERE role = 'CUSTOMER' ORDER BY name");
$services = $database->fetchAll("SELECT id, name, price, duration FROM services WHERE is_active = 1 ORDER BY name");
$staff = $database->fetchAll("SELECT id, name FROM users WHERE role = 'STAFF' ORDER BY name");

// Get pre-filled date from URL parameter (from calendar)
$prefilledDate = $_GET['date'] ?? null;
if ($prefilledDate && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $prefilledDate)) {
    $prefilledDate = null; // Invalid format, ignore
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Create New Booking";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="medical-rooms-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">Create New Booking</h1>
                                <p class="mt-2 text-sm text-gray-600">Schedule a new appointment for a patient</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="inline-flex items-center px-5 py-2.5 rounded-xl bg-white text-redolence-blue border border-redolence-blue hover:bg-gray-50 transition-all font-semibold text-sm uppercase tracking-wide">
                                    Back to Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-green-50 border border-green-100 text-green-700' : 'bg-red-50 border border-red-100 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Booking Form -->
                    <div class="medical-rooms-card p-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Booking Details</h2>
                        <form method="POST" id="bookingForm" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Customer Selection -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Patient *</label>
                                    <select name="user_id" id="customerId" required 
                                            class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green placeholder-gray-400">
                                        <option value="">Select a patient</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?= $customer['id'] ?>"><?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Service Selection -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Treatment/Service *</label>
                                    <select name="service_id" id="serviceId" required 
                                            class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green placeholder-gray-400">
                                        <option value="">Select a treatment</option>
                                        <?php foreach ($services as $service): ?>
                                            <option value="<?= $service['id'] ?>" data-price="<?= $service['price'] ?>" data-duration="<?= $service['duration'] ?>">
                                                <?= htmlspecialchars($service['name']) ?> - <?= formatCurrency($service['price']) ?> (<?= $service['duration'] ?> min)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Staff Selection -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Medical Staff *</label>
                                    <select name="staff_id" id="staffId" required 
                                            class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green placeholder-gray-400">
                                        <option value="">Select staff member</option>
                                        <?php foreach ($staff as $staffMember): ?>
                                            <option value="<?= $staffMember['id'] ?>"><?= htmlspecialchars($staffMember['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Date -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Date *</label>
                                    <input type="date" name="date" id="bookingDate" required min="<?= date('Y-m-d') ?>"
                                           value="<?= $prefilledDate ? htmlspecialchars($prefilledDate) : '' ?>"
                                           class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green">
                                    <?php if ($prefilledDate): ?>
                                        <p class="text-xs text-redolence-green mt-1">📅 Date pre-filled from calendar</p>
                                    <?php endif; ?>
                                </div>

                                <!-- Start Time -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Start Time *</label>
                                    <input type="time" name="start_time" id="startTime" required
                                           class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green">
                                </div>

                                <!-- End Time (Auto-calculated) -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">End Time</label>
                                    <input type="time" name="end_time" id="endTime" readonly
                                           class="w-full px-4 py-3 bg-gray-100 border border-gray-200 rounded-xl text-gray-600 cursor-not-allowed">
                                </div>

                                <!-- Total Amount -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Total Amount</label>
                                    <input type="number" name="total_amount" id="totalAmount" step="1" readonly
                                           class="w-full px-4 py-3 bg-gray-100 border border-gray-200 rounded-xl text-gray-600 cursor-not-allowed">
                                </div>

                                <!-- Points Used -->
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Points to Use</label>
                                    <input type="number" name="points_used" id="pointsUsed" min="0" value="0"
                                           class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-redolence-green">
                                    <p class="text-xs text-gray-500 mt-1">10 points = TSH 1,000 discount</p>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mt-6">
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Notes</label>
                                <textarea name="notes" rows="4" 
                                          class="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green"
                                          placeholder="Any special notes or requirements for this appointment..."></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-8 flex gap-4">
                                <button type="submit" 
                                        class="flex-1 bg-redolence-green text-white py-3 px-6 rounded-xl font-semibold hover:bg-redolence-green/90 transition-all uppercase tracking-wide text-sm">
                                    Create Booking
                                </button>
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="flex-1 bg-white text-redolence-navy border border-gray-200 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 transition-all uppercase tracking-wide text-sm text-center">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Modern Notification System -->
<div id="notificationContainer" class="fixed top-4 right-4 z-[80] space-y-2"></div>

<!-- Custom Styles for Conflict Indicators -->
<style>
.border-yellow-500 {
    border-color: #eab308 !important;
    box-shadow: 0 0 0 1px #eab308;
}

.border-red-500 {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 1px #ef4444;
}

.conflict-warning {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}
</style>

<script>
// Modern Notification System
function showNotification(message, type = 'success', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');

    // Set notification styles based on type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>`;
            break;
        case 'error':
            bgColor = 'bg-red-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
            break;
        default:
            bgColor = 'bg-gray-600';
            textColor = 'text-white';
            icon = '';
    }

    notification.className = `${bgColor} ${textColor} px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex-shrink-0">${icon}</div>
        <div class="flex-1">
            <p class="font-medium">${message}</p>
        </div>
        <button onclick="removeNotification(this.parentElement)" class="flex-shrink-0 ml-4 text-white hover:text-gray-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
        notification.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }

    return notification;
}

function removeNotification(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Booking conflict checking and form functionality
document.addEventListener('DOMContentLoaded', function() {
    const serviceSelect = document.getElementById('serviceId');
    const staffSelect = document.getElementById('staffId');
    const dateInput = document.getElementById('bookingDate');
    const startTimeInput = document.getElementById('startTime');
    const endTimeInput = document.getElementById('endTime');
    const totalAmountInput = document.getElementById('totalAmount');
    const pointsUsedInput = document.getElementById('pointsUsed');

    let conflictCheckTimeout = null;

    // Update end time and total when service or start time changes
    function updateBookingDetails() {
        const selectedService = serviceSelect.options[serviceSelect.selectedIndex];
        const startTime = startTimeInput.value;
        
        if (selectedService.value && startTime) {
            const duration = parseInt(selectedService.dataset.duration);
            const price = parseInt(selectedService.dataset.price);
            
            // Calculate end time
            const [hours, minutes] = startTime.split(':').map(Number);
            const startMinutes = hours * 60 + minutes;
            const endMinutes = startMinutes + duration;
            const endHours = Math.floor(endMinutes / 60);
            const endMins = endMinutes % 60;
            
            endTimeInput.value = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
            
            // Update total amount
            updateTotalAmount(price);
        }
    }

    function updateTotalAmount(basePrice) {
        const pointsUsed = parseInt(pointsUsedInput.value) || 0;
        const discount = pointsUsed * 100; // 10 points = TSH 1,000
        const total = Math.max(0, basePrice - discount);
        totalAmountInput.value = parseInt(total);
    }

    // Check for booking conflicts
    function checkBookingConflict() {
        const staffId = staffSelect.value;
        const date = dateInput.value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;

        // Clear previous timeout
        if (conflictCheckTimeout) {
            clearTimeout(conflictCheckTimeout);
        }

        // Only check if all required fields are filled
        if (!staffId || !date || !startTime || !endTime) {
            return;
        }

        // Debounce the conflict check
        conflictCheckTimeout = setTimeout(() => {
            const conflictData = {
                staff_id: staffId,
                date: date,
                start_time: startTime + ':00', // Add seconds
                end_time: endTime + ':00'      // Add seconds
            };

            fetch('<?= getBasePath() ?>/api/admin/check-booking-conflict.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(conflictData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.has_conflict) {
                        // Show conflict warning with clearer message
                        const conflictMessage = `${data.staff_name} is already booked at this time. Please choose a different time or date.`;
                        showNotification(conflictMessage, 'warning', 8000);

                        // Add visual indication to form fields
                        startTimeInput.classList.add('border-yellow-500');
                        endTimeInput.classList.add('border-yellow-500');

                        // Show conflict details in console for debugging
                        console.log('Booking conflicts found:', data.conflicts);
                    } else {
                        // Clear any previous conflict indicators
                        startTimeInput.classList.remove('border-yellow-500');
                        endTimeInput.classList.remove('border-yellow-500');
                    }
                } else {
                    console.error('Conflict check error:', data.error);
                }
            })
            .catch(error => {
                console.error('Error checking booking conflict:', error);
            });
        }, 500); // 500ms delay to avoid too many API calls
    }

    // Event listeners for booking details
    serviceSelect.addEventListener('change', function() {
        updateBookingDetails();
        checkBookingConflict();
    });

    startTimeInput.addEventListener('change', function() {
        updateBookingDetails();
        checkBookingConflict();
    });

    // Event listeners for conflict checking
    staffSelect.addEventListener('change', checkBookingConflict);
    dateInput.addEventListener('change', checkBookingConflict);
    endTimeInput.addEventListener('change', checkBookingConflict);

    pointsUsedInput.addEventListener('input', function() {
        const selectedService = serviceSelect.options[serviceSelect.selectedIndex];
        if (selectedService.value) {
            const price = parseInt(selectedService.dataset.price);
            updateTotalAmount(price);
        }
    });

    // Form validation with modern notifications
    document.getElementById('bookingForm').addEventListener('submit', function(e) {
        const requiredFields = [
            { name: 'user_id', label: 'Patient' },
            { name: 'service_id', label: 'Treatment/Service' },
            { name: 'staff_id', label: 'Medical Staff' },
            { name: 'date', label: 'Date' },
            { name: 'start_time', label: 'Start Time' }
        ];

        let isValid = true;
        let missingFields = [];

        requiredFields.forEach(fieldInfo => {
            const field = document.querySelector(`[name="${fieldInfo.name}"]`);
            if (!field.value.trim()) {
                isValid = false;
                missingFields.push(fieldInfo.label);
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Ensure end_time and total_amount are calculated
        const endTimeInput = document.getElementById('endTime');
        const totalAmountInput = document.getElementById('totalAmount');

        if (!endTimeInput.value || !totalAmountInput.value) {
            updateBookingDetails(); // Try to calculate them
        }

        if (!isValid) {
            e.preventDefault();
            const message = `Please fill in the following required fields: ${missingFields.join(', ')}`;
            showNotification(message, 'error', 6000);
            return;
        }

        // Check if there are any conflict warnings
        if (startTimeInput.classList.contains('border-yellow-500')) {
            e.preventDefault();
            showNotification('Cannot create booking - specialist is already booked at this time. Please choose a different time or date.', 'error', 8000);
            return;
        }

        // Show success message
        showNotification('Creating booking...', 'info', 2000);
    });
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
