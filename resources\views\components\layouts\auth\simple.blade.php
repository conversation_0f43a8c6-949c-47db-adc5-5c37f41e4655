<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">

<head>
    @include('partials.head')
</head>

<body
    class="min-h-screen font-sans antialiased bg-[#FDF2F4] bg-gradient-to-br from-[#FDF2F4] via-[#FAE1E5] to-[#FDF2F4] relative overflow-hidden">
    <!-- Decorative Background Elements -->
    <div class="fixed top-0 left-0 w-full h-full overflow-hidden pointer-events-none z-0">
        <div class="absolute top-[-10%] right-[-5%] w-96 h-96 bg-[#E98CA5]/20 rounded-full blur-3xl"></div>
        <div class="absolute bottom-[-10%] left-[-5%] w-96 h-96 bg-[#C85E78]/10 rounded-full blur-3xl"></div>
    </div>

    <div class="relative z-10 flex min-h-screen flex-col items-center justify-center p-6 md:p-10">
        <div class="w-full max-w-[400px] flex flex-col gap-8">
            <!-- Logo -->
            <a href="{{ route('home') }}" class="flex justify-center transition-transform hover:scale-105 duration-300"
                wire:navigate>
                <img src="{{ asset('1.png') }}" alt="{{ config('app.name') }}"
                    class="h-16 md:h-20 w-auto object-contain drop-shadow-sm">
            </a>

            <!-- Card -->
            <div
                class="flex flex-col gap-6 bg-white/60 backdrop-blur-xl p-8 rounded-3xl shadow-xl ring-1 ring-white/50">
                {{ $slot }}
            </div>
        </div>
    </div>

    <!-- Fonts for Premium Theme -->
    <link
        href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,600;0,700;1,400&family=Montserrat:wght@300;400;500;600&display=swap"
        rel="stylesheet">

    @fluxScripts
</body>

</html>