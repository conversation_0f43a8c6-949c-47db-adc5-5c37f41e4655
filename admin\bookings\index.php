<?php
/**
 * Patient Appointments Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/booking_expiration.php';

// Run expiration check if needed
$expirationResult = runExpirationCheckIfNeeded();
if ($expirationResult && $expirationResult['expired_count'] > 0) {
    $_SESSION['success'] = "{$expirationResult['expired_count']} pending appointments have been marked as expired.";
}

// Check if user is medical admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $bookingId = $_POST['booking_id'] ?? '';

    if ($action === 'update_status' && $bookingId) {
        $newStatus = $_POST['status'] ?? '';
        $result = updateBookingStatus($bookingId, $newStatus);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient appointment status updated successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/bookings');
    } elseif ($action === 'delete' && $bookingId) {
        $result = deleteBooking($bookingId);

        if ($result['success']) {
            $_SESSION['success'] = 'Patient appointment deleted successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/bookings');
    }
}

// Get patient appointments with filters
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;
$status = sanitize($_GET['status'] ?? '');
$date = sanitize($_GET['date'] ?? '');
$search = sanitize($_GET['search'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($status) {
    $whereClause .= " AND b.status = ?";
    $params[] = $status;
}

if ($date) {
    $whereClause .= " AND DATE(b.date) = ?";
    $params[] = $date;
}

if ($search) {
    $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ? OR s.name LIKE ? OR sv.name LIKE ? OR p.name LIKE ? OR st.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$appointments = $database->fetchAll(
    "SELECT b.id, b.user_id, b.service_id, b.service_variation_id, b.package_id, b.staff_id, b.room_id,
            b.date, b.start_time, b.end_time, b.status, b.total_amount, b.notes,
            b.offer_id, b.points_used, b.created_at, b.updated_at,
            u.name as patient_name, u.email as patient_email, u.phone as patient_phone,
            s.name as treatment_name, s.price as treatment_price, s.duration as treatment_duration,
            sv.name as variation_name, sv.price as variation_price, sv.duration as variation_duration,
            p.name as package_name, p.price as package_price,
            st.name as specialist_name, st.email as specialist_email,
            r.name as room_name, r.type as room_type
     FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
     LEFT JOIN packages p ON b.package_id = p.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     LEFT JOIN rooms r ON b.room_id = r.id
     $whereClause
     GROUP BY b.id, b.user_id, b.service_id, b.service_variation_id, b.package_id, b.staff_id, b.room_id,
              b.date, b.start_time, b.end_time, b.status, b.total_amount, b.notes,
              b.offer_id, b.points_used, b.created_at, b.updated_at,
              u.name, u.email, u.phone, s.name, s.price, s.duration,
              sv.name, sv.price, sv.duration, p.name, p.price, st.name, st.email,
              r.name, r.type
     ORDER BY b.date DESC, b.start_time DESC
     LIMIT $limit OFFSET $offset",
    $params
);

// Calculate duration for package appointments and handle variations
foreach ($appointments as $index => $appointment) {
    if (!empty($appointment['package_id']) && empty($appointment['treatment_duration'])) {
        // Get total duration for package
        $packageServices = $database->fetchAll("
            SELECT s.duration
            FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
        ", [$appointment['package_id']]);

        $appointments[$index]['treatment_duration'] = array_sum(array_column($packageServices, 'duration'));
    } elseif (!empty($appointment['variation_duration'])) {
        // Use variation duration if available
        $appointments[$index]['treatment_duration'] = $appointment['variation_duration'];
    }
}

$totalAppointments = $database->fetch(
    "SELECT COUNT(DISTINCT b.id) as count FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
     LEFT JOIN packages p ON b.package_id = p.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     $whereClause",
    $params
)['count'];

$totalPages = ceil($totalAppointments / $limit);

// Get appointment statistics
$stats = getBookingStats();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Patient Appointments Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Appointments Management CSS -->
<style>
/* Medical Appointments Specific Styles */
.medical-appointment-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-appointment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-appointment-card:hover::before {
    left: 100%;
}

.medical-appointment-card:hover {
    transform: translateY(-4px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.medical-stats-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-filter-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-header-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.patient-avatar {
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
}

.medical-status-pending {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
    color: #d97706;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.medical-status-confirmed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.medical-status-completed {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.medical-status-cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medical-status-no_show {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(107, 114, 128, 0.05));
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.medical-status-expired {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(156, 163, 175, 0.05));
    color: #9ca3af;
    border: 1px solid rgba(156, 163, 175, 0.3);
}

.medical-status-in_progress {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(168, 85, 247, 0.05));
    color: #7c3aed;
    border: 1px solid rgba(168, 85, 247, 0.3);
}

.medical-btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.medical-btn-primary:hover::before {
    left: 100%;
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-btn-secondary {
    background: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.medical-table {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.medical-table-header {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    border-bottom: 2px solid rgba(73, 167, 92, 0.1);
}

.medical-table-row {
    border-bottom: 1px solid rgba(73, 167, 92, 0.05);
    transition: all 0.3s ease;
}

.medical-table-row:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.02), rgba(88, 148, 210, 0.02));
}

.medical-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.medical-action-view {
    background: linear-gradient(135deg, var(--primary-blue), #3b82f6);
    color: white;
}

.medical-action-view:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(88, 148, 210, 0.3);
}

.medical-action-edit {
    background: linear-gradient(135deg, var(--primary-green), #10b981);
    color: white;
}

.medical-action-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(73, 167, 92, 0.3);
}

.medical-action-status {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.medical-action-status:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
}

.medical-action-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-action-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-pagination {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
}

.medical-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(25px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 24px;
}

@media (max-width: 768px) {
    .medical-appointment-card {
        border-radius: 16px;
        padding: 1rem;
    }
    
    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }
    
    .medical-table {
        border-radius: 16px;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                
                    <!-- Medical Header -->
                    <div class="medical-header-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Patient Appointments
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Manage patient appointments and treatment schedules</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $totalAppointments ?> Total Appointments
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        Medical Scheduling
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <a href="<?= getBasePath() ?>/admin/bookings/calendar.php"
                                   class="medical-btn-secondary inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Calendar View
                                </a>
                                <a href="<?= getBasePath() ?>/admin/bookings/create.php"
                                   class="medical-btn-primary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    New Appointment
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-4 rounded-xl border-2 <?= $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <?php if ($messageType === 'success'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php else: ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php endif; ?>
                                </svg>
                                <?= htmlspecialchars($message) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Appointments</dt>
                                        <dd class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                        <dd class="text-2xl font-bold text-yellow-600"><?= number_format($stats['pending']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Confirmed</dt>
                                        <dd class="text-2xl font-bold text-green-600"><?= number_format($stats['confirmed']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Today's Revenue</dt>
                                        <dd class="text-2xl font-bold text-redolence-green"><?= formatCurrency($stats['today_revenue']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-6 mb-8">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                           placeholder="Search by patient, treatment, or specialist..."
                                           class="w-full pl-10 pr-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                            </div>
                            <div>
                                <select name="status" class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                    <option value="">All Status</option>
                                    <option value="PENDING" <?= $status === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                                    <option value="CONFIRMED" <?= $status === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                                    <option value="COMPLETED" <?= $status === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                                    <option value="CANCELLED" <?= $status === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                                    <option value="NO_SHOW" <?= $status === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                                    <option value="EXPIRED" <?= $status === 'EXPIRED' ? 'selected' : '' ?>>Expired</option>
                                </select>
                            </div>
                            <div>
                                <input type="date" name="date" value="<?= htmlspecialchars($date) ?>"
                                       class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                            </div>
                            <button type="submit" class="medical-btn-primary">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Filter
                            </button>
                            <?php if ($search || $status || $date): ?>
                                <a href="<?= getBasePath() ?>/admin/bookings" class="medical-btn-secondary">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Clear
                                </a>
                            <?php endif; ?>
                        </form>
                    </div>

                    <!-- Medical Appointments Table -->
                    <div class="medical-table">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="medical-table-header">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Patient</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Treatment</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Specialist</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Room</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Date & Time</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-4 text-left text-xs font-semibold text-redolence-navy uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <?php foreach ($appointments as $appointment): ?>
                                        <tr class="medical-table-row">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="patient-avatar h-10 w-10">
                                                            <?= strtoupper(substr($appointment['patient_name'], 0, 2)) ?>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-semibold text-redolence-navy"><?= htmlspecialchars($appointment['patient_name']) ?></div>
                                                        <div class="text-sm text-gray-500"><?= htmlspecialchars($appointment['patient_email']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-redolence-navy font-medium">
                                                    <?php if (!empty($appointment['treatment_name'])): ?>
                                                        <?= htmlspecialchars($appointment['treatment_name']) ?>
                                                        <?php if (!empty($appointment['variation_name'])): ?>
                                                            <span class="text-xs bg-redolence-blue text-white px-2 py-1 rounded-full ml-2">
                                                                <?= htmlspecialchars($appointment['variation_name']) ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    <?php elseif (!empty($appointment['package_name'])): ?>
                                                        <?= htmlspecialchars($appointment['package_name']) ?>
                                                        <span class="text-xs bg-redolence-green text-white px-2 py-1 rounded-full ml-2">PACKAGE</span>
                                                    <?php else: ?>
                                                        Unknown Treatment
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?php
                                                    $duration = !empty($appointment['variation_duration']) ? $appointment['variation_duration'] : $appointment['treatment_duration'];
                                                    echo $duration;
                                                    ?> min
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-redolence-navy font-medium"><?= htmlspecialchars($appointment['specialist_name'] ?? 'Unassigned') ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if (!empty($appointment['room_name'])): ?>
                                                    <div class="text-sm text-redolence-navy font-medium"><?= htmlspecialchars($appointment['room_name']) ?></div>
                                                    <div class="text-xs text-gray-500"><?= htmlspecialchars($appointment['room_type']) ?></div>
                                                <?php else: ?>
                                                    <div class="text-sm text-red-500">No Room</div>
                                                    <div class="text-xs text-gray-400">Assignment Required</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-redolence-navy font-medium"><?= date('M j, Y', strtotime($appointment['date'])) ?></div>
                                                <div class="text-sm text-gray-500"><?= date('g:i A', strtotime($appointment['start_time'])) ?> - <?= date('g:i A', strtotime($appointment['end_time'])) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-bold text-redolence-green"><?= formatCurrency($appointment['total_amount']) ?></div>
                                                <?php if (isset($appointment['points_used']) && $appointment['points_used'] > 0): ?>
                                                    <div class="text-xs text-gray-400"><?= $appointment['points_used'] ?> points used</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $statusInfo = getBookingStatusInfo($appointment['status']);
                                                $statusClass = 'medical-status-' . strtolower($appointment['status']);
                                                ?>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?= $statusClass ?>">
                                                    <?= $statusInfo['label'] ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="flex gap-2">
                                                    <button onclick="viewBooking('<?= $appointment['id'] ?>')"
                                                            class="medical-action-btn medical-action-view text-xs">
                                                        View
                                                    </button>
                                                    <button onclick="editBooking('<?= $appointment['id'] ?>')"
                                                            class="medical-action-btn medical-action-edit text-xs">
                                                        Edit
                                                    </button>
                                                    <button onclick="updateStatus('<?= $appointment['id'] ?>', '<?= $appointment['status'] ?>')"
                                                            class="medical-action-btn medical-action-status text-xs">
                                                        Status
                                                    </button>
                                                    <?php if (in_array($appointment['status'], ['CANCELLED', 'NO_SHOW'])): ?>
                                                        <button onclick="deleteBooking('<?= $appointment['id'] ?>')"
                                                                class="medical-action-btn medical-action-delete text-xs">
                                                            Delete
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Medical Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="medical-pagination px-6 py-4 flex items-center justify-between border-t border-gray-100">
                                <div class="flex-1 flex justify-between sm:hidden">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                           class="medical-btn-secondary">
                                            Previous
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                           class="medical-btn-secondary">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p class="text-sm text-gray-600">
                                            Showing <span class="font-semibold text-redolence-green"><?= $offset + 1 ?></span> to 
                                            <span class="font-semibold text-redolence-green"><?= min($offset + $limit, $totalAppointments) ?></span> of 
                                            <span class="font-semibold text-redolence-green"><?= $totalAppointments ?></span> patient appointments
                                        </p>
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
                                            <?php for ($i = 1; $i <= min($totalPages, 10); $i++): ?>
                                                <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                                   class="relative inline-flex items-center px-4 py-2 border text-sm font-semibold transition-all <?= $i === $page ? 'z-10 bg-redolence-green border-redolence-green text-white' : 'bg-white border-gray-300 text-gray-700 hover:bg-redolence-green/10 hover:border-redolence-green hover:text-redolence-green' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-modal p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-redolence-navy">Update Appointment Status</h2>
            <button onclick="closeStatusModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="statusForm" method="POST">
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="booking_id" id="statusBookingId">
            
            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-navy mb-3">New Status</label>
                <select name="status" id="newStatus" required 
                        class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                    <option value="PENDING">Pending</option>
                    <option value="CONFIRMED">Confirmed</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="CANCELLED">Cancelled</option>
                    <option value="NO_SHOW">No Show</option>
                    <option value="EXPIRED">Expired</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Update Status
                </button>
                <button type="button" onclick="closeStatusModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function updateStatus(bookingId, currentStatus) {
    document.getElementById('statusBookingId').value = bookingId;
    document.getElementById('newStatus').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function viewBooking(bookingId) {
    window.location.href = `<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}`;
}

function editBooking(bookingId) {
    window.location.href = `<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}&edit=1`;
}

function deleteBooking(bookingId) {
    if (confirm('Are you sure you want to delete this patient appointment? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="booking_id" value="${bookingId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStatusModal();
    }
});

// Close modal on backdrop click
document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStatusModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>