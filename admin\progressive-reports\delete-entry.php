<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle POST request only
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('/admin/progressive-reports');
}

$entryId = $_POST['entry_id'] ?? '';
$reportId = $_POST['report_id'] ?? '';

if (!$entryId || !$reportId) {
    redirect('/admin/progressive-reports');
}

// Initialize handler
$progressiveReportEntry = new ProgressiveReportEntry();

// Delete the entry
$result = $progressiveReportEntry->delete($entryId);

if ($result) {
    // Redirect back to the report with success message
    header('Location: ' . getBasePath() . '/admin/progressive-reports/view.php?id=' . $reportId . '&deleted=1');
} else {
    // Redirect back to the report with error message
    header('Location: ' . getBasePath() . '/admin/progressive-reports/view.php?id=' . $reportId . '&error=delete_failed');
}
exit;
?>
