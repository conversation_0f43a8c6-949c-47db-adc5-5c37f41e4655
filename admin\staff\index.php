<?php
/**
 * Admin Staff Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createStaffMember($_POST);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff member created successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;

        case 'update':
            $result = updateStaffMember($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff member updated successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;

        case 'toggle_status':
            $result = toggleStaffStatus($_POST['id']);
            if ($result['success']) {
                $_SESSION['staff_success'] = 'Staff status updated successfully!';
            } else {
                $_SESSION['staff_error'] = $result['error'];
            }
            break;

        case 'change_staff_email':
            try {
                require_once __DIR__ . '/../../includes/admin_profile_functions.php';
                updateStaffEmail($_POST['staff_id'], $_POST['new_email'], $_SESSION['user_id']);
                $_SESSION['staff_success'] = 'Staff email updated successfully!';
            } catch (Exception $e) {
                $_SESSION['staff_error'] = $e->getMessage();
            }
            break;

    }
    
    redirect('/admin/staff');
}

// Get services for specialties
$services = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 ORDER BY name ASC");

// Get staff members with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');

$whereClause = "WHERE u.role = 'STAFF'";
$params = [];

if ($search) {
    $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== '') {
    $whereClause .= " AND u.is_active = ?";
    $params[] = (int)$status;
}

$staff = $database->fetchAll(
    "SELECT u.id, u.name, u.email, u.email_verified_at, u.image, u.password, u.phone,
            u.date_of_birth, u.role, u.points, u.referral_code, u.referred_by,
            u.is_active, u.created_at, u.updated_at,
            ss.role as staff_role,
            ss.hourly_rate,
            COALESCE(booking_stats.total_bookings, 0) as total_bookings,
            COALESCE(booking_stats.completed_bookings, 0) as completed_bookings,
            COALESCE(booking_stats.total_revenue, 0) as total_revenue
     FROM users u
     LEFT JOIN (
         SELECT user_id, role, hourly_rate
         FROM staff_schedules
         GROUP BY user_id, role, hourly_rate
     ) ss ON u.id = ss.user_id
     LEFT JOIN (
         SELECT staff_id,
                COUNT(*) as total_bookings,
                COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
                SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END) as total_revenue
         FROM bookings
         WHERE staff_id IS NOT NULL
         GROUP BY staff_id
     ) booking_stats ON u.id = booking_stats.staff_id
     $whereClause
     ORDER BY u.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalStaff = $database->fetch(
    "SELECT COUNT(*) as count FROM users u $whereClause",
    $params
)['count'];

$totalPages = ceil($totalStaff / $limit);

// Get staff statistics
$stats = getStaffStats();

// Handle messages - only for staff page
$message = '';
$messageType = '';
if (isset($_SESSION['staff_success'])) {
    $message = $_SESSION['staff_success'];
    $messageType = 'success';
    unset($_SESSION['staff_success']);
} elseif (isset($_SESSION['staff_error'])) {
    $message = $_SESSION['staff_error'];
    $messageType = 'error';
    unset($_SESSION['staff_error']);
}

$pageTitle = "Staff Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Staff Management CSS -->
<style>
/* Medical Staff Management Specific Styles */
.medical-staff-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-staff-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-staff-card:hover::before {
    left: 100%;
}

.medical-staff-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-staff-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-staff-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-staff-item:hover::before {
    transform: scaleX(1);
}

.medical-staff-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-staff-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.staff-type-senior {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.staff-type-specialist {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.staff-type-therapist {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.staff-type-manager {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.staff-type-trainee {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.medical-status-active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-status-inactive {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-staff-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-staff-grid {
        grid-template-columns: 1fr;
    }

    .medical-staff-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-staff-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Medical Staff
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive staff management, scheduling, and performance tracking</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= count($staff) ?> Active Specialists
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Advanced Scheduling
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex gap-3">
                                <button onclick="openCreateModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    + Add Staff Member
                                </button>
                                <button onclick="testModal()" class="medical-btn-secondary text-sm px-4 py-2">
                                    Test Modal
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Staff</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Active Staff</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['active']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Monthly Revenue</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= formatCurrency($stats['avg_monthly_revenue']) ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-purple-100 to-purple-200">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Bookings</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total_bookings']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Staff</h2>
                        <form method="GET" class="flex flex-col sm:flex-row gap-6">
                            <div class="flex-1">
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                       placeholder="Search staff by name, email, or phone..."
                                       class="medical-form-input w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input">
                                    <option value="">All Status</option>
                                    <option value="1" <?= $status === '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= $status === '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="flex items-end gap-3">
                                <button type="submit" class="medical-btn-primary">
                                    Apply Filters
                                </button>
                                <?php if ($search || $status !== ''): ?>
                                    <a href="<?= getBasePath() ?>/admin/staff" class="medical-btn-secondary">
                                        Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>

                    <!-- Medical Staff Grid -->
                    <div class="medical-staff-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                        <?php foreach ($staff as $member): ?>
                            <div class="medical-staff-item">
                                <!-- Staff Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center flex-1">
                                        <div class="medical-staff-avatar mr-4">
                                            <?= strtoupper(substr($member['name'], 0, 2)) ?>
                                        </div>
                                        <div class="flex-1">
                                            <h3 class="text-lg font-bold text-redolence-navy mb-1">
                                                <?= htmlspecialchars($member['name']) ?>
                                            </h3>
                                            <p class="text-gray-600 text-sm"><?= htmlspecialchars($member['email']) ?></p>
                                            <?php if ($member['staff_role']): ?>
                                                <span class="medical-staff-type-badge staff-type-<?= strtolower(str_replace(' ', '', $member['staff_role'])) ?> mt-1">
                                                    <?= htmlspecialchars($member['staff_role']) ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <span class="medical-staff-type-badge <?= $member['is_active'] ? 'medical-status-active' : 'medical-status-inactive' ?> ml-4">
                                        <?= $member['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>

                                <!-- Contact Information -->
                                <?php if ($member['phone']): ?>
                                    <div class="mb-4">
                                        <p class="text-sm text-redolence-navy flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <?= htmlspecialchars($member['phone']) ?>
                                        </p>
                                    </div>
                                <?php endif; ?>

                                <!-- Performance Stats -->
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                                        <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Completed</p>
                                        <p class="text-xl font-bold text-redolence-navy"><?= number_format($member['completed_bookings']) ?></p>
                                    </div>
                                    <div class="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                                        <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Revenue</p>
                                        <p class="text-xl font-bold text-redolence-green">TSH <?= number_format($member['total_revenue'], 0) ?></p>
                                    </div>
                                </div>

                                <!-- Join Date -->
                                <div class="text-center text-sm text-gray-600 mb-4">
                                    Joined <?= date('M j, Y', strtotime($member['created_at'])) ?>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2 pt-4 border-t border-gray-200">
                                    <button onclick="editStaff('<?= $member['id'] ?>')"
                                            class="flex-1 medical-btn-primary text-sm px-3 py-2">
                                        Edit
                                    </button>
                                    <a href="<?= getBasePath() ?>/admin/staff/schedule.php?id=<?= $member['id'] ?>"
                                       class="flex-1 medical-btn-secondary text-sm px-3 py-2 text-center">
                                        Schedule
                                    </a>
                                </div>
                                <div class="flex gap-2 mt-2">
                                    <button onclick="changeStaffEmail('<?= $member['id'] ?>', '<?= htmlspecialchars($member['name']) ?>', '<?= htmlspecialchars($member['email']) ?>')"
                                            class="flex-1 medical-btn-secondary text-sm px-3 py-2">
                                        Change Email
                                    </button>
                                    <button onclick="toggleStatus('<?= $member['id'] ?>', <?= $member['is_active'] ? 'false' : 'true' ?>)"
                                            class="flex-1 <?= $member['is_active'] ? 'medical-btn-danger' : 'medical-btn-primary' ?> text-sm px-3 py-2">
                                        <?= $member['is_active'] ? 'Deactivate' : 'Activate' ?>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-staff-card p-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing <span class="font-semibold text-redolence-navy"><?= $offset + 1 ?></span> to
                                    <span class="font-semibold text-redolence-navy"><?= min($offset + $limit, $totalStaff) ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalStaff ?></span> results
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $page ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Create/Edit Staff Modal -->
<div id="staffModal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center backdrop-blur-sm" style="z-index: 9999;">
    <div class="bg-white w-full max-w-3xl mx-4 max-h-screen overflow-y-auto p-8 rounded-2xl shadow-2xl border-2 border-gray-100" id="staffModalContent">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-2xl font-bold text-redolence-navy">Add Staff Member</h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="staffForm" method="POST">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="staffId">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Full Name *</label>
                    <input type="text" name="name" id="staffName" required 
                           class="medical-form-input w-full">
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Email *</label>
                    <input type="email" name="email" id="staffEmail" required 
                           class="medical-form-input w-full">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Phone</label>
                    <input type="tel" name="phone" id="staffPhone"
                           class="medical-form-input w-full">
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Staff Role</label>
                    <select name="staff_role" id="staffRole"
                            class="medical-form-input w-full">
                        <option value="Staff Member">Staff Member</option>
                        <option value="Senior Stylist">Senior Stylist</option>
                        <option value="Hair Stylist">Hair Stylist</option>
                        <option value="Massage Therapist">Massage Therapist</option>
                        <option value="Esthetician">Esthetician</option>
                        <option value="Nail Technician">Nail Technician</option>
                        <option value="Manager">Manager</option>
                        <option value="Assistant Manager">Assistant Manager</option>
                        <option value="Specialist">Specialist</option>
                        <option value="Trainee">Trainee</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Password *</label>
                    <input type="password" name="password" id="staffPassword"
                           class="medical-form-input w-full">
                    <p class="text-xs text-gray-400 mt-1">Leave blank to keep current password (when editing)</p>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Salary (TSH)</label>
                    <input type="number" name="hourly_rate" id="staffHourlyRate" step="1000" min="0" placeholder="e.g. 500000"
                           class="medical-form-input w-full">
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Services Specialties</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    <?php foreach ($services as $service): ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="specialties[]" value="<?= $service['id'] ?>"
                                   class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                            <span class="ml-2 text-sm text-gray-700"><?= htmlspecialchars($service['name']) ?></span>
                        </label>
                    <?php endforeach; ?>
                </div>
                <p class="text-xs text-gray-400 mt-1">Select the services this staff member can perform</p>
            </div>
            
            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="staffActive" value="1" checked 
                           class="rounded border-gray-300 text-redolence-green focus:ring-redolence-green">
                    <span class="ml-2 text-sm text-gray-700">Staff member is active</span>
                </label>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Save Staff Member
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Staff Email Change Modal -->
<div id="emailChangeModal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[70] backdrop-blur-sm">
    <div class="bg-white w-full max-w-md mx-4 p-8 rounded-2xl shadow-2xl border-2 border-gray-100">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-redolence-navy">Change Staff Email</h3>
            <button onclick="closeEmailChangeModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="change_staff_email">
            <input type="hidden" name="staff_id" id="emailChangeStaffId">

            <div>
                <label class="block text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Staff Member</label>
                <input type="text" id="emailChangeStaffName" disabled
                       class="medical-form-input w-full bg-gray-100">
            </div>

            <div>
                <label class="block text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Current Email</label>
                <input type="email" id="emailChangeCurrentEmail" disabled
                       class="medical-form-input w-full bg-gray-100">
            </div>

            <div>
                <label class="block text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">New Email *</label>
                <input type="email" name="new_email" required
                       class="medical-form-input w-full">
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeEmailChangeModal()"
                        class="medical-btn-secondary">
                    Cancel
                </button>
                <button type="submit"
                        class="medical-btn-primary">
                    Change Email
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Test function to debug modal
function testModal() {
    console.log('Testing modal...');
    const modal = document.getElementById('staffModal');
    console.log('Modal element:', modal);
    console.log('Modal classes:', modal ? modal.className : 'Modal not found');

    if (modal) {
        if (modal.classList.contains('hidden')) {
            console.log('Modal is hidden, showing it...');
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
            console.log('Modal classes after showing:', modal.className);
            console.log('Modal style display:', modal.style.display);
        } else {
            console.log('Modal is visible, hiding it...');
            modal.classList.add('hidden');
            modal.style.display = 'none';
            console.log('Modal classes after hiding:', modal.className);
            console.log('Modal style display:', modal.style.display);
        }
    }
}

// Modal functions
function openCreateModal() {
    console.log('Opening create modal...');

    // Check if modal exists
    const modal = document.getElementById('staffModal');
    if (!modal) {
        console.error('Staff modal not found!');
        alert('Modal not found. Please refresh the page.');
        return;
    }

    document.getElementById('modalTitle').textContent = 'Add Staff Member';
    document.getElementById('formAction').value = 'create';
    document.getElementById('staffForm').reset();
    document.getElementById('staffActive').checked = true;
    document.getElementById('staffPassword').required = true;

    // Reset staff role to default
    const staffRoleSelect = document.getElementById('staffRole');
    if (staffRoleSelect) {
        staffRoleSelect.value = 'Staff Member';
    }

    // Reset salary to default
    const staffHourlyRate = document.getElementById('staffHourlyRate');
    if (staffHourlyRate) {
        staffHourlyRate.value = '500000';
    }

    console.log('Showing create modal...');
    modal.classList.remove('hidden');
    modal.style.display = 'flex';
    console.log('Create modal should now be visible');
}

function closeModal() {
    console.log('Closing modal...');
    const modal = document.getElementById('staffModal');
    if (modal) {
        modal.classList.add('hidden');
        modal.style.display = 'none';
        console.log('Modal closed');
    } else {
        console.error('Modal not found when trying to close');
    }
}

function editStaff(staffId) {
    console.log('🔧 Edit staff called with ID:', staffId);

    // Check if modal exists
    const modal = document.getElementById('staffModal');
    if (!modal) {
        console.error('❌ Staff modal not found!');
        alert('Modal not found. Please refresh the page.');
        return;
    }

    console.log('✅ Modal found:', modal);

    // Show loading state
    const loadingModal = document.createElement('div');
    loadingModal.id = 'loadingModal';
    loadingModal.innerHTML = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70]">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-redolence-green mr-3"></div>
                    <span class="text-redolence-navy">Loading staff details...</span>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(loadingModal);

    // Fetch staff data
    const apiUrl = `<?= getBasePath() ?>/api/admin/staff.php?id=${staffId}`;
    console.log('🌐 Fetching staff data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.text(); // Get as text first to see raw response
        })
        .then(text => {
            console.log('📄 Raw response:', text);

            try {
                const data = JSON.parse(text);
                console.log('📊 Parsed staff data:', data);

                return data;
            } catch (parseError) {
                console.error('❌ JSON parse error:', parseError);
                console.error('❌ Raw response that failed to parse:', text);
                throw new Error('Invalid JSON response from server');
            }
        })
        .then(data => {

                // Remove loading modal
                const loadingEl = document.getElementById('loadingModal');
                if (loadingEl) {
                    document.body.removeChild(loadingEl);
                    console.log('🗑️ Loading modal removed');
                }

                if (data.success) {
                    console.log('✅ API call successful');
                    const staff = data.data;
                    console.log('👤 Staff data:', staff);

                    // Set modal title and form action
                    console.log('🏷️ Setting modal title and form action');
                    document.getElementById('modalTitle').textContent = 'Edit Staff Member';
                    document.getElementById('formAction').value = 'update';
                    document.getElementById('staffId').value = staffId;

                // Populate form fields with error handling
                console.log('📝 Populating form fields...');

                const setFieldValue = (fieldId, value) => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = value;
                        } else {
                            field.value = value || '';
                        }
                        console.log(`✅ Set ${fieldId}:`, value);
                    } else {
                        console.warn(`⚠️ Field not found: ${fieldId}`);
                    }
                };

                setFieldValue('staffName', staff.name);
                setFieldValue('staffEmail', staff.email);
                setFieldValue('staffPhone', staff.phone);
                setFieldValue('staffRole', staff.staff_role || 'Staff Member');
                setFieldValue('staffHourlyRate', staff.hourly_rate || 500000);
                setFieldValue('staffActive', staff.is_active);

                // Handle specialties - uncheck all first, then check the ones for this staff
                console.log('🎯 Handling specialties...');
                const specialtyCheckboxes = document.querySelectorAll('input[name="specialties[]"]');
                console.log(`📋 Found ${specialtyCheckboxes.length} specialty checkboxes`);

                specialtyCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                if (staff.specialties && staff.specialties.length > 0) {
                    console.log('🎯 Staff specialties:', staff.specialties);
                    staff.specialties.forEach(specialtyId => {
                        const checkbox = document.querySelector(`input[name="specialties[]"][value="${specialtyId}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                            console.log(`✅ Checked specialty: ${specialtyId}`);
                        } else {
                            console.warn(`⚠️ Specialty checkbox not found: ${specialtyId}`);
                        }
                    });
                } else {
                    console.log('ℹ️ No specialties to set');
                }

                // Password field is not required for editing
                console.log('🔐 Configuring password field...');
                const passwordField = document.getElementById('staffPassword');
                if (passwordField) {
                    passwordField.required = false;
                    passwordField.placeholder = 'Leave blank to keep current password';
                    passwordField.value = ''; // Clear any existing value
                    console.log('✅ Password field configured');
                } else {
                    console.warn('⚠️ Password field not found');
                }

                // Show modal
                console.log('🎭 Showing modal...');
                const modal = document.getElementById('staffModal');
                console.log('🎭 Modal element before show:', modal);
                console.log('🎭 Modal classes before:', modal.className);

                modal.classList.remove('hidden');
                modal.style.display = 'flex';

                console.log('🎭 Modal classes after:', modal.className);
                console.log('🎭 Modal style display:', modal.style.display);
                console.log('✅ Modal should now be visible!');
            } else {
                console.error('API returned error:', data.error);
                alert('Error loading staff details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            // Remove loading modal
            const loadingEl = document.getElementById('loadingModal');
            if (loadingEl) {
                document.body.removeChild(loadingEl);
            }
            console.error('Error fetching staff data:', error);
            alert('Error loading staff details: ' + error.message + '. Please try again.');
        });
}

function toggleStatus(staffId, newStatus) {
    const action = newStatus ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this staff member?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="id" value="${staffId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Email change functions
function changeStaffEmail(staffId, staffName, currentEmail) {
    document.getElementById('emailChangeStaffId').value = staffId;
    document.getElementById('emailChangeStaffName').value = staffName;
    document.getElementById('emailChangeCurrentEmail').value = currentEmail;
    document.getElementById('emailChangeModal').classList.remove('hidden');
}

function closeEmailChangeModal() {
    document.getElementById('emailChangeModal').classList.add('hidden');
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        closeEmailChangeModal();
    }
});

// Close modal on backdrop click
document.getElementById('staffModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

document.getElementById('emailChangeModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEmailChangeModal();
    }
});

// Form submission
document.getElementById('staffForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const action = formData.get('action');

    // Show loading state
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Saving...';
    submitButton.disabled = true;

    fetch('<?= getBasePath() ?>/admin/staff/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Reload the page to see changes
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the staff member.');

        // Restore button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    });
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>