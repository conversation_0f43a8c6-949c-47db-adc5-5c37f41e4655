@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');
@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: 'Poppins', 'Montserrat', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: 'Playfair Display', Georgia, serif;
    --font-body: 'Montserrat', ui-sans-serif, system-ui, sans-serif;

    --color-zinc-50: #fafafa;
    --color-zinc-100: #f5f5f5;
    --color-zinc-200: #e5e5e5;
    --color-zinc-300: #d4d4d4;
    --color-zinc-400: #a3a3a3;
    --color-zinc-500: #737373;
    --color-zinc-600: #525252;
    --color-zinc-700: #404040;
    --color-zinc-800: #262626;
    --color-zinc-900: #171717;
    --color-zinc-950: #0a0a0a;

    /* Luxury Feminine Color Palette */
    --color-pink-salon-light: #E98CA5;
    --color-pink-salon: #F4B6C5;
    --color-rose-salon: #C85E78;
    --color-charcoal-salon: #4A4A52;
    --color-gold-salon: #DCC7A1;
    --color-blush-salon: #F7E9E6;
    --color-gray-salon: #EFEFEF;
    --color-mauve-salon: #8B5D66;
    --color-obsidian-salon: #2C2C34;

    /* Legacy aliases for compatibility */
    --color-rose-light: #F4B6C5;
    --color-rose: #E98CA5;
    --color-rose-deep: #C85E78;
    --color-mauve: #8B5D66;
    --color-gold: #DCC7A1;
    --color-blush: #F7E9E6;
    --color-gray-warm: #EFEFEF;
    --color-charcoal: #4A4A52;
    --color-obsidian: #2C2C34;

    /* Border Radius */
    --radius-salon-sm: 8px;
    --radius-salon: 12px;
    --radius-salon-md: 16px;
    --radius-salon-lg: 24px;

    /* Custom Shadows */
    --shadow-salon-sm: 0 2px 8px rgba(200, 94, 120, 0.08);
    --shadow-salon: 0 4px 16px rgba(200, 94, 120, 0.12);
    --shadow-salon-lg: 0 8px 24px rgba(200, 94, 120, 0.16);

    --color-accent: var(--color-neutral-800);
    --color-accent-content: var(--color-neutral-800);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-white);
        --color-accent-content: var(--color-white);
        --color-accent-foreground: var(--color-neutral-800);
    }
}

@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }

    /* Alpine.js x-cloak */
    [x-cloak] {
        display: none !important;
    }

    /* Smooth Scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Custom Animations */
    @keyframes shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse-soft {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.05);
        }
    }

    /* Loading State */
    .loading-shimmer {
        background: linear-gradient(
            90deg,
            #F7E9E6 0%,
            #E98CA5 50%,
            #F7E9E6 100%
        );
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
    }

    /* Fade In Animation */
    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Pulse Animation */
    .pulse-soft {
        animation: pulse-soft 2s ease-in-out infinite;
    }
}

[data-flux-field]:not(ui-radio, ui-checkbox) {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-[#C85E78] ring-offset-2 ring-offset-white;
}

/* Custom Utility Classes */
.shadow-salon-sm {
    box-shadow: 0 2px 8px rgba(200, 94, 120, 0.08);
}

.shadow-salon {
    box-shadow: 0 4px 16px rgba(200, 94, 120, 0.12);
}

.shadow-salon-lg {
    box-shadow: 0 8px 24px rgba(200, 94, 120, 0.16);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #EFEFEF;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #DCC7A1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #C85E78;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */