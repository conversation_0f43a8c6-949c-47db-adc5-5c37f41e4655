[2025-12-10 09:15:39] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/143.0.0.0 Safari/537.36","timestamp":"2025-12-10T09:15:38.177Z"} 
[2025-12-10 09:15:43] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-10T09:15:38.178Z"} 
[2025-12-10 10:26:24] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/143.0.0.0 Safari/537.36","timestamp":"2025-12-10T10:26:24.481Z"} 
[2025-12-10 10:26:25] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/143.0.0.0 Safari/537.36","timestamp":"2025-12-10T10:26:24.486Z"} 
[2025-12-10 10:26:25] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-10T10:26:24.486Z"} 
[2025-12-10 15:47:08] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://instyle.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/143.0.0.0 Safari/537.36","timestamp":"2025-12-10T15:47:07.602Z"} 
[2025-12-10 15:53:18] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://instyle.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/143.0.0.0 Safari/537.36","timestamp":"2025-12-10T15:53:18.212Z"} 
[2025-12-11 10:18:18] local.WARNING: Alpine Expression Error: can't access property "on", window.Livewire.find(...) is undefined

Expression: "window.Livewire.find('yvS3SyYByBE8rYaK66Lv').on('profile-updated', () => { clearTimeout(timeout); shown = true; timeout = setTimeout(() => { shown = false }, 2000); })" null null null null 3048 null null null null null false null  [Circular] [Circular] center 0s opacity, transform 1.5s cubic-bezier(0.4, 0.0, 0.2, 1) 1 scale(1) 0 scale(1) false  {"url":"https://instyle.test/manager/settings/profile","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-11T10:18:17.769Z"} 
[2025-12-11 10:18:18] local.ERROR: TypeError: can't access property "on", window.Livewire.find(...) is undefined https://instyle.test/manager/appointments line 7859 > injectedScript line 5 > AsyncFunction 3 48 TypeError can't access property "on", window.Livewire.find(...) is undefined anonymous@https://instyle.test/manager/appointments line 7859 > injectedScript line 5 > AsyncFunction:3:48
hu/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:1075
uu@https://instyle.test/manager/appointments line 7859 > injectedScript:1:4981
ct@https://instyle.test/manager/appointments line 7859 > injectedScript:5:145
@https://instyle.test/manager/appointments line 7859 > injectedScript:5:32884
Ve/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:11686
n@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2335
a@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2365
bu@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2375
Pe@https://instyle.test/manager/appointments line 7859 > injectedScript:5:5172
wu/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:4426
Qr/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:2735
Qr@https://instyle.test/manager/appointments line 7859 > injectedScript:1:2723
au/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:1708
au/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:1796 {"url":"https://instyle.test/manager/settings/profile","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-11T10:18:17.771Z"} 
[2025-12-11 10:18:18] local.ERROR: TypeError: can't access property "on", window.Livewire.find(...) is undefined https://instyle.test/manager/appointments line 7859 > injectedScript line 5 > AsyncFunction 3 48 TypeError can't access property "on", window.Livewire.find(...) is undefined anonymous@https://instyle.test/manager/appointments line 7859 > injectedScript line 5 > AsyncFunction:3:48
hu/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:1075
uu@https://instyle.test/manager/appointments line 7859 > injectedScript:1:4981
ct@https://instyle.test/manager/appointments line 7859 > injectedScript:5:145
@https://instyle.test/manager/appointments line 7859 > injectedScript:5:32884
Ve/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:11686
n@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2335
a@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2365
bu@https://instyle.test/manager/appointments line 7859 > injectedScript:5:2375
Pe@https://instyle.test/manager/appointments line 7859 > injectedScript:5:5172
wu/<@https://instyle.test/manager/appointments line 7859 > injectedScript:5:4426
Qr/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:2735
Qr@https://instyle.test/manager/appointments line 7859 > injectedScript:1:2723
au/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:1708
au/<@https://instyle.test/manager/appointments line 7859 > injectedScript:1:1796 {"url":"https://instyle.test/manager/settings/profile","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-11T10:18:17.771Z"} 
[2025-12-12 07:50:18] local.ERROR: Unhandled Promise Rejection TypeError NetworkError when attempting to fetch resource. null {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T07:50:18.117Z"} 
[2025-12-12 08:04:50] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:50.155Z"} 
[2025-12-12 08:04:50] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:50.155Z"} 
[2025-12-12 08:04:51] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:50.670Z"} 
[2025-12-12 08:04:51] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:50.670Z"} 
[2025-12-12 08:04:51] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:51.046Z"} 
[2025-12-12 08:04:51] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:51.046Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.136Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.136Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.261Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.261Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.387Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.387Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.512Z"} 
[2025-12-12 08:04:52] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.512Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.637Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.637Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.790Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.790Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.951Z"} 
[2025-12-12 08:04:53] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:52.951Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:56.850Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:56.850Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:56.974Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:56.974Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.086Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.086Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.239Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.239Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.376Z"} 
[2025-12-12 08:04:57] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.376Z"} 
[2025-12-12 08:04:58] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.527Z"} 
[2025-12-12 08:04:58] local.ERROR: ReferenceError: togglePassword is not defined https://instyle.test/manager/clients 1 1 ReferenceError togglePassword is not defined onclick@https://instyle.test/manager/clients:1:1 {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:04:57.527Z"} 
[2025-12-12 08:15:25] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/manager/settings/two-factor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:15:25.334Z"} 
[2025-12-12 08:17:30] local.INFO: [vite] Direct websocket connection fallback. Check out https://vite.dev/config/server-options.html#server-hmr to remove the previous connection error. {"url":"https://instyle.test/manager/clients","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T08:17:30.078Z"} 
[2025-12-12 09:01:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/manager/appointments?search=JAM&status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36","timestamp":"2025-12-12T09:01:34.231Z"} 
[2025-12-12 09:01:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T09:01:34.232Z"} 
[2025-12-12 09:01:35] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T09:01:34.192Z"} 
[2025-12-12 21:28:59] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?search=&status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:28:58.914Z"} 
[2025-12-12 21:29:08] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?search=&status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:29:07.974Z"} 
[2025-12-12 21:29:44] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?search=&status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:29:44.451Z"} 
[2025-12-12 21:32:02] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?search=&status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:32:01.953Z"} 
[2025-12-12 21:32:44] local.ERROR: Unhandled Promise Rejection InvalidStateError HTMLDialogElement.showModal: Cannot call showModal() on an open non-modal dialog. showHtmlModal@https://instyle.test/livewire/livewire.js?id=646f9d24:4031:11
showFailureModal@https://instyle.test/livewire/livewire.js?id=646f9d24:4372:18
sendRequest@https://instyle.test/livewire/livewire.js?id=646f9d24:4349:14 {"url":"https://instyle.test/manager/appointments?status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:32:44.530Z"} 
[2025-12-12 21:33:18] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?status=all&date=","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:33:18.494Z"} 
[2025-12-12 21:38:46] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:38:46.380Z"} 
[2025-12-12 21:41:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:41:24.090Z"} 
[2025-12-12 21:42:42] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:42:41.959Z"} 
[2025-12-12 21:42:42] local.WARNING: Livewire: [wire:model="appointment_id"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-12T21:42:41.964Z"} 
[2025-12-13 10:23:37] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:23:36.251Z"} 
[2025-12-13 10:24:50] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:50.567Z"} 
[2025-12-13 10:24:50] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:50.567Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:51.981Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:51.981Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.131Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.131Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.269Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.269Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.345Z"} 
[2025-12-13 10:24:52] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.345Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.796Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:52.796Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.097Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.097Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.272Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.272Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.448Z"} 
[2025-12-13 10:24:53] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.448Z"} 
[2025-12-13 10:24:54] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.924Z"} 
[2025-12-13 10:24:54] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:53.924Z"} 
[2025-12-13 10:24:54] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:54.226Z"} 
[2025-12-13 10:24:54] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:54.226Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:55.867Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:55.867Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:55.991Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:55.991Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.117Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.118Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.242Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.242Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.394Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.394Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.518Z"} 
[2025-12-13 10:24:56] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:56.518Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:57.774Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:57.774Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:58.263Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:58.263Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:58.563Z"} 
[2025-12-13 10:24:58] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:24:58.563Z"} 
[2025-12-13 10:25:04] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:04.469Z"} 
[2025-12-13 10:25:08] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:08.516Z"} 
[2025-12-13 10:25:08] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:08.516Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:08.727Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:08.727Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.016Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showEditModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showEditModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.016Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.366Z"} 
[2025-12-13 10:25:09] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.366Z"} 
[2025-12-13 10:25:10] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.680Z"} 
[2025-12-13 10:25:10] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.680Z"} 
[2025-12-13 10:25:10] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.842Z"} 
[2025-12-13 10:25:10] local.ERROR: ReferenceError: showViewModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showViewModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:09.842Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.072Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.072Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.234Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.234Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.434Z"} 
[2025-12-13 10:25:11] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/appointments 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/appointments:1:1 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:25:11.434Z"} 
[2025-12-13 10:27:10] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:27:09.367Z"} 
[2025-12-13 10:27:28] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:27:27.516Z"} 
[2025-12-13 10:27:42] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:27:41.289Z"} 
[2025-12-13 10:27:55] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:27:54.475Z"} 
[2025-12-13 10:47:28] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:47:28.326Z"} 
[2025-12-13 10:49:03] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:49:03.558Z"} 
[2025-12-13 10:49:30] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:49:30.414Z"} 
[2025-12-13 10:50:14] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:50:14.609Z"} 
[2025-12-13 10:50:38] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:50:37.942Z"} 
[2025-12-13 10:51:07] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:51:06.925Z"} 
[2025-12-13 10:55:15] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:55:14.186Z"} 
[2025-12-13 10:55:53] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:55:52.008Z"} 
[2025-12-13 10:56:07] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:56:06.297Z"} 
[2025-12-13 10:56:29] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:56:28.713Z"} 
[2025-12-13 10:56:34] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T10:56:32.856Z"} 
[2025-12-13 11:03:54] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:03:53.859Z"} 
[2025-12-13 11:04:35] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:04:34.974Z"} 
[2025-12-13 11:05:44] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:05:44.065Z"} 
[2025-12-13 11:06:06] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:06:06.573Z"} 
[2025-12-13 11:06:22] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:06:22.104Z"} 
[2025-12-13 11:07:07] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:07:07.108Z"} 
[2025-12-13 11:09:07] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:09:06.731Z"} 
[2025-12-13 11:09:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:09:24.077Z"} 
[2025-12-13 11:09:45] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:09:44.964Z"} 
[2025-12-13 11:10:00] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:09:59.784Z"} 
[2025-12-13 11:10:46] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:10:46.552Z"} 
[2025-12-13 11:10:51] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:10:50.889Z"} 
[2025-12-13 11:10:55] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:10:55.139Z"} 
[2025-12-13 11:10:59] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:10:59.472Z"} 
[2025-12-13 11:11:04] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:11:03.854Z"} 
[2025-12-13 11:11:08] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:11:07.935Z"} 
[2025-12-13 11:11:12] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:11:12.133Z"} 
[2025-12-13 11:11:16] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:11:16.361Z"} 
[2025-12-13 11:11:50] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:11:50.209Z"} 
[2025-12-13 11:15:14] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:15:14.492Z"} 
[2025-12-13 11:15:27] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:15:27.402Z"} 
[2025-12-13 11:15:41] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:15:41.249Z"} 
[2025-12-13 11:16:05] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:16:05.171Z"} 
[2025-12-13 11:16:40] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:16:40.405Z"} 
[2025-12-13 11:16:55] local.ERROR: Unhandled Promise Rejection InvalidStateError HTMLDialogElement.showModal: Cannot call showModal() on an open non-modal dialog. showHtmlModal@https://instyle.test/livewire/livewire.js?id=646f9d24:4031:11
showFailureModal@https://instyle.test/livewire/livewire.js?id=646f9d24:4372:18
sendRequest@https://instyle.test/livewire/livewire.js?id=646f9d24:4349:14 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:16:55.363Z"} 
[2025-12-13 11:17:22] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:17:21.710Z"} 
[2025-12-13 11:18:06] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:18:05.692Z"} 
[2025-12-13 11:18:58] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:18:57.633Z"} 
[2025-12-13 11:19:33] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:19:33.250Z"} 
[2025-12-13 11:20:31] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:20:31.449Z"} 
[2025-12-13 11:23:33] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:23:32.763Z"} 
[2025-12-13 11:24:35] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:24:34.679Z"} 
[2025-12-13 11:26:00] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:25:59.703Z"} 
[2025-12-13 11:26:29] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:26:28.977Z"} 
[2025-12-13 11:26:42] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:26:42.218Z"} 
[2025-12-13 11:29:02] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:29:01.584Z"} 
[2025-12-13 11:29:50] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:29:49.790Z"} 
[2025-12-13 11:29:55] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:29:54.941Z"} 
[2025-12-13 11:29:55] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:29:54.941Z"} 
[2025-12-13 11:29:55] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:29:54.956Z"} 
[2025-12-13 11:32:57] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:32:56.512Z"} 
[2025-12-13 11:34:37] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:34:36.743Z"} 
[2025-12-13 11:35:04] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:35:03.622Z"} 
[2025-12-13 11:37:32] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:37:32.319Z"} 
[2025-12-13 11:38:40] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:38:40.322Z"} 
[2025-12-13 11:39:21] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:39:20.877Z"} 
[2025-12-13 11:40:03] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:40:02.671Z"} 
[2025-12-13 11:40:22] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:40:21.682Z"} 
[2025-12-13 11:41:00] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:40:59.763Z"} 
[2025-12-13 11:42:02] local.WARNING: Livewire: missing closing tags found. Ensure your template elements contain matching closing tags. [Circular] 4dmhZZXnprxEqc9k5W7S 4dmhZZXnprxEqc9k5W7S {"data":{"search":"","departmentFilter":"all","statusFilter":"all","employmentTypeFilter":"all","sortBy":"name","sortDirection":"asc","selectedStaff":[[],{"s":"arr"}],"selectAll":false,"staff_id":null,"name":null,"email":null,"password":null,"phone":null,"position":null,"department":null,"bio":null,"avatar":null,"hire_date":null,"hourly_rate":null,"commission_rate":0,"employment_type":"full-time","skills":[[],{"s":"arr"}],"certifications":[[],{"s":"arr"}],"experience_years":0,"working_hours":[[],{"s":"arr"}],"available_for_booking":true,"emergency_contact_name":null,"emergency_contact_phone":null,"address":null,"status":"active","notes":null,"temp_avatar":null,"paginators":[{"page":1},{"s":"arr"}]},"memo":{"id":"4dmhZZXnprxEqc9k5W7S","name":"manager.team","path":"manager\/team","method":"GET","release":"a-a-a","children":[],"scripts":[],"assets":[],"errors":[],"locale":"en"},"checksum":"15687ea4c9d101ea930b3c45a48588036e9e029d23e1be8491a74f2b8426374e"} null all all all name asc  arr false null null null null null null null null null null null 0 full-time  arr  arr 0  arr true null null null active null null 1 arr 4dmhZZXnprxEqc9k5W7S manager.team manager/team GET a-a-a     en 15687ea4c9d101ea930b3c45a48588036e9e029d23e1be8491a74f2b8426374e manager.team search push false null departmentFilter push false all statusFilter push false all page push false null search push false null departmentFilter push false all statusFilter push false all page push false null null all all all name asc  false null null null null null null null null null null null 0 full-time   0  true null null null active null null 1 null all all all name asc  false null null null null null null null null null null null 0 full-time   0  true null null null active null null 1 null all all all name asc  false null null null null null null null null null null null 0 full-time   0  true null null null active null null 1    null null null null null 103 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:42:01.640Z"} 
[2025-12-13 11:44:50] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:49.780Z"} 
[2025-12-13 11:44:50] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:49.780Z"} 
[2025-12-13 11:44:51] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:51.157Z"} 
[2025-12-13 11:44:51] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:51.157Z"} 
[2025-12-13 11:44:52] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.539Z"} 
[2025-12-13 11:44:52] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.539Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.748Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.748Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.912Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:52.912Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.088Z"} 
[2025-12-13 11:44:53] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.088Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.439Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.439Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.602Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.602Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.753Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.753Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.941Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:53.941Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:54.104Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:54.104Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:54.315Z"} 
[2025-12-13 11:44:54] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:44:54.315Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:26.812Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:26.812Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:26.987Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:26.987Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:27.314Z"} 
[2025-12-13 11:45:27] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:27.314Z"} 
[2025-12-13 11:45:28] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:28.266Z"} 
[2025-12-13 11:45:28] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:28.266Z"} 
[2025-12-13 11:45:28] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:28.567Z"} 
[2025-12-13 11:45:28] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:28.567Z"} 
[2025-12-13 11:45:30] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:29.784Z"} 
[2025-12-13 11:45:30] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:29.784Z"} 
[2025-12-13 11:45:30] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:30.535Z"} 
[2025-12-13 11:45:30] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:30.535Z"} 
[2025-12-13 11:45:31] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:30.698Z"} 
[2025-12-13 11:45:31] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:30.698Z"} 
[2025-12-13 11:45:32] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.064Z"} 
[2025-12-13 11:45:32] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.064Z"} 
[2025-12-13 11:45:32] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.365Z"} 
[2025-12-13 11:45:32] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.365Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.752Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.752Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.953Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:32.953Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:33.304Z"} 
[2025-12-13 11:45:33] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:33.304Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:33.744Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:33.744Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:34.146Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:34.146Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:34.469Z"} 
[2025-12-13 11:45:34] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:34.469Z"} 
[2025-12-13 11:45:37] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:37.567Z"} 
[2025-12-13 11:45:37] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:37.567Z"} 
[2025-12-13 11:45:38] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:37.855Z"} 
[2025-12-13 11:45:38] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:37.855Z"} 
[2025-12-13 11:45:38] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:38.030Z"} 
[2025-12-13 11:45:38] local.ERROR: ReferenceError: showCreateModal is not defined https://instyle.test/manager/team 1 1 ReferenceError showCreateModal is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:38.030Z"} 
[2025-12-13 11:45:39] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.448Z"} 
[2025-12-13 11:45:39] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.448Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.735Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.735Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.885Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:39.885Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:40.010Z"} 
[2025-12-13 11:45:40] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:40.010Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:46.856Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:46.857Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.031Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.031Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.180Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.180Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.343Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.343Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.495Z"} 
[2025-12-13 11:45:47] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.495Z"} 
[2025-12-13 11:45:48] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.783Z"} 
[2025-12-13 11:45:48] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:47.783Z"} 
[2025-12-13 11:45:48] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:48.084Z"} 
[2025-12-13 11:45:48] local.ERROR: ReferenceError: viewStaff is not defined https://instyle.test/manager/team 1 1 ReferenceError viewStaff is not defined onclick@https://instyle.test/manager/team:1:1 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T11:45:48.084Z"} 
[2025-12-13 13:07:22] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:07:21.910Z"} 
[2025-12-13 13:07:30] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:07:30.381Z"} 
[2025-12-13 13:07:30] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:07:30.381Z"} 
[2025-12-13 13:21:25] local.DEBUG: Submitting edit form with data: 11 <NAME_EMAIL> null +255 715 678 901 Hair 2 null full-time active null null null null {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:21:25.532Z"} 
[2025-12-13 13:21:26] local.DEBUG: All values synced, calling updateStaff... {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:21:25.883Z"} 
[2025-12-13 13:22:49] local.DEBUG: Submitting edit form with data: 11 <NAME_EMAIL> null +255 715 678 901 Hair null full-time active null null null null {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:22:49.026Z"} 
[2025-12-13 13:22:49] local.DEBUG: All values synced, calling updateStaff... {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:22:49.374Z"} 
[2025-12-13 13:38:47] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:46.712Z"} 
[2025-12-13 13:38:47] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:46.712Z"} 
[2025-12-13 13:38:48] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.391Z"} 
[2025-12-13 13:38:48] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.391Z"} 
[2025-12-13 13:38:48] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.553Z"} 
[2025-12-13 13:38:48] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.553Z"} 
[2025-12-13 13:38:49] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.716Z"} 
[2025-12-13 13:38:49] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.716Z"} 
[2025-12-13 13:38:49] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.879Z"} 
[2025-12-13 13:38:49] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:48.879Z"} 
[2025-12-13 13:38:51] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.550Z"} 
[2025-12-13 13:38:51] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.550Z"} 
[2025-12-13 13:38:52] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.711Z"} 
[2025-12-13 13:38:52] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.712Z"} 
[2025-12-13 13:38:52] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.886Z"} 
[2025-12-13 13:38:52] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:51.887Z"} 
[2025-12-13 13:38:54] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.372Z"} 
[2025-12-13 13:38:54] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.372Z"} 
[2025-12-13 13:38:54] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.520Z"} 
[2025-12-13 13:38:54] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.520Z"} 
[2025-12-13 13:38:55] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.682Z"} 
[2025-12-13 13:38:55] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:54.682Z"} 
[2025-12-13 13:38:55] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.375Z"} 
[2025-12-13 13:38:55] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.375Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.522Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.522Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.689Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.689Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.901Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:55.901Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.540Z"} 
[2025-12-13 13:38:56] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.540Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.700Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.700Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.826Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:56.826Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:57.607Z"} 
[2025-12-13 13:38:57] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:57.607Z"} 
[2025-12-13 13:38:58] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:57.779Z"} 
[2025-12-13 13:38:58] local.ERROR: ReferenceError: staffData is not defined https://instyle.test/manager/team 1398 23 ReferenceError staffData is not defined viewStaff@https://instyle.test/manager/team:1398:23
onclick@https://instyle.test/manager/team:1:10 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:38:57.779Z"} 
[2025-12-13 13:43:05] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:43:05.117Z"} 
[2025-12-13 13:43:41] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:43:40.513Z"} 
[2025-12-13 13:44:39] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments#","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T13:44:38.431Z"} 
[2025-12-13 20:22:12] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:22:11.966Z"} 
[2025-12-13 20:22:16] local.ERROR: SyntaxError: redeclaration of let servicesData https://instyle.test/manager/services line 7859 > injectedScript 1 1 SyntaxError redeclaration of let servicesData @https://instyle.test/manager/services line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:22:16.243Z"} 
[2025-12-13 20:22:16] local.ERROR: SyntaxError: redeclaration of let servicesData https://instyle.test/manager/services line 7859 > injectedScript 1 1 SyntaxError redeclaration of let servicesData @https://instyle.test/manager/services line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:22:16.243Z"} 
[2025-12-13 20:22:25] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:22:24.444Z"} 
[2025-12-13 20:23:30] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:23:30.091Z"} 
[2025-12-13 20:23:33] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:23:32.647Z"} 
[2025-12-13 20:23:33] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:23:32.647Z"} 
[2025-12-13 20:25:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:25:23.840Z"} 
[2025-12-13 20:40:07] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:07.514Z"} 
[2025-12-13 20:40:17] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:17.364Z"} 
[2025-12-13 20:40:17] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:17.364Z"} 
[2025-12-13 20:40:20] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:19.849Z"} 
[2025-12-13 20:40:20] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:19.849Z"} 
[2025-12-13 20:40:20] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:19.866Z"} 
[2025-12-13 20:40:29] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:29.351Z"} 
[2025-12-13 20:40:29] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:29.351Z"} 
[2025-12-13 20:40:29] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:40:29.367Z"} 
[2025-12-13 20:50:04] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 5 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:50:03.678Z"} 
[2025-12-13 20:50:04] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 5 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:50:03.679Z"} 
[2025-12-13 20:52:03] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 9 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:03.575Z"} 
[2025-12-13 20:52:03] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 9 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:03.575Z"} 
[2025-12-13 20:52:35] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 13 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:35.299Z"} 
[2025-12-13 20:52:35] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 13 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:35.299Z"} 
[2025-12-13 20:52:56] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 17 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:55.916Z"} 
[2025-12-13 20:52:56] local.ERROR: SyntaxError: expected expression, got '<' https://instyle.test/manager/services 1498 17 SyntaxError expected expression, got '<' null {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T20:52:55.916Z"} 
[2025-12-13 21:01:19] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:01:18.809Z"} 
[2025-12-13 21:01:19] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:01:18.809Z"} 
[2025-12-13 21:01:22] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:01:22.269Z"} 
[2025-12-13 21:01:22] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:01:22.269Z"} 
[2025-12-13 21:02:59] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.431Z"} 
[2025-12-13 21:02:59] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.431Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.782Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.782Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.904Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:02:59.904Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.055Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.055Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.169Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.169Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.318Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.318Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.468Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.468Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.631Z"} 
[2025-12-13 21:03:00] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.631Z"} 
[2025-12-13 21:03:01] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.782Z"} 
[2025-12-13 21:03:01] local.ERROR: ReferenceError: hideServiceModal is not defined https://instyle.test/manager/services 1 1 ReferenceError hideServiceModal is not defined onclick@https://instyle.test/manager/services:1:1 {"url":"https://instyle.test/manager/services","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:03:00.782Z"} 
[2025-12-13 21:09:34] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8105:35
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8100:43
whenTheBackOrForwardButtonIsClicked/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7433:19 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:09:33.748Z"} 
[2025-12-13 21:09:34] local.ERROR: SyntaxError: redeclaration of let staffData https://instyle.test/manager/team line 7859 > injectedScript 1 1 SyntaxError redeclaration of let staffData @https://instyle.test/manager/team line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8105:35
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8100:43
whenTheBackOrForwardButtonIsClicked/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7433:19 {"url":"https://instyle.test/manager/team","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:09:33.748Z"} 
[2025-12-13 21:09:57] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:09:57.320Z"} 
[2025-12-13 21:10:01] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:10:01.023Z"} 
[2025-12-13 21:10:02] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T21:10:02.383Z"} 
[2025-12-13 22:03:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://instyle.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:03:33.190Z"} 
[2025-12-13 22:03:35] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"https://instyle.test/manager/services/categories","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:03:35.448Z"} 
[2025-12-13 22:25:43] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:25:43.036Z"} 
[2025-12-13 22:26:14] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:26:14.401Z"} 
[2025-12-13 22:26:14] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:26:14.401Z"} 
[2025-12-13 22:26:14] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:26:14.415Z"} 
[2025-12-13 22:27:00] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:27:00.091Z"} 
[2025-12-13 22:27:00] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:27:00.091Z"} 
[2025-12-13 22:27:00] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:27:00.108Z"} 
[2025-12-13 22:28:51] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:28:50.745Z"} 
[2025-12-13 22:28:55] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:28:55.571Z"} 
[2025-12-13 22:29:18] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:29:18.405Z"} 
[2025-12-13 22:30:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:30:23.643Z"} 
[2025-12-13 22:30:40] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:30:40.036Z"} 
[2025-12-13 22:30:40] local.ERROR: SyntaxError: redeclaration of let currentAppointmentId https://instyle.test/manager/appointments line 7859 > injectedScript 1 1 SyntaxError redeclaration of let currentAppointmentId @https://instyle.test/manager/appointments line 7859 > injectedScript:1:1
swapCurrentPageWithNewHtml@https://instyle.test/livewire/livewire.js?id=646f9d24:7859:19
navigate_default/navigateTo/</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8055:37
preventAlpineFromPickingUpDomChanges@https://instyle.test/livewire/livewire.js?id=646f9d24:8132:13
navigate_default/navigateTo/<@https://instyle.test/livewire/livewire.js?id=646f9d24:8045:45
getPretchedHtmlOr/prefetches[uri].whenFinished@https://instyle.test/livewire/livewire.js?id=646f9d24:7588:16
storeThePrefetchedHtmlForWhenALinkIsClicked@https://instyle.test/livewire/livewire.js?id=646f9d24:7572:11
navigate_default/</</<@https://instyle.test/livewire/livewire.js?id=646f9d24:8024:54
prefetchHtml/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7564:15
performFetch/<@https://instyle.test/livewire/livewire.js?id=646f9d24:7551:15 {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:30:40.036Z"} 
[2025-12-13 22:30:40] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:30:40.055Z"} 
[2025-12-13 22:32:37] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:32:36.831Z"} 
[2025-12-13 22:32:39] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:32:39.660Z"} 
[2025-12-13 22:36:03] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:36:03.162Z"} 
[2025-12-13 22:36:49] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:36:48.696Z"} 
[2025-12-13 22:37:06] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:37:06.632Z"} 
[2025-12-13 22:37:09] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:37:09.153Z"} 
[2025-12-14 01:40:39] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?page=3","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:40:39.292Z"} 
[2025-12-14 01:40:46] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:40:45.787Z"} 
[2025-12-14 01:41:13] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:41:13.378Z"} 
[2025-12-14 01:41:30] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:41:30.584Z"} 
[2025-12-14 01:44:04] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:44:03.703Z"} 
[2025-12-14 01:45:09] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?page=6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:45:08.001Z"} 
[2025-12-14 01:45:23] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?page=6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:45:22.047Z"} 
[2025-12-14 01:46:22] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:46:21.909Z"} 
[2025-12-14 01:47:22] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:47:22.441Z"} 
[2025-12-14 01:48:03] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?page=6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:48:03.320Z"} 
[2025-12-14 01:48:37] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?page=1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:48:37.262Z"} 
[2025-12-14 01:52:14] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:52:13.835Z"} 
[2025-12-14 01:54:14] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:54:13.772Z"} 
[2025-12-14 01:54:44] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:54:43.975Z"} 
[2025-12-14 01:55:35] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:55:35.122Z"} 
[2025-12-14 01:56:04] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:56:03.828Z"} 
[2025-12-14 01:57:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T22:57:23.728Z"} 
[2025-12-14 02:03:39] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:03:39.167Z"} 
[2025-12-14 02:03:43] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:03:43.039Z"} 
[2025-12-14 02:03:47] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:03:46.833Z"} 
[2025-12-14 02:03:50] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:03:50.017Z"} 
[2025-12-14 02:03:55] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:03:54.929Z"} 
[2025-12-14 02:04:23] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:04:22.738Z"} 
[2025-12-14 02:04:29] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:04:29.188Z"} 
[2025-12-14 02:04:36] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:04:36.102Z"} 
[2025-12-14 02:05:05] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:05:05.571Z"} 
[2025-12-14 02:05:15] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:05:14.825Z"} 
[2025-12-14 02:05:24] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:05:24.449Z"} 
[2025-12-14 02:05:40] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:05:40.469Z"} 
[2025-12-14 02:09:18] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments?date=2025-12-14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:09:18.092Z"} 
[2025-12-14 02:09:20] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:09:20.203Z"} 
[2025-12-14 02:09:55] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:09:55.098Z"} 
[2025-12-14 02:11:04] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:11:04.485Z"} 
[2025-12-14 02:11:15] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:11:15.007Z"} 
[2025-12-14 02:12:16] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:12:16.012Z"} 
[2025-12-14 02:15:50] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:15:50.290Z"} 
[2025-12-14 02:15:54] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:15:53.690Z"} 
[2025-12-14 02:15:57] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:15:57.128Z"} 
[2025-12-14 02:17:00] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:17:00.267Z"} 
[2025-12-14 02:17:13] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:17:12.682Z"} 
[2025-12-14 02:19:58] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:19:57.630Z"} 
[2025-12-14 02:20:08] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:20:08.361Z"} 
[2025-12-14 02:20:53] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:20:53.432Z"} 
[2025-12-14 02:21:13] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:21:13.181Z"} 
[2025-12-14 02:21:31] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:21:31.508Z"} 
[2025-12-14 02:22:05] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:22:05.330Z"} 
[2025-12-14 02:24:14] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:24:13.944Z"} 
[2025-12-14 02:25:40] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:25:40.008Z"} 
[2025-12-14 02:26:32] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:26:31.929Z"} 
[2025-12-14 02:26:47] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:26:46.638Z"} 
[2025-12-14 02:27:20] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:27:20.089Z"} 
[2025-12-14 02:28:21] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:28:20.994Z"} 
[2025-12-14 02:29:33] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:29:32.785Z"} 
[2025-12-14 02:29:35] local.WARNING: Livewire: [wire:model="selectAll"] property does not exist on component: [manager.appointments]  {"url":"https://instyle.test/manager/appointments","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:29:34.829Z"} 
[2025-12-14 02:30:40] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:30:39.772Z"} 
[2025-12-14 02:31:00] local.WARNING: Livewire: [wire:model="service_id"] property does not exist on component: [manager.appointments.calendar]            {"url":"https://instyle.test/manager/appointments/calendar","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:146.0) Gecko/20100101 Firefox/146.0","timestamp":"2025-12-13T23:30:59.915Z"} 
