<?php
/**
 * Gallery Management - Admin Panel
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/gallery_functions.php';

/**
 * Helper function to get the correct image URL
 * Handles both file paths and external URLs
 */
function getImageUrl($imagePath) {
    if (empty($imagePath)) {
        return '';
    }

    // If it's already a full URL, return as is
    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
        return $imagePath;
    }

    // If it's a file path, prepend the base path
    return getBasePath() . '/' . ltrim($imagePath, '/');
}

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create':
            $data = [
                'title' => sanitize($_POST['title']),
                'category' => sanitize($_POST['category']),
                'treatment' => sanitize($_POST['treatment']),
                'description' => sanitize($_POST['description']),
                'duration' => sanitize($_POST['duration']),
                'sessions' => (int)($_POST['sessions'] ?? 0),
                'sort_order' => (int)($_POST['sort_order'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];

            // Handle images (both uploads and URLs)
            if ($data['category'] === 'before-after') {
                // Handle before image
                if (!empty($_POST['before_image_url'])) {
                    // Use URL
                    $data['before_image'] = sanitize($_POST['before_image_url']);
                } elseif (isset($_FILES['before_image']) && $_FILES['before_image']['error'] === UPLOAD_ERR_OK) {
                    // Upload file
                    $uploadResult = uploadGalleryImage($_FILES['before_image'], 'before');
                    if ($uploadResult) {
                        $data['before_image'] = $uploadResult;
                    }
                }

                // Handle after image
                if (!empty($_POST['after_image_url'])) {
                    // Use URL
                    $data['after_image'] = sanitize($_POST['after_image_url']);
                } elseif (isset($_FILES['after_image']) && $_FILES['after_image']['error'] === UPLOAD_ERR_OK) {
                    // Upload file
                    $uploadResult = uploadGalleryImage($_FILES['after_image'], 'after');
                    if ($uploadResult) {
                        $data['after_image'] = $uploadResult;
                    }
                }
            } else {
                // Handle clinic image
                if (!empty($_POST['image_url'])) {
                    // Use URL
                    $data['image'] = sanitize($_POST['image_url']);
                } elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    // Upload file
                    $uploadResult = uploadGalleryImage($_FILES['image'], 'clinic');
                    if ($uploadResult) {
                        $data['image'] = $uploadResult;
                    }
                }
            }

            $result = createGalleryItem($data);
            if ($result) {
                $_SESSION['success'] = 'Gallery item created successfully!';
            } else {
                $_SESSION['error'] = 'Failed to create gallery item.';
            }
            break;

        case 'update':
            $id = (int)$_POST['id'];
            $data = [
                'title' => sanitize($_POST['title']),
                'category' => sanitize($_POST['category']),
                'treatment' => sanitize($_POST['treatment']),
                'description' => sanitize($_POST['description']),
                'duration' => sanitize($_POST['duration']),
                'sessions' => (int)($_POST['sessions'] ?? 0),
                'sort_order' => (int)($_POST['sort_order'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];

            // Get existing item for image handling
            $existingItem = $database->fetch("SELECT * FROM gallery_items WHERE id = ?", [$id]);
            if ($existingItem) {
                $data['before_image'] = $existingItem['before_image'];
                $data['after_image'] = $existingItem['after_image'];
                $data['image'] = $existingItem['image'];

                // Handle new images (both uploads and URLs)
                if ($data['category'] === 'before-after') {
                    // Handle before image
                    if (!empty($_POST['before_image_url'])) {
                        // Use URL - delete old file if it exists and is a file path
                        if ($existingItem['before_image'] && !filter_var($existingItem['before_image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['before_image']);
                        }
                        $data['before_image'] = sanitize($_POST['before_image_url']);
                    } elseif (isset($_FILES['before_image']) && $_FILES['before_image']['error'] === UPLOAD_ERR_OK) {
                        // Upload new file - delete old file if it exists
                        if ($existingItem['before_image'] && !filter_var($existingItem['before_image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['before_image']);
                        }
                        $uploadResult = uploadGalleryImage($_FILES['before_image'], 'before');
                        if ($uploadResult) {
                            $data['before_image'] = $uploadResult;
                        }
                    }

                    // Handle after image
                    if (!empty($_POST['after_image_url'])) {
                        // Use URL - delete old file if it exists and is a file path
                        if ($existingItem['after_image'] && !filter_var($existingItem['after_image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['after_image']);
                        }
                        $data['after_image'] = sanitize($_POST['after_image_url']);
                    } elseif (isset($_FILES['after_image']) && $_FILES['after_image']['error'] === UPLOAD_ERR_OK) {
                        // Upload new file - delete old file if it exists
                        if ($existingItem['after_image'] && !filter_var($existingItem['after_image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['after_image']);
                        }
                        $uploadResult = uploadGalleryImage($_FILES['after_image'], 'after');
                        if ($uploadResult) {
                            $data['after_image'] = $uploadResult;
                        }
                    }
                } else {
                    // Handle clinic image
                    if (!empty($_POST['image_url'])) {
                        // Use URL - delete old file if it exists and is a file path
                        if ($existingItem['image'] && !filter_var($existingItem['image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['image']);
                        }
                        $data['image'] = sanitize($_POST['image_url']);
                    } elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                        // Upload new file - delete old file if it exists
                        if ($existingItem['image'] && !filter_var($existingItem['image'], FILTER_VALIDATE_URL)) {
                            deleteGalleryImage($existingItem['image']);
                        }
                        $uploadResult = uploadGalleryImage($_FILES['image'], 'clinic');
                        if ($uploadResult) {
                            $data['image'] = $uploadResult;
                        }
                    }
                }

                $result = updateGalleryItem($id, $data);
                if ($result) {
                    $_SESSION['success'] = 'Gallery item updated successfully!';
                } else {
                    $_SESSION['error'] = 'Failed to update gallery item.';
                }
            }
            break;

        case 'delete':
            $id = (int)$_POST['id'];
            $existingItem = $database->fetch("SELECT * FROM gallery_items WHERE id = ?", [$id]);
            if ($existingItem) {
                // Delete associated images
                if ($existingItem['before_image']) deleteGalleryImage($existingItem['before_image']);
                if ($existingItem['after_image']) deleteGalleryImage($existingItem['after_image']);
                if ($existingItem['image']) deleteGalleryImage($existingItem['image']);

                $result = deleteGalleryItem($id);
                if ($result) {
                    $_SESSION['success'] = 'Gallery item deleted successfully!';
                } else {
                    $_SESSION['error'] = 'Failed to delete gallery item.';
                }
            }
            break;

        case 'toggle_status':
            $id = (int)$_POST['id'];
            $result = toggleGalleryItemStatus($id);
            if ($result) {
                $_SESSION['success'] = 'Gallery item status updated successfully!';
            } else {
                $_SESSION['error'] = 'Failed to update gallery item status.';
            }
            break;
    }

    redirect('/admin/gallery');
}

// Get filters
$filters = [
    'category' => $_GET['category'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// Get gallery statistics
$stats = getGalleryStats();

// Gallery categories for display
$galleryCategories = [
    'before-after' => 'Before & After',
    'clinic' => 'Clinic Photos'
];

// Get all gallery items with filters using the admin function
$page = 1;
$limit = 1000; // Get all items for now
$galleryData = getGalleryItemsForAdmin($page, $limit, $filters['search'], $filters['category']);
$galleryItems = $galleryData['items'];

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Medical Gallery Management";
$pageDescription = "Manage before/after treatment results and clinic photos for Redolence Medi Aesthetics";
include __DIR__ . '/../../includes/admin_header.php';
?>

<style>
.medical-gallery-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.medical-gallery-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(73, 167, 92, 0.15);
    border-color: rgba(73, 167, 92, 0.2);
}

.medical-header-card {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    border-radius: 20px;
    color: white;
    padding: 2rem;
    margin-bottom: 2rem;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(73, 167, 92, 0.1);
    color: #49a75c;
    border: 1px solid rgba(73, 167, 92, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: rgba(73, 167, 92, 0.2);
    border-color: rgba(73, 167, 92, 0.5);
}

.medical-form-input {
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.medical-form-input:focus {
    border-color: #49a75c;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    outline: none;
}

.medical-form-input:disabled {
    background-color: #f3f4f6 !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.medical-form-input:not(:disabled) {
    background: rgba(255, 255, 255, 0.9) !important;
    cursor: text !important;
}

/* Ensure form fields are interactive */
#galleryForm input:not(:disabled),
#galleryForm select:not(:disabled),
#galleryForm textarea:not(:disabled) {
    pointer-events: auto !important;
    user-select: text !important;
}

#galleryForm input[type="radio"]:not(:disabled),
#galleryForm input[type="checkbox"]:not(:disabled) {
    cursor: pointer !important;
}

.medical-table {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.medical-table th {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    color: white;
    padding: 1rem;
    font-weight: 600;
}

.medical-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
}

.medical-table tr:hover {
    background: rgba(73, 167, 92, 0.05);
}

.medical-modal {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.medical-modal.show {
    opacity: 1;
    visibility: visible;
}

.medical-modal-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
}

.medical-modal.show .medical-modal-content {
    transform: scale(1) translateY(0);
}

/* Form Field Animations */
.medical-form-input {
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(73, 167, 92, 0.15);
}

/* Button Hover Animations */
.medical-btn-primary,
.medical-btn-secondary {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary::before,
.medical-btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.medical-btn-primary:hover::before,
.medical-btn-secondary:hover::before {
    left: 100%;
}

/* Card Animations */
.gallery-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover {
    transform: translateY(-4px) scale(1.02);
}

/* Image Preview Animations */
.image-preview-container {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Animation Enhancements */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Modern Confirmation Dialog */
.modern-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modern-dialog.show {
    opacity: 1;
    visibility: visible;
}

.modern-dialog-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    text-align: center;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modern-dialog.show .modern-dialog-content {
    transform: scale(1);
}

.modern-dialog-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-dialog-icon.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.modern-dialog-icon.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.modern-dialog-icon.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.modern-dialog-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.modern-dialog-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.modern-dialog-btn.primary {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.modern-dialog-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.modern-dialog-btn.secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.modern-dialog-btn.secondary:hover {
    background: rgba(107, 114, 128, 0.2);
}

.modern-dialog-btn.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.modern-dialog-btn.danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Image Source Toggle Styling */
.image-source-toggle {
    background: rgba(73, 167, 92, 0.05);
    border: 1px solid rgba(73, 167, 92, 0.2);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.image-source-toggle label {
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-source-toggle label:hover {
    color: #49a75c;
}

.image-source-toggle input[type="radio"] {
    accent-color: #49a75c;
}

/* Image Preview Enhancements */
.image-preview-container {
    border: 2px dashed rgba(73, 167, 92, 0.3);
    background: rgba(73, 167, 92, 0.05);
    transition: all 0.3s ease;
}

.image-preview-container:hover {
    border-color: rgba(73, 167, 92, 0.5);
    background: rgba(73, 167, 92, 0.1);
}

.image-preview-container img {
    border: 2px solid rgba(73, 167, 92, 0.2);
    transition: all 0.3s ease;
}

.image-preview-container img:hover {
    border-color: rgba(73, 167, 92, 0.4);
    transform: scale(1.05);
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }

    .medical-modal-content {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
        border-radius: 16px;
    }

    .modern-dialog-content {
        padding: 1.5rem;
        margin: 1rem;
        border-radius: 16px;
    }

    .modern-dialog-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modern-dialog-btn {
        width: 100%;
        justify-content: center;
    }

    .gallery-item {
        padding: 0.75rem;
    }

    .medical-gallery-card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .medical-form-input {
        padding: 0.625rem 0.875rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .medical-btn-primary,
    .medical-btn-secondary {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .medical-header-card {
        padding: 1rem;
        border-radius: 12px;
    }

    .medical-header-card h1 {
        font-size: 1.5rem;
    }

    .medical-modal-content {
        margin: 0.25rem;
        max-height: calc(100vh - 0.5rem);
        border-radius: 12px;
    }

    .grid.grid-cols-1.md\\:grid-cols-2.gap-4 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .grid.grid-cols-1.md\\:grid-cols-2.xl\\:grid-cols-3.gap-6 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
</style>

<style>
.medical-gallery-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.medical-gallery-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(73, 167, 92, 0.15);
    border-color: rgba(73, 167, 92, 0.2);
}

.medical-header-card {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    border-radius: 20px;
    color: white;
    padding: 2rem;
    margin-bottom: 2rem;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c 0%, #2563eb 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(73, 167, 92, 0.1);
    color: #49a75c;
    border: 1px solid rgba(73, 167, 92, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: rgba(73, 167, 92, 0.2);
    border-color: rgba(73, 167, 92, 0.5);
}

.gallery-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(73, 167, 92, 0.15);
    border-color: rgba(73, 167, 92, 0.2);
}

.gallery-thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(73, 167, 92, 0.1);
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.medical-form-input {
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.medical-form-input:focus {
    border-color: #49a75c;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    outline: none;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-purple { background: rgba(139, 92, 246, 0.1); color: #7c3aed; }
.badge-blue { background: rgba(59, 130, 246, 0.1); color: #2563eb; }
.badge-green { background: rgba(34, 197, 94, 0.1); color: #16a34a; }
.badge-red { background: rgba(239, 68, 68, 0.1); color: #dc2626; }

.medical-modal {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.medical-modal-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .medical-gallery-card {
        border-radius: 16px;
        padding: 1rem;
    }

    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }

    .medical-modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
            <!-- Medical Header -->
            <div class="medical-header-card">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold">Medical Gallery Management</h1>
                        <p class="text-blue-100 mt-2">Manage before/after treatment results and clinic photos</p>
                        <p class="text-sm text-blue-100 mt-1"><?= count($galleryItems) ?> Total Items | <?= $stats['before_after'] ?> Before/After | <?= $stats['clinic'] ?> Clinic Photos</p>
                    </div>
                    <button onclick="openCreateModal()" class="medical-btn-primary">
                        <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        Add Gallery Item
                    </button>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 <?= $messageType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200' ?> rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 <?= $messageType === 'success' ? 'text-green-600' : 'text-red-600' ?> mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <?php if ($messageType === 'success'): ?>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            <?php else: ?>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            <?php endif; ?>
                        </svg>
                        <span class="<?= $messageType === 'success' ? 'text-green-800' : 'text-red-800' ?>"><?= htmlspecialchars($message) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Controls Section -->
            <div class="medical-gallery-card mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <!-- Search and Filter -->
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <form method="GET" class="flex space-x-4">
                            <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>"
                                   placeholder="Search gallery items..."
                                   class="medical-form-input w-64">
                            <select name="category" class="medical-form-input">
                                <option value="">All Categories</option>
                                <?php foreach ($galleryCategories as $value => $label): ?>
                                    <option value="<?= $value ?>" <?= $filters['category'] === $value ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="medical-btn-secondary">
                                <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                                </svg>
                                Search
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Gallery Grid -->
            <?php if (empty($galleryItems)): ?>
                <div class="medical-gallery-card text-center py-12">
                    <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">No Gallery Items Found</h3>
                    <p class="text-gray-600 mb-6">Get started by creating your first gallery item to showcase treatment results and clinic photos.</p>
                    <button onclick="openCreateModal()" class="medical-btn-primary">
                        <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        Create Your First Gallery Item
                    </button>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    <?php foreach ($galleryItems as $item): ?>
                        <div class="medical-gallery-card">
                            <!-- Item Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-bold text-gray-900 mb-1">
                                        <?= htmlspecialchars($item['title']) ?>
                                    </h3>
                                    <p class="text-gray-600 text-sm">
                                        <?= htmlspecialchars(substr($item['description'], 0, 100)) ?><?= strlen($item['description']) > 100 ? '...' : '' ?>
                                    </p>
                                </div>
                                <div class="flex gap-2 ml-4">
                                    <button onclick="openEditModal('<?= $item['id'] ?>')" class="medical-btn-secondary text-xs px-2 py-1">
                                        <svg class="w-3 h-3 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                        </svg>
                                        Edit
                                    </button>
                                    <button onclick="toggleStatus('<?= $item['id'] ?>')" class="medical-btn-secondary text-xs px-2 py-1">
                                        <svg class="w-3 h-3 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                        </svg>
                                        <?= $item['is_active'] ? 'Hide' : 'Show' ?>
                                    </button>
                                    <button onclick="deleteGalleryItem('<?= $item['id'] ?>', '<?= htmlspecialchars($item['title']) ?>')"
                                            class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-md hover:bg-red-200 border border-red-200">
                                        <svg class="w-3 h-3 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"/>
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </div>

                            <!-- Image Preview -->
                            <div class="mb-4">
                                <?php if ($item['category'] === 'before-after'): ?>
                                    <div class="grid grid-cols-2 gap-2">
                                        <?php if ($item['before_image']): ?>
                                            <div class="gallery-thumbnail">
                                                <img src="<?= htmlspecialchars(getImageUrl($item['before_image'])) ?>"
                                                     alt="Before"
                                                     onerror="this.parentElement.innerHTML='<div class=\'w-full h-full bg-red-100 text-red-500 text-xs flex items-center justify-center\'>Image Error</div>'">
                                            </div>
                                        <?php else: ?>
                                            <div class="gallery-thumbnail bg-gray-100 text-gray-400 text-xs flex items-center justify-center">No Before</div>
                                        <?php endif; ?>

                                        <?php if ($item['after_image']): ?>
                                            <div class="gallery-thumbnail">
                                                <img src="<?= htmlspecialchars(getImageUrl($item['after_image'])) ?>"
                                                     alt="After"
                                                     onerror="this.parentElement.innerHTML='<div class=\'w-full h-full bg-red-100 text-red-500 text-xs flex items-center justify-center\'>Image Error</div>'">
                                            </div>
                                        <?php else: ?>
                                            <div class="gallery-thumbnail bg-gray-100 text-gray-400 text-xs flex items-center justify-center">No After</div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <?php if ($item['image']): ?>
                                        <div class="gallery-thumbnail w-full h-32">
                                            <img src="<?= htmlspecialchars(getImageUrl($item['image'])) ?>"
                                                 alt="<?= htmlspecialchars($item['title']) ?>"
                                                 onerror="this.parentElement.innerHTML='<div class=\'w-full h-full bg-red-100 text-red-500 text-xs flex items-center justify-center\'>Image Error</div>'">
                                        </div>
                                    <?php else: ?>
                                        <div class="gallery-thumbnail w-full h-32 bg-gray-100 text-gray-400 flex items-center justify-center">No Image</div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <!-- Item Details -->
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-xs font-medium text-gray-500 uppercase mb-1">Category</p>
                                    <span class="badge <?= $item['category'] === 'before-after' ? 'badge-purple' : 'badge-blue' ?>">
                                        <?= htmlspecialchars($galleryCategories[$item['category']] ?? $item['category']) ?>
                                    </span>
                                </div>
                                <div>
                                    <p class="text-xs font-medium text-gray-500 uppercase mb-1">Status</p>
                                    <span class="badge <?= $item['is_active'] ? 'badge-green' : 'badge-red' ?>">
                                        <?= $item['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>
                            </div>

                            <!-- Treatment & Date -->
                            <div class="flex justify-between items-end">
                                <div>
                                    <?php if ($item['treatment']): ?>
                                        <p class="text-xs font-medium text-gray-500 uppercase mb-1">Treatment</p>
                                        <p class="text-gray-900 font-medium text-sm">
                                            <?= htmlspecialchars($item['treatment']) ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs font-medium text-gray-500 uppercase mb-1">Created</p>
                                    <p class="text-gray-600 text-sm">
                                        <?= date('M j, Y', strtotime($item['created_at'])) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>
    </div>
</div>

<!-- Medical Gallery Modal -->
<div id="galleryModal" class="medical-modal fixed inset-0 hidden z-50 flex items-center justify-center p-4">
    <div class="medical-modal-content max-w-2xl w-full p-6">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 id="modalTitle" class="text-xl font-bold">Add Gallery Item</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 text-xl">&times;</button>
            </div>

            <form id="galleryForm" method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="id" id="itemId">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Title *</label>
                        <input type="text" name="title" id="title" class="medical-form-input w-full" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Category *</label>
                        <select name="category" id="category" class="medical-form-input w-full" required onchange="toggleCategoryFields()">
                            <option value="">Select Category</option>
                            <option value="before-after">Before & After</option>
                            <option value="clinic">Clinic Photo</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium mb-2 text-gray-700">Description *</label>
                    <textarea name="description" id="description" class="medical-form-input w-full" rows="3" required></textarea>
                </div>

                <div id="treatmentGroup" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Treatment</label>
                        <input type="text" name="treatment" id="treatment" class="medical-form-input w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Duration</label>
                        <input type="text" name="duration" id="duration" class="medical-form-input w-full" placeholder="e.g., 6 weeks">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Sessions</label>
                        <input type="number" name="sessions" id="sessions" class="medical-form-input w-full" min="1">
                    </div>
                </div>

                <!-- Before/After Images -->
                <div id="beforeAfterFields" style="display: none;">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-2 text-gray-700">Before Image</label>

                            <!-- Image Source Toggle -->
                            <div class="mb-3">
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="before_image_source" value="upload" class="mr-2" checked onchange="toggleImageSource('before')">
                                        <span class="text-sm">Upload File</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="before_image_source" value="url" class="mr-2" onchange="toggleImageSource('before')">
                                        <span class="text-sm">Image URL</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Upload Option -->
                            <div id="before_upload_option">
                                <input type="file" name="before_image" id="before_image" class="medical-form-input w-full" accept="image/*">
                                <p class="text-xs text-gray-500 mt-1">Max 5MB. JPEG, PNG, WebP supported.</p>
                            </div>

                            <!-- URL Option -->
                            <div id="before_url_option" style="display: none;">
                                <input type="url" name="before_image_url" id="before_image_url" class="medical-form-input w-full" placeholder="https://example.com/image.jpg">
                                <p class="text-xs text-gray-500 mt-1">Enter a valid image URL (JPEG, PNG, WebP).</p>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2 text-gray-700">After Image</label>

                            <!-- Image Source Toggle -->
                            <div class="mb-3">
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="after_image_source" value="upload" class="mr-2" checked onchange="toggleImageSource('after')">
                                        <span class="text-sm">Upload File</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="after_image_source" value="url" class="mr-2" onchange="toggleImageSource('after')">
                                        <span class="text-sm">Image URL</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Upload Option -->
                            <div id="after_upload_option">
                                <input type="file" name="after_image" id="after_image" class="medical-form-input w-full" accept="image/*">
                                <p class="text-xs text-gray-500 mt-1">Max 5MB. JPEG, PNG, WebP supported.</p>
                            </div>

                            <!-- URL Option -->
                            <div id="after_url_option" style="display: none;">
                                <input type="url" name="after_image_url" id="after_image_url" class="medical-form-input w-full" placeholder="https://example.com/image.jpg">
                                <p class="text-xs text-gray-500 mt-1">Enter a valid image URL (JPEG, PNG, WebP).</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Clinic Image -->
                <div id="clinicFields" style="display: none;">
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Clinic Image</label>

                        <!-- Image Source Toggle -->
                        <div class="mb-3">
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="image_source" value="upload" class="mr-2" checked onchange="toggleImageSource('clinic')">
                                    <span class="text-sm">Upload File</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="image_source" value="url" class="mr-2" onchange="toggleImageSource('clinic')">
                                    <span class="text-sm">Image URL</span>
                                </label>
                            </div>
                        </div>

                        <!-- Upload Option -->
                        <div id="clinic_upload_option">
                            <input type="file" name="image" id="image" class="medical-form-input w-full" accept="image/*">
                            <p class="text-xs text-gray-500 mt-1">Max 5MB. JPEG, PNG, WebP supported.</p>
                        </div>

                        <!-- URL Option -->
                        <div id="clinic_url_option" style="display: none;">
                            <input type="url" name="image_url" id="image_url" class="medical-form-input w-full" placeholder="https://example.com/image.jpg">
                            <p class="text-xs text-gray-500 mt-1">Enter a valid image URL (JPEG, PNG, WebP).</p>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2 text-gray-700">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" class="medical-form-input w-full" value="0">
                    </div>
                    <div class="flex items-center">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" checked class="mr-2 text-redolence-green">
                            <span class="font-medium text-gray-700">Active</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeModal()" class="medical-btn-secondary">Cancel</button>
                    <button type="submit" class="medical-btn-primary">
                        <span id="submitText">Create Gallery Item</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<script>
// Medical Gallery Management JavaScript

// Modern Dialog Functions
function showDialog(title, message, type = 'warning', confirmText = 'Confirm', cancelText = 'Cancel') {
    return new Promise((resolve) => {
        const dialog = document.getElementById('modernDialog');
        const dialogTitle = document.getElementById('dialogTitle');
        const dialogMessage = document.getElementById('dialogMessage');
        const dialogIcon = document.getElementById('dialogIcon');
        const confirmBtn = document.getElementById('dialogConfirm');
        const cancelBtn = document.getElementById('dialogCancel');

        dialogTitle.textContent = title;
        dialogMessage.textContent = message;
        confirmBtn.textContent = confirmText;

        // Show/hide cancel button based on whether it's needed
        if (cancelText && type !== 'info') {
            cancelBtn.textContent = cancelText;
            cancelBtn.style.display = 'inline-block';
        } else {
            cancelBtn.style.display = 'none';
        }

        // Update icon and button style based on type
        dialogIcon.className = `modern-dialog-icon ${type}`;

        if (type === 'error') {
            confirmBtn.className = 'modern-dialog-btn danger';
            dialogIcon.innerHTML = `
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
            `;
        } else if (type === 'warning') {
            confirmBtn.className = 'modern-dialog-btn danger';
            dialogIcon.innerHTML = `
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
            `;
        } else if (type === 'success') {
            confirmBtn.className = 'modern-dialog-btn primary';
            dialogIcon.innerHTML = `
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
            `;
        } else {
            confirmBtn.className = 'modern-dialog-btn primary';
            dialogIcon.innerHTML = `
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            `;
        }

        dialog.classList.add('show');

        const handleConfirm = () => {
            dialog.classList.remove('show');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            resolve(true);
        };

        const handleCancel = () => {
            dialog.classList.remove('show');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            resolve(false);
        };

        confirmBtn.addEventListener('click', handleConfirm);
        if (cancelBtn.style.display !== 'none') {
            cancelBtn.addEventListener('click', handleCancel);
        }

        // Close on backdrop click for info dialogs
        if (type === 'info') {
            const handleBackdropClick = (e) => {
                if (e.target === dialog) {
                    dialog.classList.remove('show');
                    dialog.removeEventListener('click', handleBackdropClick);
                    resolve(true);
                }
            };
            dialog.addEventListener('click', handleBackdropClick);
        }
    });
}

// Toast Notification Functions
function showToast(title, message, type = 'success', duration = 5000) {
    const toast = document.getElementById('toast');
    const toastTitle = document.getElementById('toastTitle');
    const toastMessage = document.getElementById('toastMessage');
    const toastIcon = document.getElementById('toastIcon');
    const toastContainer = toast.querySelector('.bg-white');

    toastTitle.textContent = title;
    toastMessage.textContent = message;

    // Clear any existing auto-hide timeout
    if (toast.hideTimeout) {
        clearTimeout(toast.hideTimeout);
    }

    // Update icon and colors based on type
    if (type === 'success') {
        toastContainer.className = 'bg-white rounded-lg shadow-lg border-l-4 border-green-500 p-4 max-w-sm';
        toastIcon.innerHTML = `
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
            </div>
        `;
    } else if (type === 'error') {
        toastContainer.className = 'bg-white rounded-lg shadow-lg border-l-4 border-red-500 p-4 max-w-sm';
        toastIcon.innerHTML = `
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
            </div>
        `;
    } else if (type === 'warning') {
        toastContainer.className = 'bg-white rounded-lg shadow-lg border-l-4 border-yellow-500 p-4 max-w-sm';
        toastIcon.innerHTML = `
            <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
            </div>
        `;
    } else if (type === 'info') {
        toastContainer.className = 'bg-white rounded-lg shadow-lg border-l-4 border-blue-500 p-4 max-w-sm';
        toastIcon.innerHTML = `
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
        `;
    }

    // Show toast with slide-in animation
    toast.style.transform = 'translateX(0)';

    // Handle progress bar animation
    const progressBar = document.getElementById('toastProgressBar');
    const progressContainer = document.getElementById('toastProgress');

    if (duration > 0) {
        // Show progress bar
        progressContainer.style.display = 'block';
        progressBar.style.width = '100%';

        // Animate progress bar
        setTimeout(() => {
            progressBar.style.transition = `width ${duration}ms linear`;
            progressBar.style.width = '0%';
        }, 50);

        // Auto hide after specified duration
        toast.hideTimeout = setTimeout(() => {
            hideToast();
        }, duration);
    } else {
        // Hide progress bar for persistent toasts
        progressContainer.style.display = 'none';
    }
}

function hideToast() {
    const toast = document.getElementById('toast');
    toast.style.transform = 'translateX(100%)';

    // Clear timeout if it exists
    if (toast.hideTimeout) {
        clearTimeout(toast.hideTimeout);
        delete toast.hideTimeout;
    }
}

// Loading State Functions
function showLoadingState(message = 'Loading...') {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingMessage = document.getElementById('loadingMessage');
    loadingMessage.textContent = message;
    loadingOverlay.classList.remove('hidden');

    // Store original disabled states and disable form interactions while loading
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            // Store the original disabled state
            input.dataset.originalDisabled = input.disabled.toString();
            input.disabled = true;
        });
    });
}

function hideLoadingState() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('hidden');

    // Restore original disabled states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            // Restore the original disabled state
            const originalDisabled = input.dataset.originalDisabled;
            if (originalDisabled !== undefined) {
                input.disabled = originalDisabled === 'true';
                delete input.dataset.originalDisabled;
            } else {
                input.disabled = false;
            }
        });
    });
}

// Network Error Handling
function handleNetworkError(error, operation = 'operation') {
    console.error(`Network error during ${operation}:`, error);

    let message = 'A network error occurred. Please check your connection and try again.';

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
        message = 'Unable to connect to the server. Please check your internet connection.';
    } else if (error.status === 404) {
        message = 'The requested resource was not found.';
    } else if (error.status === 403) {
        message = 'You do not have permission to perform this action.';
    } else if (error.status === 500) {
        message = 'A server error occurred. Please try again later.';
    } else if (error.status >= 400 && error.status < 500) {
        message = 'Invalid request. Please check your input and try again.';
    }

    showToast('Network Error', message, 'error', 8000);
}

// Form Management Functions
function enableFormFields() {
    const form = document.getElementById('galleryForm');
    if (form) {
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach((input, index) => {
            input.disabled = false;
            input.removeAttribute('readonly');
            // Remove any data attributes that might be causing issues
            delete input.dataset.originalDisabled;

            // Debug: Log each input state
            console.log(`Input ${index}: ${input.name || input.id || input.type} - disabled: ${input.disabled}, readonly: ${input.readOnly}`);
        });

        console.log('Form fields enabled. Total inputs:', inputs.length);

        // Force focus on first input to test interactivity
        const firstInput = form.querySelector('input[type="text"], input[type="url"], select, textarea');
        if (firstInput) {
            setTimeout(() => {
                firstInput.focus();
                console.log('Focused on first input:', firstInput.name || firstInput.id);
            }, 200);
        }
    } else {
        console.error('Gallery form not found!');
    }
}

function debugFormState() {
    const form = document.getElementById('galleryForm');
    if (form) {
        console.log('=== FORM DEBUG ===');
        console.log('Form element:', form);
        console.log('Form disabled:', form.disabled);

        const inputs = form.querySelectorAll('input, select, textarea, button');
        console.log('Total form inputs:', inputs.length);

        inputs.forEach((input, index) => {
            if (input.disabled || input.readOnly) {
                console.warn(`Input ${index} (${input.name || input.id || input.type}) is disabled/readonly:`, {
                    disabled: input.disabled,
                    readonly: input.readOnly,
                    style: input.style.pointerEvents
                });
            }
        });
        console.log('=== END DEBUG ===');
    }
}

function resetFormToDefaults() {
    // Reset radio buttons to default (upload)
    const uploadRadios = document.querySelectorAll('input[type="radio"][value="upload"]');
    uploadRadios.forEach(radio => {
        radio.checked = true;
    });

    // Reset image source toggles
    toggleImageSource('before');
    toggleImageSource('after');
    toggleImageSource('clinic');
}

// Gallery Modal Functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Gallery Item';
    document.getElementById('formAction').value = 'create';
    document.getElementById('submitText').textContent = 'Create Gallery Item';
    document.getElementById('galleryForm').reset();
    document.getElementById('itemId').value = '';
    clearExistingImages();
    resetFormToDefaults();
    toggleCategoryFields();
    enableFormFields();

    // Show modal with animation
    const modal = document.getElementById('galleryModal');
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.add('show');
        // Set up focus management
        trapFocus(modal);
        // Ensure form is enabled after modal is shown
        setTimeout(() => {
            enableFormFields();
        }, 100);
    }, 10);
}

function closeModal() {
    const modal = document.getElementById('galleryModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

async function openEditModal(id) {
    try {
        // Show loading state
        showLoadingState('Loading gallery item...');

        // Load gallery item data via AJAX
        const response = await fetch('get_gallery_item.php?id=' + id);
        const data = await response.json();

        hideLoadingState();

        if (data.success) {
            const item = data.data;

            // Populate form fields
            document.getElementById('modalTitle').textContent = 'Edit Gallery Item';
            document.getElementById('formAction').value = 'update';
            document.getElementById('submitText').textContent = 'Update Gallery Item';
            document.getElementById('itemId').value = item.id;
            document.getElementById('title').value = item.title;
            document.getElementById('category').value = item.category;
            document.getElementById('treatment').value = item.treatment || '';
            document.getElementById('description').value = item.description;
            document.getElementById('duration').value = item.duration || '';
            document.getElementById('sessions').value = item.sessions || '';
            document.getElementById('sort_order').value = item.sort_order;
            document.getElementById('is_active').checked = item.is_active == 1;

            // Show existing images preview
            showExistingImages(item);

            // Set image source options based on existing data
            setImageSourceOptions(item);

            // Toggle category fields
            toggleCategoryFields();

            // Ensure all form fields are enabled
            enableFormFields();

            // Show modal with animation
            const modal = document.getElementById('galleryModal');
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.classList.add('show');
                // Set up focus management
                trapFocus(modal);
                // Ensure form is enabled after modal is shown
                setTimeout(() => {
                    enableFormFields();
                    debugFormState();
                }, 100);
            }, 10);
        } else {
            await showDialog(
                'Error Loading Item',
                'Error loading gallery item: ' + data.error,
                'error',
                'OK'
            );
        }
    } catch (error) {
        hideLoadingState();
        handleNetworkError(error, 'loading gallery item');
    }
}

function toggleStatus(id) {
    if (confirm('Are you sure you want to toggle the status of this gallery item?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="toggle_status"><input type="hidden" name="id" value="' + id + '">';
        document.body.appendChild(form);
        form.submit();
    }
}

function closeModal() {
    document.getElementById('galleryModal').classList.add('hidden');
}

// Image Source Options Functions
function setImageSourceOptions(item) {
    if (item.category === 'before-after') {
        // Set before image source
        if (item.before_image) {
            const isUrl = isValidUrl(item.before_image);
            const beforeUploadRadio = document.querySelector('input[name="before_image_source"][value="upload"]');
            const beforeUrlRadio = document.querySelector('input[name="before_image_source"][value="url"]');
            const beforeUrlInput = document.getElementById('before_image_url');

            if (isUrl) {
                beforeUrlRadio.checked = true;
                beforeUrlInput.value = item.before_image;
                toggleImageSource('before');
            } else {
                beforeUploadRadio.checked = true;
                toggleImageSource('before');
            }
        }

        // Set after image source
        if (item.after_image) {
            const isUrl = isValidUrl(item.after_image);
            const afterUploadRadio = document.querySelector('input[name="after_image_source"][value="upload"]');
            const afterUrlRadio = document.querySelector('input[name="after_image_source"][value="url"]');
            const afterUrlInput = document.getElementById('after_image_url');

            if (isUrl) {
                afterUrlRadio.checked = true;
                afterUrlInput.value = item.after_image;
                toggleImageSource('after');
            } else {
                afterUploadRadio.checked = true;
                toggleImageSource('after');
            }
        }
    } else {
        // Set clinic image source
        if (item.image) {
            const isUrl = isValidUrl(item.image);
            const clinicUploadRadio = document.querySelector('input[name="image_source"][value="upload"]');
            const clinicUrlRadio = document.querySelector('input[name="image_source"][value="url"]');
            const clinicUrlInput = document.getElementById('image_url');

            if (isUrl) {
                clinicUrlRadio.checked = true;
                clinicUrlInput.value = item.image;
                toggleImageSource('clinic');
            } else {
                clinicUploadRadio.checked = true;
                toggleImageSource('clinic');
            }
        }
    }
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Image Preview Functions
function showExistingImages(item) {
    clearExistingImages();

    if (item.category === 'before-after') {
        if (item.before_image_url) {
            showImagePreview('before_image', item.before_image_url, 'Current Before Image');
        }
        if (item.after_image_url) {
            showImagePreview('after_image', item.after_image_url, 'Current After Image');
        }
    } else if (item.image_url) {
        showImagePreview('image', item.image_url, 'Current Image');
    }
}

function clearExistingImages() {
    const previewContainers = document.querySelectorAll('.image-preview-container');
    previewContainers.forEach(container => container.remove());
}

function showImagePreview(fieldName, imageUrl, label) {
    const field = document.getElementById(fieldName);
    if (!field) return;

    const container = field.parentElement;

    // Remove existing preview
    const existingPreview = container.querySelector('.image-preview-container');
    if (existingPreview) {
        existingPreview.remove();
    }

    // Create preview container
    const previewContainer = document.createElement('div');
    previewContainer.className = 'image-preview-container mt-2 p-3 bg-gray-50 rounded-lg border';

    previewContainer.innerHTML = `
        <div class="flex items-center space-x-3">
            <img src="${imageUrl}" alt="${label}" class="w-16 h-16 object-cover rounded-lg border">
            <div class="flex-1">
                <p class="text-sm font-medium text-gray-700">${label}</p>
                <p class="text-xs text-gray-500">Upload a new file to replace this image</p>
            </div>
        </div>
    `;

    container.appendChild(previewContainer);
}

// Image Source Toggle Functions
function toggleImageSource(type) {
    const uploadOption = document.getElementById(`${type}_upload_option`);
    const urlOption = document.getElementById(`${type}_url_option`);

    // Handle different naming conventions for radio buttons
    let radioName;
    if (type === 'clinic') {
        radioName = 'image_source';
    } else {
        radioName = `${type}_image_source`;
    }

    const sourceRadios = document.querySelectorAll(`input[name="${radioName}"]`);

    // Check if elements exist
    if (!uploadOption || !urlOption || sourceRadios.length === 0) {
        return;
    }

    let selectedSource = 'upload';
    sourceRadios.forEach(radio => {
        if (radio.checked) {
            selectedSource = radio.value;
        }
    });

    if (selectedSource === 'url') {
        uploadOption.style.display = 'none';
        urlOption.style.display = 'block';
        // Clear file input
        const fileInput = uploadOption.querySelector('input[type="file"]');
        if (fileInput) fileInput.value = '';
    } else {
        uploadOption.style.display = 'block';
        urlOption.style.display = 'none';
        // Clear URL input
        const urlInput = urlOption.querySelector('input[type="url"]');
        if (urlInput) urlInput.value = '';
    }
}

function toggleCategoryFields() {
    const category = document.getElementById('category').value;
    const beforeAfterFields = document.getElementById('beforeAfterFields');
    const clinicFields = document.getElementById('clinicFields');
    const treatmentGroup = document.getElementById('treatmentGroup');

    if (category === 'before-after') {
        beforeAfterFields.style.display = 'block';
        clinicFields.style.display = 'none';
        treatmentGroup.style.display = 'block';
    } else if (category === 'clinic') {
        beforeAfterFields.style.display = 'none';
        clinicFields.style.display = 'block';
        treatmentGroup.style.display = 'none';
    } else {
        beforeAfterFields.style.display = 'none';
        clinicFields.style.display = 'none';
        treatmentGroup.style.display = 'none';
    }

    // Clear existing image previews when category changes
    clearExistingImages();
}

async function deleteGalleryItem(id, title) {
    try {
        const confirmed = await showDialog(
            'Delete Gallery Item',
            `Are you sure you want to delete "${title}"? This action cannot be undone and will permanently remove all associated images.`,
            'error',
            'Delete Permanently',
            'Cancel'
        );

        if (confirmed) {
            showLoadingState('Deleting gallery item...');

            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type="hidden" name="action" value="delete"><input type="hidden" name="id" value="' + id + '">';
            document.body.appendChild(form);
            form.submit();
        }
    } catch (error) {
        console.error('Error in deleteGalleryItem:', error);
        showToast('Error', 'An unexpected error occurred. Please try again.', 'error');
    }
}

async function toggleStatus(id) {
    try {
        const confirmed = await showDialog(
            'Toggle Status',
            'Are you sure you want to change the visibility status of this gallery item? This will affect whether it appears on the public gallery.',
            'warning',
            'Toggle Status',
            'Cancel'
        );

        if (confirmed) {
            showLoadingState('Updating gallery item status...');

            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type="hidden" name="action" value="toggle_status"><input type="hidden" name="id" value="' + id + '">';
            document.body.appendChild(form);
            form.submit();
        }
    } catch (error) {
        console.error('Error in toggleStatus:', error);
        showToast('Error', 'Failed to toggle status. Please try again.', 'error');
    }
}

// Form Validation Functions
function validateForm() {
    const title = document.getElementById('title').value.trim();
    const category = document.getElementById('category').value;
    const description = document.getElementById('description').value.trim();

    const errors = [];
    let isValid = true;

    // Title validation
    if (!title) {
        highlightField('title', true, 'Title is required');
        errors.push('Title is required');
        isValid = false;
    } else if (title.length < 3) {
        highlightField('title', true, 'Title must be at least 3 characters long');
        errors.push('Title must be at least 3 characters long');
        isValid = false;
    } else if (title.length > 100) {
        highlightField('title', true, 'Title must be less than 100 characters');
        errors.push('Title must be less than 100 characters');
        isValid = false;
    } else {
        highlightField('title', false);
    }

    // Category validation
    if (!category) {
        highlightField('category', true, 'Please select a category');
        errors.push('Category is required');
        isValid = false;
    } else {
        highlightField('category', false);
    }

    // Description validation
    if (!description) {
        highlightField('description', true, 'Description is required');
        errors.push('Description is required');
        isValid = false;
    } else if (description.length < 10) {
        highlightField('description', true, 'Description must be at least 10 characters long');
        errors.push('Description must be at least 10 characters long');
        isValid = false;
    } else if (description.length > 500) {
        highlightField('description', true, 'Description must be less than 500 characters');
        errors.push('Description must be less than 500 characters');
        isValid = false;
    } else {
        highlightField('description', false);
    }

    // Category-specific validation
    if (category === 'before-after') {
        const treatment = document.getElementById('treatment').value.trim();
        if (!treatment) {
            highlightField('treatment', true, 'Treatment is required for before/after items');
            errors.push('Treatment is required for before/after items');
            isValid = false;
        } else if (treatment.length < 3) {
            highlightField('treatment', true, 'Treatment name must be at least 3 characters long');
            errors.push('Treatment name must be at least 3 characters long');
            isValid = false;
        } else {
            highlightField('treatment', false);
        }

        // Validate sessions if provided
        const sessions = document.getElementById('sessions').value;
        if (sessions && (isNaN(sessions) || sessions < 1 || sessions > 50)) {
            highlightField('sessions', true, 'Sessions must be a number between 1 and 50');
            errors.push('Sessions must be a number between 1 and 50');
            isValid = false;
        } else {
            highlightField('sessions', false);
        }

        // Validate image URLs if using URL option
        const beforeUrlRadio = document.querySelector('input[name="before_image_source"][value="url"]');
        const afterUrlRadio = document.querySelector('input[name="after_image_source"][value="url"]');

        if (beforeUrlRadio && beforeUrlRadio.checked) {
            const beforeUrl = document.getElementById('before_image_url').value.trim();
            if (beforeUrl && !isValidUrl(beforeUrl)) {
                highlightField('before_image_url', true, 'Please enter a valid image URL');
                errors.push('Before image URL is not valid');
                isValid = false;
            } else {
                highlightField('before_image_url', false);
            }
        }

        if (afterUrlRadio && afterUrlRadio.checked) {
            const afterUrl = document.getElementById('after_image_url').value.trim();
            if (afterUrl && !isValidUrl(afterUrl)) {
                highlightField('after_image_url', true, 'Please enter a valid image URL');
                errors.push('After image URL is not valid');
                isValid = false;
            } else {
                highlightField('after_image_url', false);
            }
        }
    } else {
        // Clear treatment field errors for non-before-after categories
        highlightField('treatment', false);
        highlightField('sessions', false);

        // Validate clinic image URL if using URL option
        const clinicUrlRadio = document.querySelector('input[name="image_source"][value="url"]');
        if (clinicUrlRadio && clinicUrlRadio.checked) {
            const clinicUrl = document.getElementById('image_url').value.trim();
            if (clinicUrl && !isValidUrl(clinicUrl)) {
                highlightField('image_url', true, 'Please enter a valid image URL');
                errors.push('Image URL is not valid');
                isValid = false;
            } else {
                highlightField('image_url', false);
            }
        }
    }

    // Sort order validation
    const sortOrder = document.getElementById('sort_order').value;
    if (sortOrder && (isNaN(sortOrder) || sortOrder < 0 || sortOrder > 9999)) {
        highlightField('sort_order', true, 'Sort order must be a number between 0 and 9999');
        errors.push('Sort order must be a number between 0 and 9999');
        isValid = false;
    } else {
        highlightField('sort_order', false);
    }

    return { isValid, errors };
}

// Character Counter Function
function addCharacterCounter(field, maxLength) {
    const container = field.parentElement;

    // Remove existing counter
    const existingCounter = container.querySelector('.character-counter');
    if (existingCounter) {
        existingCounter.remove();
    }

    // Create counter element
    const counter = document.createElement('div');
    counter.className = 'character-counter text-xs text-gray-500 mt-1 text-right';

    const updateCounter = () => {
        const currentLength = field.value.length;
        const remaining = maxLength - currentLength;

        counter.textContent = `${currentLength}/${maxLength}`;

        if (remaining < 20) {
            counter.className = 'character-counter text-xs text-orange-500 mt-1 text-right';
        } else if (remaining < 0) {
            counter.className = 'character-counter text-xs text-red-500 mt-1 text-right font-medium';
        } else {
            counter.className = 'character-counter text-xs text-gray-500 mt-1 text-right';
        }
    };

    // Initial update
    updateCounter();

    // Update on input
    field.addEventListener('input', updateCounter);

    // Add counter to container
    container.appendChild(counter);
}

function highlightField(fieldId, hasError, errorMessage = '') {
    const field = document.getElementById(fieldId);
    const container = field.parentElement;

    // Remove existing error message
    const existingError = container.querySelector('.field-error-message');
    if (existingError) {
        existingError.remove();
    }

    if (hasError) {
        field.classList.add('border-red-500', 'bg-red-50');
        field.classList.remove('border-gray-300');
        field.setAttribute('aria-invalid', 'true');

        // Add error message
        if (errorMessage) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error-message text-red-600 text-sm mt-1 flex items-center';
            errorDiv.innerHTML = `
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                ${errorMessage}
            `;
            container.appendChild(errorDiv);
        }
    } else {
        field.classList.remove('border-red-500', 'bg-red-50');
        field.classList.add('border-gray-300');
        field.setAttribute('aria-invalid', 'false');
    }
}

// Form Submission Handler
document.getElementById('galleryForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    // Validate form
    const validation = validateForm();
    if (!validation.isValid) {
        showToast('Validation Error', 'Please correct the errors below and try again.', 'error', 7000);

        // Focus on first error field
        const firstErrorField = document.querySelector('[aria-invalid="true"]');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#galleryForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
    `;

    try {
        // Submit form
        this.submit();
    } catch (error) {
        // Reset button state on error
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        showToast('Submission Error', 'Failed to submit form. Please try again.', 'error');
    }
});

// Real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const fields = ['title', 'category', 'description', 'treatment', 'sessions', 'sort_order', 'before_image_url', 'after_image_url', 'image_url'];
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // Validate on blur
            field.addEventListener('blur', function() {
                validateForm();
            });

            // Clear error state on input with debouncing
            let timeout;
            field.addEventListener('input', function() {
                // Clear error state immediately
                highlightField(fieldId, false);

                // Debounced validation for better UX
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    if (this.value.trim()) {
                        validateForm();
                    }
                }, 500);
            });

            // Add character count for text fields
            if (fieldId === 'title' || fieldId === 'description') {
                addCharacterCounter(field, fieldId === 'title' ? 100 : 500);
            }
        }
    });

    // Show toast notifications for server messages
    <?php if ($message): ?>
        showToast(
            '<?= $messageType === 'success' ? 'Success' : 'Error' ?>',
            '<?= addslashes($message) ?>',
            '<?= $messageType ?>',
            <?= $messageType === 'success' ? 5000 : 8000 ?>
        );
    <?php endif; ?>

    // Add click handlers to test form interactivity
    document.addEventListener('click', function(e) {
        if (e.target.closest('#galleryModal')) {
            console.log('Click inside modal detected');
            debugFormState();
        }
    });
});

// Accessibility and Keyboard Navigation
document.addEventListener('keydown', function(event) {
    // Close modal on Escape key
    if (event.key === 'Escape') {
        const modal = document.getElementById('galleryModal');
        const dialog = document.getElementById('modernDialog');

        if (!modal.classList.contains('hidden')) {
            closeModal();
        } else if (dialog.classList.contains('show')) {
            dialog.classList.remove('show');
        }
    }

    // Hide toast on Escape key
    if (event.key === 'Escape') {
        const toast = document.getElementById('toast');
        if (toast.style.transform === 'translateX(0px)') {
            hideToast();
        }
    }
});

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('galleryModal');
    if (event.target === modal) {
        closeModal();
    }
});

// Focus management for modals
function trapFocus(element) {
    const focusableElements = element.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    element.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    lastElement.focus();
                    e.preventDefault();
                }
            } else {
                if (document.activeElement === lastElement) {
                    firstElement.focus();
                    e.preventDefault();
                }
            }
        }
    });

    // Focus first element
    firstElement.focus();
}
</script>

<!-- Modern Confirmation Dialog -->
<div id="modernDialog" class="modern-dialog">
    <div class="modern-dialog-content">
        <div id="dialogIcon" class="modern-dialog-icon warning">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
        </div>
        <h3 id="dialogTitle" class="text-lg font-bold text-gray-900 mb-2">Confirm Action</h3>
        <p id="dialogMessage" class="text-gray-600 mb-4">Are you sure you want to perform this action?</p>
        <div class="modern-dialog-buttons">
            <button id="dialogCancel" class="modern-dialog-btn secondary">Cancel</button>
            <button id="dialogConfirm" class="modern-dialog-btn danger">Confirm</button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-filter backdrop-blur-sm z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-xl p-8 max-w-sm mx-4 text-center shadow-2xl">
        <div class="relative mb-6">
            <!-- Medical-themed loading animation -->
            <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 mx-auto">
                <div class="absolute top-0 left-0 h-16 w-16 rounded-full border-4 border-transparent border-t-blue-600 animate-spin"></div>
                <div class="absolute top-2 left-2 h-12 w-12 rounded-full border-4 border-transparent border-t-green-500 animate-spin" style="animation-duration: 1.5s;"></div>
            </div>
            <!-- Medical cross icon in center -->
            <div class="absolute inset-0 flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
        <p id="loadingMessage" class="text-gray-700 font-semibold text-lg mb-2">Loading...</p>
        <p class="text-gray-500 text-sm">Please wait while we process your request</p>
    </div>
</div>

<!-- Success/Error Toast -->
<div id="toast" class="fixed top-4 right-4 z-50 transform translate-x-full transition-all duration-300 ease-in-out">
    <div class="bg-white rounded-lg shadow-lg border-l-4 p-4 max-w-sm min-w-80">
        <div class="flex items-start">
            <div id="toastIcon" class="flex-shrink-0 mr-3 mt-0.5">
                <!-- Icon will be inserted here -->
            </div>
            <div class="flex-1">
                <p id="toastTitle" class="font-semibold text-gray-900 mb-1"></p>
                <p id="toastMessage" class="text-sm text-gray-600"></p>
            </div>
            <button onclick="hideToast()" class="ml-3 text-gray-400 hover:text-gray-600 transition-colors duration-200 flex-shrink-0">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
            </button>
        </div>
        <!-- Progress bar for auto-hide -->
        <div id="toastProgress" class="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden">
            <div id="toastProgressBar" class="h-full bg-current transition-all duration-100 ease-linear" style="width: 100%"></div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
