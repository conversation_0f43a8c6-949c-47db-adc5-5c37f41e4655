@props([
    'icon',
    'label',
    'value',
    'trend' => null,
    'trendUp' => true,
    'iconColor' => 'rose'
])

{{-- Modern minimal metric card with horizontal layout --}}
<div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm hover:shadow-md hover:border-[#E98CA5]/30 transition-all duration-200">
    <div class="flex items-center gap-4">
        {{-- Icon Container --}}
        <div class="flex-shrink-0">
            <div class="w-12 h-12 rounded-full flex items-center justify-center 
                @if($iconColor === 'rose') bg-[#E98CA5]/10
                @elseif($iconColor === 'beige') bg-[#DCC7A1]/10
                @elseif($iconColor === 'green') bg-emerald-500/10
                @else bg-[#E98CA5]/10
                @endif">
                @php
                    $iconColorClass = match($iconColor) {
                        'rose' => 'text-[#E98CA5]',
                        'beige' => 'text-[#DCC7A1]',
                        'green' => 'text-emerald-600',
                        default => 'text-[#E98CA5]'
                    };
                @endphp
                <flux:icon :icon="$icon" class="w-6 h-6 {{ $iconColorClass }}" />
            </div>
        </div>

        {{-- Content --}}
        <div class="flex-1 min-w-0">
            <div class="font-body text-sm font-medium text-[#8B5D66] truncate mb-1">
                {{ $label }}
            </div>
            <div class="font-body text-2xl font-bold text-[#2C2C34]">
                {{ $value }}
            </div>
            
            {{-- Optional Trend --}}
            @if($trend)
                <div class="flex items-center gap-1 font-body text-xs font-medium mt-1 {{ $trendUp ? 'text-emerald-600' : 'text-red-500' }}">
                    @if($trendUp)
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    @else
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                    @endif
                    <span>{{ $trend }}</span>
                </div>
            @endif
        </div>
    </div>
</div>
