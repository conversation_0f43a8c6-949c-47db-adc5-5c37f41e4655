<div>
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if(session()->has('success')): ?>
            <div
                class="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-xl p-4 flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'check-circle','class' => 'w-5 h-5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'check-circle','class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                    <p class="font-medium"><?php echo e(session('success')); ?></p>
                </div>
                <button onclick="this.parentElement.remove()" class="text-green-600 hover:text-green-800">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                </button>
            </div>
        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>

        <?php if(session()->has('error')): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-800 rounded-xl p-4 flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'exclamation-circle','class' => 'w-5 h-5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'exclamation-circle','class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                    <p class="font-medium"><?php echo e(session('error')); ?></p>
                </div>
                <button onclick="this.parentElement.remove()" class="text-red-600 hover:text-red-800">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                </button>
            </div>
        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.page-header','data' => ['title' => __('Appointments'),'subtitle' => __('Manage and track all salon appointments')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Appointments')),'subtitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Manage and track all salon appointments'))]); ?>
             <?php $__env->slot('actions', null, []); ?> 
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['onclick' => 'window.location.href=\''.e(route('manager.appointments.calendar')).'\'','variant' => 'outline','icon' => 'calendar','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onclick' => 'window.location.href=\''.e(route('manager.appointments.calendar')).'\'','variant' => 'outline','icon' => 'calendar','size' => 'md']); ?>
                    Calendar View
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['wire:click' => 'exportAppointments','variant' => 'outline','icon' => 'arrow-down-tray','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'exportAppointments','variant' => 'outline','icon' => 'arrow-down-tray','size' => 'md']); ?>
                    Export
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['onclick' => 'showCreateModal()','variant' => 'primary','icon' => 'plus','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onclick' => 'showCreateModal()','variant' => 'primary','icon' => 'plus','size' => 'md']); ?>
                    New Appointment
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $attributes = $__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__attributesOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d)): ?>
<?php $component = $__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d; ?>
<?php unset($__componentOriginal410e9a77e741f66387ffa9aaa5e15c9d); ?>
<?php endif; ?>

        
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'calendar','label' => __('Total Appointments'),'value' => $totalAppointments,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Total Appointments')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalAppointments),'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'clock','label' => __('Pending'),'value' => $pendingAppointments,'iconColor' => 'beige']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'clock','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Pending')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($pendingAppointments),'iconColor' => 'beige']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'check-circle','label' => __('Confirmed'),'value' => $confirmedAppointments,'iconColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'check-circle','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Confirmed')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($confirmedAppointments),'iconColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalb38da30168ccea280f1b6bd20cb603ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.metric-card','data' => ['icon' => 'calendar-days','label' => __('Today'),'value' => $todayAppointments,'trend' => ($todayTrend > 0 ? '+' : '') . $todayTrend . ' from yesterday','trendUp' => $todayTrend >= 0,'iconColor' => 'rose']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.metric-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar-days','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Today')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($todayAppointments),'trend' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(($todayTrend > 0 ? '+' : '') . $todayTrend . ' from yesterday'),'trendUp' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($todayTrend >= 0),'iconColor' => 'rose']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $attributes = $__attributesOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__attributesOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee)): ?>
<?php $component = $__componentOriginalb38da30168ccea280f1b6bd20cb603ee; ?>
<?php unset($__componentOriginalb38da30168ccea280f1b6bd20cb603ee); ?>
<?php endif; ?>
        </div>

        
        <div class="bg-white rounded-xl p-6 border border-[#EFEFEF] shadow-sm mb-6">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'magnifying-glass','class' => 'absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#8B5D66]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        <input type="text" wire:model.live="search"
                            placeholder="Search by client name, service, or staff..."
                            class="w-full pl-10 pr-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all" />
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($search): ?>
                            <button wire:click="$set('search', '')"
                                class="absolute right-3 top-1/2 -translate-y-1/2 text-[#8B5D66] hover:text-[#E98CA5]">
                                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                            </button>
                        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </div>
                </div>
                <div class="flex gap-3">
                    <select wire:model.live="status"
                        class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all">
                        <option value="all">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="no_show">No Show</option>
                    </select>
                    <input type="date" wire:model.live="date"
                        class="px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] transition-all" />
                    <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($search || $status !== 'all' || $date): ?>
                        <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['wire:click' => 'clearFilters','variant' => 'outline','icon' => 'x-mark','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'clearFilters','variant' => 'outline','icon' => 'x-mark','size' => 'md']); ?>
                            Clear
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                    <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                </div>
            </div>
        </div>

        
        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if(count($selectedAppointments) > 0): ?>
            <div class="bg-[#E98CA5]/10 border border-[#E98CA5]/30 rounded-xl p-4 mb-6 flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'check-circle','class' => 'w-5 h-5 text-[#E98CA5]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'check-circle','class' => 'w-5 h-5 text-[#E98CA5]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                    <span class="font-medium text-[#2C2C34]"><?php echo e(count($selectedAppointments)); ?> selected</span>
                </div>
                <div class="flex gap-2">
                    <?php if (isset($component)) { $__componentOriginal479ded95b7773e4b00e526f5863fdeee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal479ded95b7773e4b00e526f5863fdeee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.button','data' => ['wire:click' => 'bulkDelete','wire:confirm' => 'Are you sure you want to delete selected appointments?','variant' => 'outline','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'bulkDelete','wire:confirm' => 'Are you sure you want to delete selected appointments?','variant' => 'outline','size' => 'sm']); ?>
                        Delete Selected
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $attributes = $__attributesOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__attributesOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal479ded95b7773e4b00e526f5863fdeee)): ?>
<?php $component = $__componentOriginal479ded95b7773e4b00e526f5863fdeee; ?>
<?php unset($__componentOriginal479ded95b7773e4b00e526f5863fdeee); ?>
<?php endif; ?>
                </div>
            </div>
        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>

        
        <div class="bg-white rounded-xl border border-[#EFEFEF] shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-[#EFEFEF]">
                    <thead class="bg-[#F7E9E6]/30">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" wire:model.live="selectAll"
                                    class="rounded border-[#EFEFEF] text-[#E98CA5] focus:ring-[#E98CA5]" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Client</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Service</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Staff</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Date & Time</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Duration</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Status</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-semibold text-[#2C2C34] uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-[#EFEFEF]">
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__empty_1 = true; $__currentLoopData = $appointments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-[#F7E9E6]/20 transition-colors"
                                wire:key="appointment-<?php echo e($appointment->id); ?>">
                                <td class="px-6 py-4">
                                    <input type="checkbox" wire:model.live="selectedAppointments"
                                        value="<?php echo e($appointment->id); ?>"
                                        class="rounded border-[#EFEFEF] text-[#E98CA5] focus:ring-[#E98CA5]" />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center gap-3">
                                        <div
                                            class="w-10 h-10 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold text-sm">
                                            <?php echo e($appointment->client->initials()); ?>

                                        </div>
                                        <div>
                                            <div class="font-body font-semibold text-[#2C2C34] text-sm">
                                                <?php echo e($appointment->client->name); ?></div>
                                            <div class="font-body text-xs text-[#8B5D66]"><?php echo e($appointment->client->email); ?>

                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-body text-sm text-[#2C2C34] font-medium">
                                        <?php echo e($appointment->service->name); ?></div>
                                    <div class="font-body text-xs text-[#8B5D66]">
                                        <?php echo e(number_format((float) $appointment->total_amount, 0)); ?> TSH</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-body text-sm text-[#2C2C34]">
                                        <?php echo e($appointment->staff?->name ?? 'Unassigned'); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-body text-sm text-[#2C2C34] font-medium">
                                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($appointment->appointment_date->isToday()): ?>
                                            Today, <?php echo e($appointment->start_time->format('g:i A')); ?>

                                        <?php elseif($appointment->appointment_date->isTomorrow()): ?>
                                            Tomorrow, <?php echo e($appointment->start_time->format('g:i A')); ?>

                                        <?php else: ?>
                                            <?php echo e($appointment->appointment_date->format('M j, Y')); ?>,
                                            <?php echo e($appointment->start_time->format('g:i A')); ?>

                                        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                    </div>
                                    <div class="font-body text-xs text-[#8B5D66]">
                                        <?php echo e($appointment->appointment_date->format('l, F j, Y')); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-body text-sm text-[#2C2C34]"><?php echo e($appointment->duration); ?> min</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div x-data="{ open: false }" class="relative">
                                        <button @click="open = !open" @click.away="open = false" class="w-full">
                                            <?php if (isset($component)) { $__componentOriginal4af424b90c762a172adad803e3194c97 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4af424b90c762a172adad803e3194c97 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.manager.status-badge','data' => ['status' => $appointment->status]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('manager.status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($appointment->status)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $attributes = $__attributesOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__attributesOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4af424b90c762a172adad803e3194c97)): ?>
<?php $component = $__componentOriginal4af424b90c762a172adad803e3194c97; ?>
<?php unset($__componentOriginal4af424b90c762a172adad803e3194c97); ?>
<?php endif; ?>
                                        </button>
                                        <div x-show="open" x-transition
                                            class="absolute z-10 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#EFEFEF] py-1">
                                            <button wire:click="updateStatus(<?php echo e($appointment->id); ?>, 'pending')"
                                                class="w-full text-left px-4 py-2 text-sm hover:bg-[#F7E9E6] transition-colors">Pending</button>
                                            <button wire:click="updateStatus(<?php echo e($appointment->id); ?>, 'confirmed')"
                                                class="w-full text-left px-4 py-2 text-sm hover:bg-[#F7E9E6] transition-colors">Confirmed</button>
                                            <button wire:click="updateStatus(<?php echo e($appointment->id); ?>, 'completed')"
                                                class="w-full text-left px-4 py-2 text-sm hover:bg-[#F7E9E6] transition-colors">Completed</button>
                                            <button wire:click="updateStatus(<?php echo e($appointment->id); ?>, 'cancelled')"
                                                class="w-full text-left px-4 py-2 text-sm hover:bg-[#F7E9E6] transition-colors">Cancelled</button>
                                            <button wire:click="updateStatus(<?php echo e($appointment->id); ?>, 'no_show')"
                                                class="w-full text-left px-4 py-2 text-sm hover:bg-[#F7E9E6] transition-colors">No
                                                Show</button>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center gap-2">
                                        <button onclick="showViewModal(<?php echo e($appointment->id); ?>)"
                                            class="p-1.5 text-[#E98CA5] hover:bg-[#F7E9E6] rounded-lg transition-colors"
                                            title="View">
                                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'eye','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'eye','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                        </button>
                                        <button onclick="showEditModal(<?php echo e($appointment->id); ?>)"
                                            class="p-1.5 text-[#4A4A52] hover:bg-[#F7E9E6] rounded-lg transition-colors"
                                            title="Edit">
                                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'pencil','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'pencil','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                        </button>
                                        <button wire:click="deleteAppointment(<?php echo e($appointment->id); ?>)"
                                            wire:confirm="Are you sure you want to cancel this appointment?"
                                            class="p-1.5 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                            title="Cancel">
                                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-4 h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'calendar','class' => 'w-12 h-12 text-[#8B5D66]/30 mb-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'calendar','class' => 'w-12 h-12 text-[#8B5D66]/30 mb-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                        <p class="text-[#2C2C34] font-semibold mb-1">No appointments found</p>
                                        <p class="text-[#8B5D66] text-sm">
                                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php if($search || $status !== 'all' || $date): ?>
                                                Try adjusting your filters or
                                                <button wire:click="clearFilters" class="text-[#E98CA5] hover:underline">clear
                                                    filters</button>
                                            <?php else: ?>
                                                Create your first appointment to get started
                                            <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </tbody>
                </table>
            </div>

            
            <div class="px-6 py-4 border-t border-[#EFEFEF] flex items-center justify-between">
                <div class="text-sm text-[#8B5D66]">
                    Showing <span class="font-semibold text-[#2C2C34]"><?php echo e($appointments->firstItem() ?? 0); ?></span> to
                    <span class="font-semibold text-[#2C2C34]"><?php echo e($appointments->lastItem() ?? 0); ?></span> of <span
                        class="font-semibold text-[#2C2C34]"><?php echo e($appointments->total()); ?></span> appointments
                </div>
                <div>
                    <?php echo e($appointments->links()); ?>

                </div>
            </div>
        </div>
    </div>

    
    <div id="create-modal" class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none; opacity: 0; transition: opacity 0.3s ease;" wire:ignore.self>
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" onclick="hideCreateModal()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
                style="transform: translateY(20px); transition: transform 0.3s ease;">
                <form wire:submit.prevent="createAppointment">
                    <div class="bg-gradient-to-r from-[#E98CA5] to-[#F7B5C8] px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'plus','class' => 'w-6 h-6 text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'plus','class' => 'w-6 h-6 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Create New Appointment</h3>
                                    <p class="text-sm text-white/80">Schedule a new appointment for a client</p>
                                </div>
                            </div>
                            <button type="button" onclick="hideCreateModal()"
                                class="text-white/80 hover:text-white transition-colors">
                                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-6 h-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Client <span
                                    class="text-red-500">*</span></label>
                            <select wire:model="client_id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">Select a client</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($client->id); ?>"><?php echo e($client->name); ?> (<?php echo e($client->email); ?>)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Service <span
                                    class="text-red-500">*</span></label>
                            <select wire:model="service_id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">Select a service</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($service->id); ?>"><?php echo e($service->name); ?> -
                                        <?php echo e($service->formatted_price); ?> (<?php echo e($service->duration); ?> min)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['service_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Staff Member</label>
                            <select wire:model="staff_id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">No preference</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#2C2C34] mb-2">Date <span
                                        class="text-red-500">*</span></label>
                                <input type="date" wire:model="appointment_date" min="<?php echo e(date('Y-m-d')); ?>"
                                    class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]" />
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['appointment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#2C2C34] mb-2">Time <span
                                        class="text-red-500">*</span></label>
                                <input type="time" wire:model="start_time"
                                    class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]" />
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Notes</label>
                            <textarea wire:model="notes" rows="3" placeholder="Add any special requests or notes..."
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] resize-none"></textarea>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
                        <button type="button" onclick="hideCreateModal()"
                            class="px-4 py-2.5 border border-[#EFEFEF] rounded-lg text-[#4A4A52] hover:bg-[#F7E9E6] transition-colors">Cancel</button>
                        <button type="submit"
                            class="px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-white rounded-lg hover:shadow-md transition-all">Create
                            Appointment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
    <div id="view-modal" class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none; opacity: 0; transition: opacity 0.3s ease;" wire:ignore.self>
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" onclick="hideViewModal()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full"
                style="transform: translateY(20px); transition: transform 0.3s ease;">
                <div class="bg-gradient-to-r from-[#8B5D66] to-[#A87584] px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'eye','class' => 'w-6 h-6 text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'eye','class' => 'w-6 h-6 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white">Appointment Details</h3>
                                <p class="text-sm text-white/80" id="view-reference">Reference #</p>
                            </div>
                        </div>
                        <button type="button" onclick="hideViewModal()"
                            class="text-white/80 hover:text-white transition-colors">
                            <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-6 h-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                        </button>
                    </div>
                </div>
                <div class="px-6 py-6" id="view-content">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="bg-gray-50 px-6 py-4 flex justify-between">
                    <button type="button" onclick="hideViewModal()"
                        class="px-4 py-2.5 border border-[#EFEFEF] rounded-lg text-[#4A4A52] hover:bg-[#F7E9E6] transition-colors">Close</button>
                    <div class="flex gap-2">
                        <button type="button" onclick="hideViewModal(); showEditModalFromView();"
                            class="px-4 py-2.5 border border-[#E98CA5] text-[#E98CA5] rounded-lg hover:bg-[#F7E9E6] transition-colors">Edit</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div id="edit-modal" class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none; opacity: 0; transition: opacity 0.3s ease;" wire:ignore>
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" onclick="hideEditModal()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
                style="transform: translateY(20px); transition: transform 0.3s ease;">
                <form id="edit-form" onsubmit="return submitEditForm(event)">
                    <div class="bg-gradient-to-r from-[#4A4A52] to-[#6B6B76] px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                                    <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'pencil','class' => 'w-6 h-6 text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'pencil','class' => 'w-6 h-6 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Edit Appointment</h3>
                                    <p class="text-sm text-white/80">Update appointment details</p>
                                </div>
                            </div>
                            <button type="button" onclick="hideEditModal()"
                                class="text-white/80 hover:text-white transition-colors">
                                <?php if (isset($component)) { $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::icon.index','data' => ['icon' => 'x-mark','class' => 'w-6 h-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'x-mark','class' => 'w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $attributes = $__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__attributesOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2)): ?>
<?php $component = $__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2; ?>
<?php unset($__componentOriginalc7d5f44bf2a2d803ed0b55f72f1f82e2); ?>
<?php endif; ?>
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-6 space-y-4">
                        <input type="hidden" id="edit-appointment-id" />
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Client <span
                                    class="text-red-500">*</span></label>
                            <select id="edit-client-id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">Select a client</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($client->id); ?>"><?php echo e($client->name); ?> (<?php echo e($client->email); ?>)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Service <span
                                    class="text-red-500">*</span></label>
                            <select id="edit-service-id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">Select a service</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($service->id); ?>"><?php echo e($service->name); ?> -
                                        <?php echo e($service->formatted_price); ?> (<?php echo e($service->duration); ?> min)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                            <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['service_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Staff Member</label>
                            <select id="edit-staff-id"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="">No preference</option>
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-[#2C2C34] mb-2">Date <span
                                        class="text-red-500">*</span></label>
                                <input type="date" id="edit-date"
                                    class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]" />
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['appointment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-[#2C2C34] mb-2">Time <span
                                        class="text-red-500">*</span></label>
                                <input type="time" id="edit-time"
                                    class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]" />
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Status <span
                                    class="text-red-500">*</span></label>
                            <select id="edit-status"
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5]">
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="no_show">No Show</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[#2C2C34] mb-2">Notes</label>
                            <textarea id="edit-notes" rows="3"
                                placeholder="Add any special requests or notes..."
                                class="w-full px-4 py-2.5 bg-white border border-[#EFEFEF] rounded-lg text-[#2C2C34] placeholder-[#8B5D66] focus:outline-none focus:ring-2 focus:ring-[#E98CA5] focus:border-[#E98CA5] resize-none"></textarea>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
                        <button type="button" onclick="hideEditModal()"
                            class="px-4 py-2.5 border border-[#EFEFEF] rounded-lg text-[#4A4A52] hover:bg-[#F7E9E6] transition-colors">Cancel</button>
                        <button type="submit"
                            class="px-4 py-2.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] text-white rounded-lg hover:shadow-md transition-all">Update
                            Appointment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    let currentAppointmentId = null;
    let appointmentsData = <?php echo json_encode($appointments->items(), 15, 512) ?>;

    function showCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideCreateModal() {
        const modal = document.getElementById('create-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function showViewModal(id) {
        currentAppointmentId = id;
        const appointment = appointmentsData.find(a => a.id === id);
        if (!appointment) return;

        document.getElementById('view-reference').textContent = `Reference #${id}`;
        document.getElementById('view-content').innerHTML = generateViewContent(appointment);

        const modal = document.getElementById('view-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);
    }

    function hideViewModal() {
        const modal = document.getElementById('view-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function showEditModal(id) {
        currentAppointmentId = id;
        const appointment = appointmentsData.find(a => a.id === id);
        if (!appointment) return;

        // Show the modal first
        const modal = document.getElementById('edit-modal');
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.inline-block').style.transform = 'translateY(0)';
        }, 10);

        // Populate form fields directly via JavaScript AND Livewire
        // This ensures immediate visual feedback
        document.getElementById('edit-client-id').value = appointment.client_id;
        document.getElementById('edit-service-id').value = appointment.service_id;
        document.getElementById('edit-staff-id').value = appointment.staff_id || '';

        // Format date properly for HTML5 date input (YYYY-MM-DD)
        const dateValue = formatDateForInput(appointment.appointment_date);
        document.getElementById('edit-date').value = dateValue;

        document.getElementById('edit-time').value = appointment.start_time.substring(0, 5);
        document.getElementById('edit-status').value = appointment.status;
        document.getElementById('edit-notes').value = appointment.notes || '';

        // Also update Livewire properties to keep them in sync
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_id', id);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('client_id', appointment.client_id);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('service_id', appointment.service_id);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('staff_id', appointment.staff_id);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_date', dateValue);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('start_time', appointment.start_time.substring(0, 5));
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_status', appointment.status);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('notes', appointment.notes || '');
    }

    function showEditModalFromView() {
        if (currentAppointmentId) {
            showEditModal(currentAppointmentId);
        }
    }

    function hideEditModal() {
        const modal = document.getElementById('edit-modal');
        modal.style.opacity = '0';
        modal.querySelector('.inline-block').style.transform = 'translateY(20px)';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    function submitEditForm(event) {
        event.preventDefault();

        // Get all form values
        const formData = {
            appointment_id: currentAppointmentId,
            client_id: document.getElementById('edit-client-id').value,
            service_id: document.getElementById('edit-service-id').value,
            staff_id: document.getElementById('edit-staff-id').value,
            appointment_date: document.getElementById('edit-date').value,
            start_time: document.getElementById('edit-time').value,
            appointment_status: document.getElementById('edit-status').value,
            notes: document.getElementById('edit-notes').value
        };

        // Sync all form values to Livewire using a single batch update
        Promise.all([
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_id', formData.appointment_id),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('client_id', formData.client_id),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('service_id', formData.service_id),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('staff_id', formData.staff_id),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_date', formData.appointment_date),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('start_time', formData.start_time),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('appointment_status', formData.appointment_status),
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('notes', formData.notes)
        ]).then(() => {
            // Call the update method after all values are synced
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('updateAppointment');
        });

        return false;
    }

    function generateViewContent(appointment) {
        const statusColors = {
            'pending': 'text-[#FA2964] bg-pink-50 border-pink-200',
            'confirmed': 'text-emerald-700 bg-emerald-50 border-emerald-200',
            'completed': 'text-emerald-600 bg-emerald-50 border-emerald-200',
            'cancelled': 'text-red-600 bg-red-50 border-red-200',
            'no_show': 'text-[#8B5D66] bg-[#F7E9E6] border-[#8B5D66]/20'
        };

        const statusText = {
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'completed': 'Completed',
            'cancelled': 'Cancelled',
            'no_show': 'No Show'
        };

        return `
            <div class="mb-6">
                <span class="inline-flex items-center font-medium border rounded-full px-2.5 py-1 text-xs ${statusColors[appointment.status]}">
                    ${statusText[appointment.status]}
                </span>
            </div>
            
            <div class="bg-[#F7E9E6]/30 rounded-xl p-6 mb-6">
                <h4 class="text-sm font-semibold text-[#2C2C34] uppercase tracking-wider mb-4">Client Information</h4>
                <div class="flex items-center gap-3">
                    <div class="w-12 h-12 rounded-full bg-[#E98CA5]/10 flex items-center justify-center text-[#E98CA5] font-semibold">
                        ${appointment.client.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                    </div>
                    <div>
                        <div class="font-semibold text-[#2C2C34]">${appointment.client.name}</div>
                        <div class="text-sm text-[#8B5D66]">${appointment.client.email}</div>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border border-[#EFEFEF] rounded-xl p-4">
                    <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Service</h5>
                    <div class="space-y-2">
                        <div class="font-semibold text-[#2C2C34]">${appointment.service.name}</div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-[#8B5D66]">Duration</span>
                            <span class="font-medium text-[#2C2C34]">${appointment.duration} minutes</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-[#8B5D66]">Price</span>
                            <span class="font-semibold text-[#E98CA5]">${formatCurrency(appointment.total_amount)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-[#EFEFEF] rounded-xl p-4">
                    <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Schedule</h5>
                    <div class="space-y-2">
                        <div class="text-sm text-[#2C2C34]">${formatDate(appointment.appointment_date)}</div>
                        <div class="font-medium text-[#2C2C34]">${formatTime(appointment.start_time)} - ${formatTime(appointment.end_time)}</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white border border-[#EFEFEF] rounded-xl p-4 mb-6">
                <h5 class="text-xs font-semibold text-[#8B5D66] uppercase tracking-wider mb-3">Staff Member</h5>
                <div class="text-sm text-[#2C2C34]">${appointment.staff ? appointment.staff.name : 'Unassigned'}</div>
            </div>
            
            ${appointment.notes ? `
                <div class="bg-amber-50 border border-amber-200 rounded-xl p-4">
                    <h5 class="text-xs font-semibold text-amber-900 uppercase tracking-wider mb-3">Notes</h5>
                    <p class="text-sm text-amber-900">${appointment.notes}</p>
                </div>
            ` : ''}
        `;
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    }

    function formatDateForInput(dateStr) {
        // Convert date to YYYY-MM-DD format for HTML5 date input
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function formatCurrency(amount) {
        const numeric = Number(amount) || 0;
        return numeric.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }) + ' TSH';
    }

    function formatTime(timeStr) {
        const [hours, minutes] = timeStr.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    }

    // Close modal after appointment created/updated
    document.addEventListener('livewire:init', () => {
        Livewire.on('appointment-created', () => {
            hideCreateModal();
        });

        Livewire.on('appointment-updated', () => {
            hideEditModal();
        });
    });
</script><?php /**PATH C:\laragon\www\Instyle\resources\views/livewire/manager/appointments/index.blade.php ENDPATH**/ ?>