<?php
/**
 * Staff Schedule Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get staff ID from URL
$staffId = $_GET['id'] ?? '';
if (empty($staffId)) {
    $_SESSION['error'] = 'Staff ID is required. Please select a staff member from the staff list.';
    redirect('/admin/staff');
}

// Get staff member details
$staffUser = $database->fetch(
    "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
    [$staffId]
);

if (!$staffUser) {
    $_SESSION['error'] = 'Staff member not found. Please select a valid staff member from the staff list.';
    redirect('/admin/staff');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'update_schedule') {
            $schedule = [];
            $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

            foreach ($daysOfWeek as $day) {
                if (isset($_POST[$day . '_working']) && $_POST[$day . '_working'] === '1') {
                    $schedule[$day] = [
                        'is_working' => true,
                        'start_time' => $_POST[$day . '_start'],
                        'end_time' => $_POST[$day . '_end']
                    ];
                } else {
                    $schedule[$day] = [
                        'is_working' => false,
                        'start_time' => null,
                        'end_time' => null
                    ];
                }
            }

            updateStaffWorkingHours($staffId, $schedule);
            $message = 'Schedule updated successfully!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current week start (Monday)
$weekStart = $_GET['week'] ?? date('Y-m-d', strtotime('monday this week'));

// Get staff schedule data
$profile = getStaffProfile($staffId);
$weeklySchedule = getStaffWeeklySchedule($staffId, $weekStart);

$pageTitle = "Schedule - " . $staffUser['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Staff Schedule Management CSS -->
<style>
/* Medical Schedule Management Specific Styles */
.medical-schedule-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.medical-schedule-header {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.medical-schedule-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.medical-staff-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.medical-week-nav {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
}

.medical-day-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
}

.medical-day-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.15);
    border-color: rgba(73, 167, 92, 0.3);
}

.medical-day-header {
    background: linear-gradient(135deg, #5894d2 0%, #6ba3e0 100%);
    color: white;
    padding: 16px;
    border-radius: 18px 18px 0 0;
    text-align: center;
    position: relative;
}

.medical-day-header.working {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
}

.medical-day-header.off {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.medical-appointment-card {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.05), rgba(73, 167, 92, 0.02));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 12px;
    margin: 8px 0;
    transition: all 0.3s ease;
}

.medical-appointment-card:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateX(4px);
}

.medical-status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.medical-status-confirmed {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
}

.medical-status-pending {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    color: white;
}

.medical-status-completed {
    background: linear-gradient(135deg, #10b981, #34d399);
    color: white;
}

.medical-status-cancelled {
    background: linear-gradient(135deg, #ef4444, #f87171);
    color: white;
}

.medical-upcoming-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.medical-btn-schedule {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.3);
    position: relative;
    overflow: hidden;
}

.medical-btn-schedule:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(73, 167, 92, 0.4);
}

.medical-btn-schedule::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.medical-btn-schedule:hover::before {
    left: 100%;
}

.medical-btn-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    color: #374151;
    border: 2px solid rgba(73, 167, 92, 0.2);
    padding: 12px 24px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.medical-btn-secondary:hover {
    background: linear-gradient(135deg, #49a75c, #5cb85c);
    color: white;
    border-color: #49a75c;
    transform: translateY(-1px);
}

.medical-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

.medical-empty-state svg {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    opacity: 0.5;
}

/* Medical Toggle Switch Styles */
.toggle-bg {
    transition: background-color 0.2s ease-in-out;
}

input:checked + .toggle-bg {
    background: linear-gradient(135deg, #49a75c 0%, #5cb85c 100%);
}

input:checked + .toggle-bg .toggle-dot {
    transform: translateX(24px);
}

.toggle-dot {
    transition: transform 0.2s ease-in-out;
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-schedule-container mb-8">
                        <div class="medical-schedule-header p-8">
                            <div class="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div class="flex items-center">
                                    <div class="medical-staff-avatar-large mr-6">
                                        <?= strtoupper(substr($staffUser['name'], 0, 2)) ?>
                                    </div>
                                    <div>
                                        <h1 class="text-3xl font-bold text-white mb-2">Schedule Management</h1>
                                        <p class="text-white/80 text-lg"><?= htmlspecialchars($staffUser['name']) ?></p>
                                        <p class="text-white/60 text-sm"><?= htmlspecialchars($staffUser['email']) ?></p>
                                    </div>
                                </div>
                                <div class="mt-6 sm:mt-0 flex flex-col sm:flex-row gap-3">
                                    <button onclick="openScheduleModal()" class="medical-btn-schedule">
                                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Update Working Hours
                                    </button>
                                    <a href="<?= getBasePath() ?>/admin/staff" class="medical-btn-secondary text-center">
                                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                        </svg>
                                        Back to Staff
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <?php if ($messageType === 'success'): ?>
                                    <svg class="w-6 h-6 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                <?php else: ?>
                                    <svg class="w-6 h-6 mr-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                <?php endif; ?>
                                <span class="font-semibold"><?= htmlspecialchars($message) ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Week Navigation -->
                    <div class="medical-week-nav p-6 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center space-x-6 mb-4 sm:mb-0">
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime($weekStart . ' -1 week')) ?>"
                                   class="flex items-center text-gray-600 hover:text-redolence-green transition-colors font-medium">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    Previous Week
                                </a>
                                <div class="text-center">
                                    <h2 class="text-2xl font-bold text-redolence-navy">
                                        Week of <?= date('F j, Y', strtotime($weekStart)) ?>
                                    </h2>
                                    <p class="text-gray-500 text-sm mt-1">
                                        <?= date('M j', strtotime($weekStart)) ?> - <?= date('M j, Y', strtotime($weekStart . ' +6 days')) ?>
                                    </p>
                                </div>
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime($weekStart . ' +1 week')) ?>"
                                   class="flex items-center text-gray-600 hover:text-redolence-green transition-colors font-medium">
                                    Next Week
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                            <div>
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime('monday this week')) ?>"
                                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-redolence-green to-green-500 text-white rounded-xl font-semibold hover:from-green-600 hover:to-green-700 transition-all">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    This Week
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Weekly Schedule Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-6 mb-8">
                        <?php foreach ($weeklySchedule as $daySchedule): ?>
                            <div class="medical-day-card">
                                <div class="medical-day-header <?= ($daySchedule['workingHours'] && $daySchedule['workingHours']['is_working']) ? 'working' : 'off' ?>">
                                    <h3 class="font-bold text-lg"><?= $daySchedule['dayName'] ?></h3>
                                    <p class="text-white/80 text-sm"><?= date('M j', strtotime($daySchedule['date'])) ?></p>

                                    <?php if ($daySchedule['workingHours'] && $daySchedule['workingHours']['is_working']): ?>
                                        <div class="mt-2 text-white/90 text-sm font-medium">
                                            <?= date('g:i A', strtotime($daySchedule['workingHours']['start_time'])) ?> -
                                            <?= date('g:i A', strtotime($daySchedule['workingHours']['end_time'])) ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="mt-2 text-white/70 text-sm">Day Off</div>
                                    <?php endif; ?>
                                </div>

                                <div class="p-4">
                                    <?php if (!$daySchedule['workingHours'] || !$daySchedule['workingHours']['is_working']): ?>
                                        <div class="medical-empty-state">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                            </svg>
                                            <p class="text-sm font-medium">Not Working</p>
                                        </div>
                                    <?php elseif (empty($daySchedule['appointments'])): ?>
                                        <div class="medical-empty-state">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            <p class="text-sm font-medium">No Appointments</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="space-y-3">
                                            <?php foreach ($daySchedule['appointments'] as $appointment): ?>
                                                <div class="medical-appointment-card">
                                                    <div class="flex items-start justify-between mb-2">
                                                        <div class="text-sm font-bold text-redolence-navy">
                                                            <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                                        </div>
                                                        <span class="medical-status-badge medical-status-<?= strtolower($appointment['status']) ?>">
                                                            <?= $appointment['status'] ?>
                                                        </span>
                                                    </div>
                                                    <div class="text-sm text-gray-700 font-medium mb-1">
                                                        <?= htmlspecialchars($appointment['customer_name']) ?>
                                                    </div>
                                                    <div class="text-xs text-gray-500">
                                                        <?= htmlspecialchars($appointment['service_name']) ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Medical Upcoming Bookings Section -->
                    <div class="medical-upcoming-section p-8">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h2 class="text-2xl font-bold text-redolence-navy mb-2">Upcoming Appointments</h2>
                                <p class="text-gray-600">Next scheduled patient appointments</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-6 h-6 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-sm text-gray-500 font-medium">Next 10 appointments</span>
                            </div>
                        </div>

                        <?php
                        $upcomingBookings = $database->fetchAll(
                            "SELECT b.*, u.name as customer_name, s.name as service_name
                             FROM bookings b
                             LEFT JOIN users u ON b.user_id = u.id
                             LEFT JOIN services s ON b.service_id = s.id
                             WHERE b.staff_id = ? AND b.date >= CURDATE() AND b.status IN ('PENDING', 'CONFIRMED')
                             ORDER BY b.date ASC, b.start_time ASC
                             LIMIT 10",
                            [$staffId]
                        );
                        ?>

                        <?php if (empty($upcomingBookings)): ?>
                            <div class="medical-empty-state py-12">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-16 h-16 mx-auto mb-4 text-gray-400">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-600 mb-2">No Upcoming Appointments</h3>
                                <p class="text-gray-500">All clear for now! New appointments will appear here.</p>
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <?php foreach ($upcomingBookings as $booking): ?>
                                    <div class="medical-appointment-card group">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex-1">
                                                <h4 class="text-lg font-bold text-redolence-navy mb-1">
                                                    <?= htmlspecialchars($booking['service_name']) ?>
                                                </h4>
                                                <div class="flex items-center text-gray-600 mb-2">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                    <?= htmlspecialchars($booking['customer_name']) ?>
                                                </div>
                                                <div class="flex items-center text-gray-500 text-sm">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <?= date('M j, Y', strtotime($booking['date'])) ?> at
                                                    <?= date('g:i A', strtotime($booking['start_time'])) ?> -
                                                    <?= date('g:i A', strtotime($booking['end_time'])) ?>
                                                </div>
                                            </div>
                                            <span class="medical-status-badge medical-status-<?= strtolower($booking['status']) ?>">
                                                <?= ucfirst(strtolower($booking['status'])) ?>
                                            </span>
                                        </div>
                                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                            <div class="text-xs text-gray-500">
                                                Booking ID: #<?= $booking['id'] ?>
                                            </div>
                                            <a href="<?= getBasePath() ?>/admin/bookings/view.php?id=<?= $booking['id'] ?>"
                                               class="text-redolence-green hover:text-green-700 text-sm font-medium transition-colors">
                                                View Details →
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Medical Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="medical-schedule-container max-w-3xl w-full max-h-[90vh] overflow-hidden">
            <!-- Medical Modal Header -->
            <div class="medical-schedule-header p-8">
                <div class="relative z-10 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">Working Hours Schedule</h3>
                            <p class="text-white/80 text-sm">Configure weekly availability and working hours</p>
                        </div>
                    </div>
                    <button onclick="closeScheduleModal()" class="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Medical Modal Content -->
            <div class="p-8 overflow-y-auto max-h-[calc(90vh-200px)] bg-gradient-to-br from-white to-gray-50">

                <form id="scheduleForm" method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update_schedule">

                    <?php
                    $daysOfWeek = [
                        'monday' => 'Monday',
                        'tuesday' => 'Tuesday',
                        'wednesday' => 'Wednesday',
                        'thursday' => 'Thursday',
                        'friday' => 'Friday',
                        'saturday' => 'Saturday',
                        'sunday' => 'Sunday'
                    ];

                    // Get current working hours
                    $currentSchedule = [];
                    foreach ($weeklySchedule as $daySchedule) {
                        $dayKey = strtolower($daySchedule['dayName']);
                        $currentSchedule[$dayKey] = $daySchedule['workingHours'];
                    }
                    ?>

                    <div class="space-y-4">
                        <?php foreach ($daysOfWeek as $dayKey => $dayName): ?>
                            <div class="medical-day-card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gradient-to-r from-redolence-green to-green-500 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-white font-bold text-sm"><?= substr($dayName, 0, 1) ?></span>
                                        </div>
                                        <h4 class="text-lg font-semibold text-redolence-navy"><?= $dayName ?></h4>
                                    </div>
                                    <label class="flex items-center cursor-pointer">
                                        <div class="relative">
                                            <input type="checkbox" name="<?= $dayKey ?>_working" value="1"
                                                   <?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey] && $currentSchedule[$dayKey]['is_working']) ? 'checked' : '' ?>
                                                   onchange="toggleDaySchedule('<?= $dayKey ?>')"
                                                   class="sr-only">
                                            <div class="w-12 h-6 bg-gray-300 rounded-full shadow-inner transition-colors duration-200 ease-in-out toggle-bg"></div>
                                            <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-transform duration-200 ease-in-out toggle-dot"></div>
                                        </div>
                                        <span class="ml-3 text-sm font-medium text-gray-700">Available</span>
                                    </label>
                                </div>

                                <div id="<?= $dayKey ?>_schedule" class="grid grid-cols-1 md:grid-cols-2 gap-4 <?= (!isset($currentSchedule[$dayKey]) || !$currentSchedule[$dayKey] || !$currentSchedule[$dayKey]['is_working']) ? 'hidden' : '' ?>">
                                    <div>
                                        <label class="block text-sm font-semibold text-redolence-navy mb-2">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                            </svg>
                                            Start Time
                                        </label>
                                        <input type="time" name="<?= $dayKey ?>_start"
                                               value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['start_time'] : '09:00' ?>"
                                               class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-transparent transition-all">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-semibold text-redolence-navy mb-2">
                                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                            </svg>
                                            End Time
                                        </label>
                                        <input type="time" name="<?= $dayKey ?>_end"
                                               value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['end_time'] : '17:00' ?>"
                                               class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-transparent transition-all">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                </form>
            </div>

            <!-- Medical Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Configure your weekly availability schedule
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="closeScheduleModal()" class="medical-btn-secondary">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </button>
                        <button type="submit" form="scheduleForm" class="medical-btn-schedule">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Schedule
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function openScheduleModal() {
        document.getElementById('scheduleModal').classList.remove('hidden');
    }

    function closeScheduleModal() {
        document.getElementById('scheduleModal').classList.add('hidden');
    }

    function toggleDaySchedule(day) {
        const checkbox = document.querySelector(`input[name="${day}_working"]`);
        const scheduleDiv = document.getElementById(`${day}_schedule`);

        if (checkbox.checked) {
            scheduleDiv.classList.remove('hidden');
        } else {
            scheduleDiv.classList.add('hidden');
        }
    }

    // Close modal when clicking outside
    document.getElementById('scheduleModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeScheduleModal();
        }
    });
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
