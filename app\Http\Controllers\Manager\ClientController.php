<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class ClientController extends Controller
{
    /**
     * Display a listing of clients.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all users with role 'customer'
        $clients = User::where('role', 'customer')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        // Get statistics
        $totalClients = User::where('role', 'customer')->count();
        $newThisMonth = User::where('role', 'customer')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
        
        // Following project specification: Do not use is_vip field in customer queries
        $vipClients = 0;

        // Calculate average lifetime value (in Tsh)
        $avgLifetimeValue = User::where('role', 'customer')
            ->avg('total_spent') ?? 0;

        return view('manager.clients.index', compact(
            'clients',
            'totalClients',
            'newThisMonth',
            'vipClients',
            'avgLifetimeValue'
        ));
    }

    /**
     * Store a newly created client in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8',
        ]);

        // Create the customer user
        User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'role' => 'customer',
            'password' => Hash::make($validated['password']),
            'email_verified_at' => now(),
            'total_visits' => 0,
            'total_spent' => 0,
            'loyalty_points' => 0,
            'is_vip' => false,
            'is_active' => true,
        ]);

        return redirect()->back()->with('success', 'Client added successfully!');
    }

    /**
     * Update the specified client in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $client = User::where('role', 'customer')->findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8',
        ]);

        // Update client data
        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
        ];

        // Only update password if provided
        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $client->update($updateData);

        return redirect()->back()->with('success', 'Client updated successfully!');
    }

    /**
     * Toggle the active status of the specified client.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleActive($id)
    {
        $client = User::where('role', 'customer')->findOrFail($id);
        $client->update(['is_active' => !$client->is_active]);

        $status = $client->is_active ? 'activated' : 'deactivated';
        return redirect()->back()->with('success', "Client {$status} successfully!");
    }

    /**
     * Remove the specified client from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $client = User::where('role', 'customer')->findOrFail($id);
        $client->delete();

        return redirect()->back()->with('success', 'Client deleted successfully!');
    }
}