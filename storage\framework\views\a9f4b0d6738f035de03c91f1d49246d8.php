<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>InStyle Hair & Beauty Salon - Luxury Beauty Experience</title>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="antialiased bg-white">
    
    <header class="fixed left-1/2 transform -translate-x-1/2 z-50 w-[95%] max-w-6xl transition-all duration-500" x-data="{ 
                scrolled: false, 
                mobileMenuOpen: false,
                lastScroll: 0,
                hidden: false,
                init() {
                    window.addEventListener('scroll', () => {
                        const currentScroll = window.pageYOffset;
                        this.scrolled = currentScroll > 50;
                        
                        if (currentScroll <= 0) {
                            this.hidden = false;
                        } else if (currentScroll > this.lastScroll && currentScroll > 100) {
                            // Scrolling down & past 100px
                            this.hidden = true;
                            this.mobileMenuOpen = false;
                        } else if (currentScroll < this.lastScroll) {
                            // Scrolling up
                            this.hidden = false;
                        }
                        
                        this.lastScroll = currentScroll;
                    });
                }
            }" :class="hidden ? '-top-32' : 'top-6'">
        
        <div class="backdrop-blur-xl bg-white/90 rounded-full shadow-2xl border border-white/20 transition-all duration-300"
            :class="scrolled ? 'shadow-2xl bg-white/95' : 'shadow-xl'">
            <div class="px-6 lg:px-8 h-20 flex items-center justify-between">
                
                <a href="#" class="flex items-center gap-3 group">
                    <div
                        class="w-10 h-10 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#C85E78] flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                    </div>
                    <span
                        class="font-serif text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#E98CA5] to-[#C85E78]">InStyle</span>
                </a>

                
                <nav class="hidden lg:flex items-center gap-8">
                    <a href="#"
                        class="font-body font-medium text-[#2C2C34] hover:text-[#C85E78] transition-colors relative group">
                        Home
                        <span
                            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] group-hover:w-full transition-all duration-300"></span>
                    </a>
                    <a href="#services"
                        class="font-body font-medium text-[#2C2C34] hover:text-[#C85E78] transition-colors relative group">
                        Services
                        <span
                            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] group-hover:w-full transition-all duration-300"></span>
                    </a>
                    <a href="#gallery"
                        class="font-body font-medium text-[#2C2C34] hover:text-[#C85E78] transition-colors relative group">
                        Gallery
                        <span
                            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] group-hover:w-full transition-all duration-300"></span>
                    </a>
                    <a href="#about"
                        class="font-body font-medium text-[#2C2C34] hover:text-[#C85E78] transition-colors relative group">
                        About
                        <span
                            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] group-hover:w-full transition-all duration-300"></span>
                    </a>
                    <a href="#contact"
                        class="font-body font-medium text-[#2C2C34] hover:text-[#C85E78] transition-colors relative group">
                        Reach Us
                        <span
                            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#E98CA5] to-[#C85E78] group-hover:w-full transition-all duration-300"></span>
                    </a>
                </nav>

                
                <a href="#services"
                    class="hidden lg:inline-flex items-center px-6 py-3 text-white font-body font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    style="background: linear-gradient(135deg, #E98CA5 0%, #C85E78 100%);">
                    Book Now
                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </a>

                
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                    class="lg:hidden p-2 text-[#2C2C34] hover:text-[#C85E78] transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                        <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        
        <div x-show="mobileMenuOpen" x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 -translate-y-2" x-transition:enter-end="opacity-100 translate-y-0"
            x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0"
            x-transition:leave-end="opacity-0 -translate-y-2" @click.away="mobileMenuOpen = false"
            class="lg:hidden mt-4 backdrop-blur-xl bg-white/95 rounded-3xl shadow-2xl border border-white/20 p-6">
            <nav class="space-y-3">
                <a href="#" @click="mobileMenuOpen = false"
                    class="block font-body font-medium text-[#2C2C34] hover:text-[#C85E78] py-3 px-4 rounded-xl hover:bg-[#F7E9E6]/50 transition-colors">Home</a>
                <a href="#services" @click="mobileMenuOpen = false"
                    class="block font-body font-medium text-[#2C2C34] hover:text-[#C85E78] py-3 px-4 rounded-xl hover:bg-[#F7E9E6]/50 transition-colors">Services</a>
                <a href="#gallery" @click="mobileMenuOpen = false"
                    class="block font-body font-medium text-[#2C2C34] hover:text-[#C85E78] py-3 px-4 rounded-xl hover:bg-[#F7E9E6]/50 transition-colors">Gallery</a>
                <a href="#about" @click="mobileMenuOpen = false"
                    class="block font-body font-medium text-[#2C2C34] hover:text-[#C85E78] py-3 px-4 rounded-xl hover:bg-[#F7E9E6]/50 transition-colors">About</a>
                <a href="#contact" @click="mobileMenuOpen = false"
                    class="block font-body font-medium text-[#2C2C34] hover:text-[#C85E78] py-3 px-4 rounded-xl hover:bg-[#F7E9E6]/50 transition-colors">Reach
                    Us</a>
                <a href="#services" @click="mobileMenuOpen = false"
                    class="block text-center px-6 py-4 text-white font-body font-semibold rounded-full shadow-lg mt-4 transform hover:scale-105 transition-all"
                    style="background: linear-gradient(135deg, #E98CA5 0%, #C85E78 100%);">
                    Book Now
                </a>
            </nav>
        </div>
    </header>


    
    <section class="relative min-h-screen flex items-center overflow-hidden bg-white pt-32">
        

        
        <div class="hidden lg:block absolute top-0 right-0 w-[45%] h-[85%] pointer-events-none overflow-hidden z-0">
            <svg class="absolute top-0 right-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                
                <path d="M0,0 L100,0 L100,100 C100,60 80,40 50,40 C20,40 0,20 0,0 Z" fill="url(#gradientTop)"
                    opacity="0.9" />
                <defs>
                    <linearGradient id="gradientTop" x1="100%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#E98CA5; stop-opacity:0.15" />
                        <stop offset="100%" style="stop-color:#F4B6C5; stop-opacity:0.05" />
                    </linearGradient>
                </defs>
            </svg>
            
            <svg class="absolute top-0 right-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <path d="M0,0 C0,30 30,60 100,60" fill="none" stroke="url(#strokeGradientTop)" stroke-width="0.5"
                    opacity="0.4" />
                <defs>
                    <linearGradient id="strokeGradientTop" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#C85E78" />
                        <stop offset="100%" style="stop-color:#E98CA5" />
                    </linearGradient>
                </defs>
            </svg>
        </div>

        
        <div class="hidden lg:block absolute bottom-0 left-0 w-[55%] h-[60%] pointer-events-none overflow-hidden z-0">
            <svg class="absolute bottom-0 left-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <path d="M0,100 L100,100 C70,100 50,80 50,50 C50,20 20,20 0,20 Z" fill="url(#gradientBottom)"
                    opacity="0.9" />
                <defs>
                    <linearGradient id="gradientBottom" x1="0%" y1="100%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#DCC7A1; stop-opacity:0.15" />
                        <stop offset="100%" style="stop-color:#F7E9E6; stop-opacity:0.05" />
                    </linearGradient>
                </defs>
            </svg>
            
            <svg class="absolute bottom-0 left-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <path d="M0,20 C40,20 60,60 100,100" fill="none" stroke="url(#strokeGradientBottom)" stroke-width="0.5"
                    opacity="0.4" />
                <defs>
                    <linearGradient id="strokeGradientBottom" x1="0%" y1="100%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#DCC7A1" />
                        <stop offset="100%" style="stop-color:#8B5D66" />
                    </linearGradient>
                </defs>
            </svg>
        </div>

        
        <div class="relative z-10 max-w-7xl mx-auto px-6 lg:px-12 py-24 w-full">
            <div class="grid lg:grid-cols-12 gap-12 items-center">
                
                <div class="lg:col-span-7 space-y-10">
                    
                    <div class="space-y-4 text-center lg:text-left" x-data="{ show: false }"
                        x-init="setTimeout(() => show = true, 500)">
                        <h1
                            class="font-serif text-6xl md:text-7xl lg:text-8xl font-bold text-[#2C2C34] leading-[0.95] tracking-tight">
                            <span class="block transition-all duration-1000 transform"
                                :class="show ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'">
                                Discover
                            </span>
                            <span
                                class="block text-transparent bg-clip-text bg-gradient-to-r from-[#E98CA5] via-[#C85E78] to-[#8B5D66] transition-all duration-1000 delay-300 transform"
                                :class="show ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'">
                                luxury
                            </span>
                            <span class="block transition-all duration-1000 delay-500 transform"
                                :class="show ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'">
                                beauty
                            </span>
                        </h1>
                    </div>

                    
                    <p class="font-body text-xl md:text-2xl text-[#4A4A52] max-w-2xl leading-relaxed">
                        Experience premium hair, skin, and beauty treatments crafted with elegance and care —
                        <span class="relative inline-block text-[#C85E78] font-bold">
                            all in InStyle.
                            <svg class="absolute -bottom-2 left-0 w-full h-3 -z-10 opacity-60" viewBox="0 0 100 10"
                                preserveAspectRatio="none">
                                <path d="M0,5 Q50,10 100,5" stroke="#E98CA5" stroke-width="3" fill="none"
                                    stroke-linecap="round" />
                            </svg>
                        </span>
                    </p>

                    
                    
                    <div class="flex flex-row items-center gap-3 pt-6 w-full sm:w-auto">
                        <a href="#services"
                            class="group relative flex-1 sm:flex-none inline-flex items-center justify-center px-4 sm:px-8 py-3.5 bg-[#2C2C34] text-white font-body font-medium text-xs sm:text-sm tracking-wide sm:tracking-widest uppercase rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                            <span class="relative z-10 flex items-center gap-2 whitespace-nowrap">
                                Book Now
                                <svg class="w-3 h-3 sm:w-4 sm:h-4 transition-transform duration-300 group-hover:translate-x-1"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                        d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </span>
                            <div
                                class="absolute inset-0 bg-white/10 translate-y-full transition-transform duration-300 group-hover:translate-y-0">
                            </div>
                        </a>

                        <a href="#gallery"
                            class="group flex-1 sm:flex-none inline-flex items-center justify-center px-4 sm:px-8 py-3.5 bg-transparent text-[#2C2C34] font-body font-medium text-xs sm:text-sm tracking-wide sm:tracking-widest uppercase rounded-xl border border-[#2C2C34]/30 transition-all duration-300 hover:border-[#2C2C34] hover:bg-[#2C2C34]/5 whitespace-nowrap">
                            Portfolio
                        </a>
                    </div>

                    
                    <div class="flex items-center gap-8 pt-6 text-sm font-body text-[#8B5D66]">
                        <div class="flex items-center gap-2">
                            <svg class="w-5 h-5 text-[#DCC7A1]" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span class="font-semibold">5.0</span>
                            <span>Rated Excellence</span>
                        </div>
                        <div class="h-4 w-px bg-[#DCC7A1]"></div>
                        <div>
                            <span class="font-semibold">500+</span> Happy Clients
                        </div>
                    </div>
                </div>

                
                <div class="lg:col-span-5 relative" x-data="{ 
                         currentSlide: 0,
                         images: [
                             'flower-crown-woman-thinking-studio-wellness-cosmetics-organic-makeup-grey-background-eco-friendly-dermatology-calm-model-with-natural-beauty-shine-glowing-skin-aesthetic.png',
                             'beauty-young-latin-woman-with-ideal-skin.png',
                             'woman-have-fun-with-cosmetic-brushes.png',
                             'makeup-tools.png'
                         ],
                         autoplay: null,
                         init() {
                             this.autoplay = setInterval(() => {
                                 this.currentSlide = (this.currentSlide + 1) % this.images.length;
                             }, 4000);
                         }
                     }">
                    
                    <div class="relative">
                        
                        <div
                            class="absolute -inset-4 bg-gradient-to-br from-[#E98CA5]/20 to-[#F4B6C5]/20 rounded-[3rem] transform rotate-3">
                        </div>

                        
                        <div
                            class="relative rounded-[2.5rem] overflow-hidden shadow-2xl transform -rotate-2 hover:rotate-0 transition-transform duration-500">
                            
                            <template x-for="(image, index) in images" :key="index">
                                <div x-show="currentSlide === index"
                                    x-transition:enter="transition ease-out duration-700"
                                    x-transition:enter-start="opacity-0 transform scale-105"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-700"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95" class="absolute inset-0">
                                    <img :src="`<?php echo e(asset('storage/hero')); ?>/${image}`" alt="Luxury Beauty Experience"
                                        class="w-full h-full object-cover">
                                </div>
                            </template>

                            
                            <img src="<?php echo e(asset('storage/hero/flower-crown-woman-thinking-studio-wellness-cosmetics-organic-makeup-grey-background-eco-friendly-dermatology-calm-model-with-natural-beauty-shine-glowing-skin-aesthetic.png')); ?>"
                                alt="Luxury Beauty Experience" class="w-full h-auto object-cover opacity-0">

                            
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-[#2C2C34]/10 via-transparent to-transparent pointer-events-none">
                            </div>

                            
                            <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
                                <template x-for="(image, index) in images" :key="index">
                                    <button @click="currentSlide = index; clearInterval(autoplay)"
                                        class="w-2 h-2 rounded-full transition-all duration-300"
                                        :class="currentSlide === index ? 'bg-white w-8' : 'bg-white/50 hover:bg-white/75'">
                                    </button>
                                </template>
                            </div>
                        </div>

                        
                        <div
                            class="absolute -bottom-8 -left-8 bg-white rounded-2xl p-6 shadow-2xl border border-[#F7E9E6] max-w-xs hidden lg:block">
                            <div class="flex items-center gap-4">
                                <div
                                    class="w-14 h-14 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center flex-shrink-0">
                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-serif font-bold text-[#2C2C34] text-lg">Premium Care</p>
                                    <p class="font-body text-sm text-[#8B5D66]">Expert Beauty Services</p>
                                </div>
                            </div>
                        </div>

                        
                        <div class="absolute -top-6 -right-6 w-24 h-24 opacity-40">
                            <div class="grid grid-cols-4 gap-2">
                                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($i = 0; $i < 16; $i++): ?>
                                    <div class="w-2 h-2 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#C85E78]"></div>
                                <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        <div
            class="absolute bottom-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-2 animate-bounce">
            <span class="text-xs font-body text-[#8B5D66] uppercase tracking-wider">Scroll</span>
            <svg class="w-6 h-6 text-[#C85E78]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
        </div>
    </section>

    
    <section id="about" class="py-24 bg-white">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                
                <div class="order-2 lg:order-1">
                    <div class="relative">
                        <div class="rounded-3xl overflow-hidden shadow-xl">
                            <img src="<?php echo e(asset('storage/hero/beauty-young-latin-woman-with-ideal-skin.png')); ?>"
                                alt="Beauty With Grace" class="w-full h-auto object-cover">
                        </div>
                        
                        <div
                            class="absolute -bottom-4 -right-4 w-full h-full border-2 border-[#DCC7A1] rounded-3xl -z-10">
                        </div>
                    </div>
                </div>

                
                <div class="order-1 lg:order-2 space-y-6">
                    <div class="inline-block">
                        <span class="text-[#C85E78] uppercase tracking-[0.3em] text-sm font-body font-medium">About
                            Us</span>
                        <div class="h-px w-20 bg-gradient-to-r from-[#DCC7A1] to-transparent mt-2"></div>
                    </div>

                    <h2 class="font-serif text-4xl md:text-5xl font-bold text-[#2C2C34] leading-tight">
                        Beauty With <span class="text-[#C85E78]">Grace</span>
                    </h2>

                    <p class="font-body text-lg text-[#4A4A52] leading-relaxed">
                        A sanctuary of elegance designed for the modern, confident woman. Our premium hair, skin, and
                        beauty treatments are crafted with the highest level of care and expertise.
                    </p>

                    <div class="space-y-4">
                        <div class="flex items-start gap-4">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mb-1">Expert Professionals</h3>
                                <p class="font-body text-[#4A4A52]">Highly trained stylists and beauty specialists</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mb-1">Premium Products</h3>
                                <p class="font-body text-[#4A4A52]">Luxury beauty products and treatments</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-body font-semibold text-[#2C2C34] mb-1">Personalized Care</h3>
                                <p class="font-body text-[#4A4A52]">Tailored services for your unique beauty needs</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <section id="services" class="py-24 bg-gradient-to-b from-[#F7E9E6] to-white">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
            
            <div class="text-center max-w-3xl mx-auto mb-16">
                <div class="inline-block mb-4">
                    <span class="text-[#C85E78] uppercase tracking-[0.3em] text-sm font-body font-medium">Our
                        Services</span>
                    <div class="h-px w-20 bg-gradient-to-r from-[#DCC7A1] to-transparent mt-2 mx-auto"></div>
                </div>
                <h2 class="font-serif text-4xl md:text-5xl font-bold text-[#2C2C34] mb-6">
                    Crafted For Your <span class="text-[#C85E78]">Glow</span>
                </h2>
                <p class="font-body text-lg text-[#4A4A52]">
                    Experience our signature treatments designed to enhance your natural beauty
                </p>
            </div>

            <div class="grid lg:grid-cols-12 gap-12 lg:gap-16 items-start">

                
                <div class="lg:col-span-7 flex flex-col gap-6">

                    
                    <div
                        class="group relative bg-[#FA2964] rounded-xl md:rounded-[2rem] overflow-hidden shadow-lg transition-transform duration-300 hover:scale-[1.02]">
                        <!-- Mobile: Vertical Stack -->
                        <div class="md:hidden">
                            <div class="relative aspect-[4/3] overflow-hidden">
                                <img src="<?php echo e(asset('storage/hero/beauty-young-latin-woman-with-ideal-skin.png')); ?>"
                                    alt="Hair Styling" class="w-full h-full object-cover object-top">
                                <div
                                    class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#FA2964]/80">
                                </div>
                            </div>
                            <div class="p-4 text-white">
                                <h3 class="font-serif text-xl font-bold mb-1">Hair Styling</h3>
                                <p class="font-body text-pink-100 text-xs mb-3 italic">Renew your style!</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-baseline">
                                        <span class="text-sm font-bold mr-0.5">$</span>
                                        <span class="font-serif text-3xl font-bold leading-none">85</span>
                                        <span class="text-[10px] opacity-70 ml-2">/ session</span>
                                    </div>
                                    <a href="#contact"
                                        class="w-9 h-9 rounded-full border-2 border-white/30 flex items-center justify-center hover:bg-white hover:text-[#FA2964] transition-all">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Desktop: Horizontal Layout -->
                        <div class="hidden md:grid md:grid-cols-5 h-full">
                            <div class="md:col-span-3 p-8 flex flex-col justify-center text-white relative z-10">
                                <div>
                                    <h3 class="font-serif text-3xl lg:text-4xl font-bold mb-2 leading-tight">Hair
                                        Styling</h3>
                                    <p class="font-body text-pink-100 text-base mb-6 italic">Renew your style!</p>
                                    <div class="flex items-center gap-4">
                                        <div class="relative pl-5 border-l-2 border-white/30">
                                            <span
                                                class="block text-[10px] uppercase tracking-wider opacity-80 mb-0.5">Starting
                                                at</span>
                                            <div class="flex items-baseline">
                                                <span class="text-lg font-bold mr-0.5">$</span>
                                                <span class="font-serif text-5xl font-bold leading-none">85</span>
                                            </div>
                                            <span class="block text-[10px] opacity-60 mt-1">////// per session</span>
                                        </div>
                                        <a href="#contact"
                                            class="ml-auto w-10 h-10 rounded-full border-2 border-white/30 flex items-center justify-center hover:bg-white hover:text-[#FA2964] transition-all duration-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                                <span
                                    class="absolute -bottom-6 -left-6 text-[6rem] font-serif font-bold text-white/5 pointer-events-none select-none">Hair</span>
                            </div>
                            <div class="md:col-span-2 relative h-full min-h-[280px]">
                                <img src="<?php echo e(asset('storage/hero/beauty-young-latin-woman-with-ideal-skin.png')); ?>"
                                    alt="Hair Styling"
                                    class="absolute inset-0 w-full h-full object-cover object-top transform group-hover:scale-110 transition-transform duration-700">
                                <div class="absolute inset-0 bg-gradient-to-l from-transparent to-[#FA2964]/20"></div>
                            </div>
                        </div>
                    </div>

                    
                    <div
                        class="group relative bg-[#F5D0B2] rounded-xl md:rounded-[2rem] overflow-hidden shadow-lg transition-transform duration-300 hover:scale-[1.02]">
                        <!-- Mobile: Vertical Stack -->
                        <div class="md:hidden">
                            <div class="relative aspect-[4/3] overflow-hidden">
                                <img src="<?php echo e(asset('storage/hero/woman-have-fun-with-cosmetic-brushes.png')); ?>"
                                    alt="Hair Coloring" class="w-full h-full object-cover object-center">
                                <div
                                    class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#F5D0B2]/80">
                                </div>
                            </div>
                            <div class="p-4 text-[#2C2C34]">
                                <h3 class="font-serif text-xl font-bold mb-1">Coloring</h3>
                                <p class="font-body text-[#8B5D66] text-xs mb-3">Vibrant colors for your personality.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-baseline">
                                        <span class="text-sm font-bold mr-0.5">$</span>
                                        <span class="font-serif text-3xl font-bold leading-none">95</span>
                                        <span class="text-[10px] opacity-70 ml-2">/ coloring</span>
                                    </div>
                                    <a href="#contact"
                                        class="w-9 h-9 rounded-full border-2 border-[#2C2C34]/20 flex items-center justify-center hover:bg-[#2C2C34] hover:text-[#F5D0B2] transition-all">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Desktop: Horizontal Layout -->
                        <div class="hidden md:grid md:grid-cols-5 h-full">
                            <div class="md:col-span-2 relative h-full min-h-[280px] order-1">
                                <img src="<?php echo e(asset('storage/hero/woman-have-fun-with-cosmetic-brushes.png')); ?>"
                                    alt="Hair Coloring"
                                    class="absolute inset-0 w-full h-full object-cover object-center transform group-hover:scale-110 transition-transform duration-700">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent to-[#F5D0B2]/20"></div>
                            </div>
                            <div
                                class="md:col-span-3 p-8 flex flex-col justify-center text-[#2C2C34] relative z-10 order-2">
                                <div>
                                    <h3 class="font-serif text-3xl lg:text-4xl font-bold mb-2 leading-tight">Coloring
                                    </h3>
                                    <p class="font-body text-[#8B5D66] text-base mb-6 max-w-xs">Awaken your personality
                                        with vibrant colors.</p>
                                    <div class="flex items-center gap-4">
                                        <div class="relative pl-5 border-l-2 border-[#8B5D66]/30">
                                            <div class="flex items-baseline">
                                                <span class="text-lg font-bold mr-0.5">$</span>
                                                <span class="font-serif text-5xl font-bold leading-none">95</span>
                                            </div>
                                            <span
                                                class="block text-[10px] uppercase tracking-wider text-[#8B5D66] mt-1">////
                                                coloring</span>
                                        </div>
                                        <a href="#contact"
                                            class="ml-auto w-10 h-10 rounded-full border-2 border-[#2C2C34]/20 flex items-center justify-center hover:bg-[#2C2C34] hover:text-[#F5D0B2] transition-all duration-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                                <span
                                    class="absolute -right-4 top-1/2 -translate-y-1/2 text-[6rem] font-serif font-bold text-white/20 pointer-events-none select-none md:rotate-0">Color</span>
                            </div>
                        </div>
                    </div>

                    
                    <div
                        class="group relative bg-[#EDEDF2] rounded-xl md:rounded-[2rem] overflow-hidden shadow-lg transition-transform duration-300 hover:scale-[1.02]">
                        <!-- Mobile: Vertical Stack -->
                        <div class="md:hidden">
                            <div class="relative aspect-[4/3] overflow-hidden">
                                <img src="<?php echo e(asset('storage/hero/flower-crown-woman-thinking-studio-wellness-cosmetics-organic-makeup-grey-background-eco-friendly-dermatology-calm-model-with-natural-beauty-shine-glowing-skin-aesthetic.png')); ?>"
                                    alt="Hydration Treatments" class="w-full h-full object-cover object-center">
                                <div
                                    class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#EDEDF2]/80">
                                </div>
                            </div>
                            <div class="p-4 text-[#2C2C34]">
                                <h3 class="font-serif text-xl font-bold mb-1">Hydration</h3>
                                <p class="font-body text-[#8B5D66] text-xs mb-3">Luxury hydration for natural shine.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-baseline">
                                        <span class="text-sm font-bold mr-0.5">$</span>
                                        <span class="font-serif text-3xl font-bold leading-none">125</span>
                                        <span class="text-[10px] opacity-70 ml-2">/ treatment</span>
                                    </div>
                                    <a href="#contact"
                                        class="w-9 h-9 rounded-full border-2 border-[#2C2C34]/20 flex items-center justify-center hover:bg-[#2C2C34] hover:text-white transition-all">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Desktop: Horizontal Layout -->
                        <div class="hidden md:grid md:grid-cols-5 h-full">
                            <div class="md:col-span-3 p-8 flex flex-col justify-center text-[#2C2C34] relative z-10">
                                <div>
                                    <h3 class="font-serif text-3xl lg:text-4xl font-bold mb-2 leading-tight">Hydration
                                    </h3>
                                    <p class="font-body text-[#8B5D66] text-base mb-6 max-w-xs">Luxury hydration for
                                        natural shine.</p>
                                    <div class="flex items-center gap-4">
                                        <div class="relative pl-5 border-l-2 border-[#2C2C34]/20">
                                            <div class="flex items-baseline">
                                                <span class="text-lg font-bold mr-0.5">$</span>
                                                <span class="font-serif text-5xl font-bold leading-none">125</span>
                                            </div>
                                            <span
                                                class="block text-[10px] uppercase tracking-wider text-[#8B5D66] mt-1">////
                                                hydration</span>
                                        </div>
                                        <a href="#contact"
                                            class="ml-auto w-10 h-10 rounded-full border-2 border-[#2C2C34]/20 flex items-center justify-center hover:bg-[#2C2C34] hover:text-white transition-all duration-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="md:col-span-2 relative h-full min-h-[280px]">
                                <img src="<?php echo e(asset('storage/hero/flower-crown-woman-thinking-studio-wellness-cosmetics-organic-makeup-grey-background-eco-friendly-dermatology-calm-model-with-natural-beauty-shine-glowing-skin-aesthetic.png')); ?>"
                                    alt="Hydration Treatments"
                                    class="absolute inset-0 w-full h-full object-cover object-center transform group-hover:scale-110 transition-transform duration-700">
                                <div class="absolute inset-0 bg-gradient-to-l from-transparent to-[#EDEDF2]/20"></div>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="hidden lg:block lg:col-span-5 sticky top-32 space-y-10 pl-6 border-l border-[#DCC7A1]/30">
                    <div class="space-y-6">
                        <span
                            class="inline-block px-4 py-1.5 rounded-full bg-[#FA2964]/10 text-[#FA2964] text-xs font-bold uppercase tracking-wider">Why
                            Choose Us</span>
                        <h3 class="font-serif text-4xl font-bold text-[#2C2C34] leading-tight">
                            Elevate Your <br> <span class="text-[#C85E78] italic">Beauty Routine</span>
                        </h3>
                        <p class="font-body text-[#4A4A52] text-lg leading-relaxed">
                            At InStyle, we believe beauty is an art form. Our dedicated team of professionals uses only
                            the finest products to ensure you leave feeling radiant and confident.
                        </p>
                    </div>

                    <ul class="space-y-6">
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-10 h-10 rounded-full bg-[#F7E9E6] flex items-center justify-center mt-1">
                                <svg class="w-5 h-5 text-[#C85E78]" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-xl font-serif font-bold text-[#2C2C34]">Premium Products</h4>
                                <p class="text-[#4A4A52] text-sm mt-1">We simply use the best organic and luxury brands
                                    available.</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-10 h-10 rounded-full bg-[#F7E9E6] flex items-center justify-center mt-1">
                                <svg class="w-5 h-5 text-[#C85E78]" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-xl font-serif font-bold text-[#2C2C34]">Relaxing Atmosphere</h4>
                                <p class="text-[#4A4A52] text-sm mt-1">Escape the city noise in our serene, designed
                                    sanctuary.</p>
                            </div>
                        </li>
                    </ul>

                    <div class="pt-6">
                        <a href="#contact"
                            class="inline-flex items-center justify-center px-8 py-4 text-white font-body font-bold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                            style="background: linear-gradient(135deg, #E98CA5 0%, #C85E78 100%);">
                            Book Full Consultation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <section id="gallery" class="py-24 bg-white">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
            
            <div class="text-center max-w-3xl mx-auto mb-16">
                <div class="inline-block mb-4">
                    <span
                        class="text-[#C85E78] uppercase tracking-[0.3em] text-sm font-body font-medium">Portfolio</span>
                    <div class="h-px w-20 bg-gradient-to-r from-[#DCC7A1] to-transparent mt-2 mx-auto"></div>
                </div>
                <h2 class="font-serif text-4xl md:text-5xl font-bold text-[#2C2C34] mb-6">
                    Our Work Speaks <span class="text-[#C85E78]">Beauty</span>
                </h2>
            </div>

            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php $__currentLoopData = ['makeup-tools.png', 'woman-have-fun-with-cosmetic-brushes.png', 'beauty-young-latin-woman-with-ideal-skin.png', 'flower-crown-woman-thinking-studio-wellness-cosmetics-organic-makeup-grey-background-eco-friendly-dermatology-calm-model-with-natural-beauty-shine-glowing-skin-aesthetic.png']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div
                        class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 <?php echo e($index === 3 ? 'md:col-span-2 lg:col-span-1' : ''); ?>">
                        <img src="<?php echo e(asset('storage/hero/' . $image)); ?>" alt="Gallery Image"
                            class="w-full h-80 object-cover transform group-hover:scale-110 transition-transform duration-500">
                        
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-[#C85E78]/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-6">
                            <p class="text-white font-serif text-xl font-semibold">View Details</p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
            </div>
        </div>
    </section>

    
    <section class="py-24 bg-gradient-to-b from-[#F7E9E6] to-[#EFEFEF]">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
            
            <div class="text-center max-w-3xl mx-auto mb-16">
                <div class="inline-block mb-4">
                    <span
                        class="text-[#C85E78] uppercase tracking-[0.3em] text-sm font-body font-medium">Testimonials</span>
                    <div class="h-px w-20 bg-gradient-to-r from-[#DCC7A1] to-transparent mt-2 mx-auto"></div>
                </div>
                <h2 class="font-serif text-4xl md:text-5xl font-bold text-[#2C2C34] mb-6">
                    Loved By Our <span class="text-[#C85E78]">Clients</span>
                </h2>
            </div>

            
            <div class="grid md:grid-cols-3 gap-8">
                
                <div class="bg-white rounded-2xl p-8 shadow-lg">
                    <div class="flex gap-1 mb-4">
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($i = 0; $i < 5; $i++): ?>
                            <svg class="w-5 h-5 text-[#DCC7A1]" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </div>
                    <p class="font-body text-[#4A4A52] mb-6 italic leading-relaxed">
                        "Absolutely stunning results! The team at InStyle transformed my look for my wedding day. I felt
                        like a princess!"
                    </p>
                    <div class="flex items-center gap-4">
                        <div
                            class="w-12 h-12 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center text-white font-serif font-bold">
                            S
                        </div>
                        <div>
                            <h4 class="font-body font-semibold text-[#2C2C34]">Sarah Johnson</h4>
                            <p class="font-body text-sm text-[#8B5D66]">Bridal Client</p>
                        </div>
                    </div>
                </div>

                
                <div class="bg-white rounded-2xl p-8 shadow-lg">
                    <div class="flex gap-1 mb-4">
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($i = 0; $i < 5; $i++): ?>
                            <svg class="w-5 h-5 text-[#DCC7A1]" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </div>
                    <p class="font-body text-[#4A4A52] mb-6 italic leading-relaxed">
                        "The most luxurious spa experience I've ever had. Every detail is perfect, from the ambiance to
                        the treatments."
                    </p>
                    <div class="flex items-center gap-4">
                        <div
                            class="w-12 h-12 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center text-white font-serif font-bold">
                            M
                        </div>
                        <div>
                            <h4 class="font-body font-semibold text-[#2C2C34]">Maria Rodriguez</h4>
                            <p class="font-body text-sm text-[#8B5D66]">Regular Client</p>
                        </div>
                    </div>
                </div>

                
                <div class="bg-white rounded-2xl p-8 shadow-lg">
                    <div class="flex gap-1 mb-4">
                        <?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if BLOCK]><![endif]--><?php endif; ?><?php for($i = 0; $i < 5; $i++): ?>
                            <svg class="w-5 h-5 text-[#DCC7A1]" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        <?php endfor; ?><?php if(\Livewire\Mechanisms\ExtendBlade\ExtendBlade::isRenderingLivewireComponent()): ?><!--[if ENDBLOCK]><![endif]--><?php endif; ?>
                    </div>
                    <p class="font-body text-[#4A4A52] mb-6 italic leading-relaxed">
                        "My hair has never looked better! The stylists really know their craft. Highly recommend InStyle
                        to everyone!"
                    </p>
                    <div class="flex items-center gap-4">
                        <div
                            class="w-12 h-12 rounded-full bg-gradient-to-br from-[#E98CA5] to-[#F4B6C5] flex items-center justify-center text-white font-serif font-bold">
                            E
                        </div>
                        <div>
                            <h4 class="font-body font-semibold text-[#2C2C34]">Emily Chen</h4>
                            <p class="font-body text-sm text-[#8B5D66]">Hair Styling Client</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <section class="py-24 relative overflow-hidden"
        style="background: linear-gradient(135deg, #E98CA5 0%, #C85E78 100%);">
        
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0"
                style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.4&quot;%3E%3Cpath d=&quot;M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');">
            </div>
        </div>

        <div class="relative z-10 max-w-4xl mx-auto px-6 lg:px-8 text-center">
            <h2 class="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                Book Your Luxury Experience
            </h2>
            <p class="font-body text-xl text-white/90 mb-10 max-w-2xl mx-auto">
                Transform your look with our premium beauty services. Schedule your appointment today and discover the
                InStyle difference.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#"
                    class="inline-flex items-center justify-center px-10 py-5 bg-white text-[#C85E78] font-body font-bold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#F7E9E6]">
                    Book Appointment
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </a>
                <a href="#"
                    class="inline-flex items-center justify-center px-10 py-5 bg-transparent text-white font-body font-bold rounded-full border-2 border-white hover:bg-white/10 transition-all duration-300">
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    
    
    <?php if (isset($component)) { $__componentOriginal8a8716efb3c62a45938aca52e78e0322 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a8716efb3c62a45938aca52e78e0322 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footer','data' => ['id' => 'contact']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'contact']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a8716efb3c62a45938aca52e78e0322)): ?>
<?php $attributes = $__attributesOriginal8a8716efb3c62a45938aca52e78e0322; ?>
<?php unset($__attributesOriginal8a8716efb3c62a45938aca52e78e0322); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a8716efb3c62a45938aca52e78e0322)): ?>
<?php $component = $__componentOriginal8a8716efb3c62a45938aca52e78e0322; ?>
<?php unset($__componentOriginal8a8716efb3c62a45938aca52e78e0322); ?>
<?php endif; ?>

</body>

</html><?php /**PATH C:\laragon\www\Instyle\resources\views\landing.blade.php ENDPATH**/ ?>