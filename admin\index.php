<?php
/**
 * Medical Admin Dashboard
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../config/app.php';
} catch (Exception $e) {
    die("Medical System Configuration Error: " . $e->getMessage());
}

// Check if auth object exists
if (!isset($auth)) {
    die("Error: Medical authentication system not found. Please check config/app.php");
}

// Require medical admin authentication
$auth->requireRole('ADMIN');

// Get medical dashboard statistics
$stats = [];

// Total patients
$stats['patients'] = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'CUSTOMER'")['count'];

// Total appointments
$stats['appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings")['count'];

// Pending appointments
$stats['pending_appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status = 'PENDING'")['count'];

// Today's appointments
$stats['today_appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE DATE(date) = CURDATE()")['count'];

// Recent medical appointments
$recentAppointments = $database->fetchAll("
    SELECT
        b.id,
        b.user_id,
        b.service_id,
        b.package_id,
        b.staff_id,
        b.date,
        b.start_time,
        b.end_time,
        b.status,

        b.notes,
        b.created_at,
        b.updated_at,
        u.name as patient_name,
        u.email as patient_email,
        s.name as treatment_name,
        st.name as specialist_name
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    ORDER BY b.created_at DESC
    LIMIT 8
");

// Treatment popularity (informational only)
$popularTreatments = $database->fetchAll("
    SELECT
        s.name as treatment_name,
        COUNT(b.id) as booking_count
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    WHERE b.status = 'COMPLETED'
    AND b.date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY s.id, s.name
    ORDER BY booking_count DESC
    LIMIT 5
");

$pageTitle = "Medical Admin Dashboard";
include __DIR__ . '/../includes/admin_header.php';
?>

<!-- Medical Dashboard CSS -->
<style>
/* Medical Dashboard Specific Styles */
.medical-stat-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-stat-card:hover::before {
    left: 100%;
}

.medical-stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.medical-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.medical-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px var(--shadow-primary);
}

.medical-appointment-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.medical-appointment-card:hover {
    transform: translateX(5px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-quick-action {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-quick-action::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-soft);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.medical-quick-action:hover::after {
    opacity: 1;
}

.medical-quick-action:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.medical-quick-action > * {
    position: relative;
    z-index: 2;
}

.status-badge-pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-badge-confirmed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge-completed {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
    color: var(--primary-green);
    border: 1px solid rgba(73, 167, 92, 0.3);
}

.status-badge-cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medical-chart-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2rem;
}

@media (max-width: 768px) {
    .medical-stat-card {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .medical-icon {
        width: 50px;
        height: 50px;
    }
    
    .medical-quick-action {
        padding: 1.5rem;
        border-radius: 16px;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    
                    <!-- Medical Dashboard Header -->
                    <div class="mb-8">
                        <div class="medical-glass border border-redolence-green/20 rounded-2xl p-6">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                <div>
                                    <h1 class="text-3xl font-bold text-redolence-navy">Medical Admin Dashboard</h1>
                                    <p class="mt-2 text-gray-600">Comprehensive overview of your medical aesthetics operations</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button class="inline-flex items-center px-5 py-2.5 rounded-xl bg-redolence-green text-white hover:bg-redolence-green/90 transition-all font-semibold text-sm uppercase tracking-wide" onclick="window.location.href='<?= getBasePath() ?>/admin/bookings/create.php'">
                                        New Appointment
                                    </button>
                                    <button class="inline-flex items-center px-5 py-2.5 rounded-xl bg-white text-redolence-blue border border-redolence-blue hover:bg-gray-50 transition-all font-semibold text-sm uppercase tracking-wide" onclick="window.location.href='<?= getBasePath() ?>/admin/customers/create.php'">
                                        Add Patient
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Key Medical Statistics -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        <!-- Total Patients -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v2h5v-2zm.645-5.785a6 6 0 119.71 0M12 3v.01M12 12a9 9 0 100-6" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900"><?= number_format($stats['patients']) ?></div>
                                    <div class="text-sm text-gray-500">Total Patients</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Total Appointments -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900"><?= number_format($stats['appointments']) ?></div>
                                    <div class="text-sm text-gray-500">Total Appointments</div>
                                </div>
                            </div>
                        </div>
                        

                        
                        <!-- Pending Appointments -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900"><?= number_format($stats['pending_appointments']) ?></div>
                                    <div class="text-sm text-gray-500">Pending Appointments</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Stats -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 mb-8">
                        <!-- Today's Appointments -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Today's Appointments</h3>
                                <a href="<?= getBasePath() ?>/admin/bookings" class="text-sm text-redolence-blue hover:underline">View All</a>
                            </div>
                            <div class="text-4xl font-bold text-redolence-green mb-2"><?= number_format($stats['today_appointments']) ?></div>
                            <div class="text-sm text-gray-500">Appointments Today</div>
                        </div>
                        

                    </div>

                    <!-- Charts and Visualizations -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        
                        <!-- Popular Treatments -->
                        <div class="medical-chart-container overflow-hidden">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Popular Treatments</h3>
                                <div class="text-sm text-gray-500">Last 30 Days</div>
                            </div>
                            <div class="overflow-x-auto h-80 pb-4" id="treatmentsTableContainer">
                                <?php if (!empty($popularTreatments)): ?>
                                    <table class="min-w-full divide-y divide-gray-200 border border-gray-100 rounded-xl overflow-hidden">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Treatment</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>

                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($popularTreatments as $treatment): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?= htmlspecialchars($treatment['treatment_name']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= number_format($treatment['booking_count']) ?></td>

                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php else: ?>
                                    <div class="text-center py-12">
                                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-gray-500">No treatment data available for the selected period.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Appointments -->
                    <div class="medical-glass border border-redolence-green/20 rounded-2xl mb-8 overflow-hidden">
                        <div class="px-6 py-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold text-redolence-navy">Recent Medical Appointments</h3>
                                    <p class="text-gray-500 text-sm mt-1">Latest booked appointments and status</p>
                                </div>
                                <a href="<?= getBasePath() ?>/admin/bookings" class="inline-flex items-center px-5 py-2.5 rounded-xl bg-white text-redolence-blue border border-redolence-blue hover:bg-gray-50 transition-all font-semibold text-sm uppercase tracking-wide">View All Bookings</a>
                            </div>
                            <div class="mt-6 overflow-x-auto max-h-[500px] pb-4" id="appointmentsTableContainer">
                                <?php if (!empty($recentAppointments)): ?>
                                    <table class="min-w-full divide-y divide-gray-200 border border-gray-100 rounded-xl overflow-hidden">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Treatment</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialist</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>

                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($recentAppointments as $appointment): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($appointment['patient_name']) ?></div>
                                                        <div class="text-sm text-gray-500"><?= htmlspecialchars($appointment['patient_email']) ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= htmlspecialchars($appointment['treatment_name']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= htmlspecialchars($appointment['specialist_name']) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= date('M d, Y', strtotime($appointment['date'])) ?> at <?= date('h:i A', strtotime($appointment['start_time'])) ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <?php
                                                        $statusClass = '';
                                                        switch ($appointment['status']) {
                                                            case 'PENDING':
                                                                $statusClass = 'bg-yellow-100 text-yellow-800';
                                                                break;
                                                            case 'CONFIRMED':
                                                                $statusClass = 'bg-blue-100 text-blue-800';
                                                                break;
                                                            case 'COMPLETED':
                                                                $statusClass = 'bg-green-100 text-green-800';
                                                                break;
                                                            case 'CANCELLED':
                                                                $statusClass = 'bg-red-100 text-red-800';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                                            <?= htmlspecialchars($appointment['status']) ?>
                                                        </span>
                                                    </td>

                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php else: ?>
                                    <div class="text-center py-12">
                                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-gray-500">No recent appointments found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Quick Actions -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        <a href="<?= getBasePath() ?>/admin/bookings" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-redolence-navy">Manage Appointments</div>
                                    <div class="text-sm text-gray-500">View and update bookings</div>
                                </div>
                            </div>
                        </a>
                        <a href="<?= getBasePath() ?>/admin/customers" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-redolence-navy">Patient Directory</div>
                                    <div class="text-sm text-gray-500">Manage patient records</div>
                                </div>
                            </div>
                        </a>
                        <a href="<?= getBasePath() ?>/admin/services" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-redolence-navy">Treatment Catalog</div>
                                    <div class="text-sm text-gray-500">Manage aesthetic services</div>
                                </div>
                            </div>
                        </a>
                        <a href="<?= getBasePath() ?>/admin/staff" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-redolence-navy">Staff Management</div>
                                    <div class="text-sm text-gray-500">Manage medical professionals</div>
                                </div>
                            </div>
                        </a>
                        <a href="<?= getBasePath() ?>/admin/rooms" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon mr-4">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-redolence-navy">Room Management</div>
                                    <div class="text-sm text-gray-500">Manage treatment facilities</div>
                                </div>
                            </div>
                        </a>

                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>