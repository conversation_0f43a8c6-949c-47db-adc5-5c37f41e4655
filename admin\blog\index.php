<?php
/**
 * Admin Blog Post Management
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/blog_functions.php'; // We'll create this next

// Require admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createBlogPost($_POST, $_FILES['image_url'] ?? null);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
                $_SESSION['form_data'] = $_POST;
            }
            break;
            
        case 'update':
            $result = updateBlogPost($_POST['id'], $_POST, $_FILES['image_url'] ?? null);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
                $_SESSION['form_data'] = $_POST;
            }
            break;
            
        case 'delete':
            $result = deleteBlogPost($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post deleted successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/blog');
}

// Get all blog posts with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$statusFilter = sanitize($_GET['status'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($statusFilter) {
    $whereClause .= " AND status = ?";
    $params[] = $statusFilter;
}

$blogPosts = $database->fetchAll(
    "SELECT bp.*, u.name as author_name
     FROM blog_posts bp
     LEFT JOIN users u ON bp.author_id = u.id
     $whereClause
     ORDER BY bp.publish_date DESC, bp.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalBlogPosts = $database->fetch(
    "SELECT COUNT(*) as count FROM blog_posts $whereClause",
    $params
)['count'];

$totalPages = ceil($totalBlogPosts / $limit);

// Get blog statistics
$blogStats = [
    'total' => $database->fetch("SELECT COUNT(*) as count FROM blog_posts")['count'],
    'published' => $database->fetch("SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'")['count'],
    'draft' => $database->fetch("SELECT COUNT(*) as count FROM blog_posts WHERE status = 'draft'")['count'],
    'archived' => $database->fetch("SELECT COUNT(*) as count FROM blog_posts WHERE status = 'archived'")['count']
];

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Blog Post Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Blog Management CSS -->
<style>
/* Medical Blog Management Specific Styles */
.medical-blog-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-blog-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-blog-card:hover::before {
    left: 100%;
}

.medical-blog-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-blog-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-blog-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-blog-item:hover::before {
    transform: scaleX(1);
}

.medical-blog-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-blog-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.blog-status-published {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.blog-status-draft {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.blog-status-archived {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.medical-stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.85));
    border: 1px solid rgba(73, 167, 92, 0.12);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.medical-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2d6a3e);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.medical-stats-card:hover::before {
    transform: scaleX(1);
}

.medical-stats-card:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.25);
    box-shadow: 0 12px 32px rgba(73, 167, 92, 0.15);
}

@media (max-width: 768px) {
    .medical-blog-grid {
        grid-template-columns: 1fr;
    }

    .medical-blog-card {
        border-radius: 16px;
        padding: 1rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-blog-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Blog Content
                                    <span class="text-redolence-green">Management</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Professional blog post creation and publication management system</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        <?= $blogStats['total'] ?> Total Posts
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        Content Publishing
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0">
                                <button onclick="openCreateModal()" class="medical-btn-primary text-lg px-6 py-3">
                                    + Create Blog Post
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Posts</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $blogStats['total'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Published</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $blogStats['published'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Draft</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $blogStats['draft'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="medical-stats-card">
                            <div class="flex items-center">
                                <div class="p-4 rounded-full bg-gradient-to-br from-gray-100 to-gray-200">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Archived</p>
                                    <p class="text-2xl font-bold text-redolence-navy"><?= $blogStats['archived'] ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Filters -->
                    <div class="medical-filter-card p-8 mb-8">
                        <h2 class="text-xl font-bold text-redolence-navy mb-6">Filter & Search Blog Posts</h2>
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                       placeholder="Search posts by title, summary, content..." 
                                       class="medical-form-input w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status</label>
                                <select name="status" class="medical-form-input w-full">
                                    <option value="">All Statuses</option>
                                    <option value="draft" <?= $statusFilter === 'draft' ? 'selected' : '' ?>>Draft</option>
                                    <option value="published" <?= $statusFilter === 'published' ? 'selected' : '' ?>>Published</option>
                                    <option value="archived" <?= $statusFilter === 'archived' ? 'selected' : '' ?>>Archived</option>
                                </select>
                            </div>
                            <div class="flex items-end gap-3">
                                <button type="submit" class="medical-btn-primary">
                                    Apply Filters
                                </button>
                                <?php if ($search || $statusFilter): ?>
                                    <a href="<?= getBasePath() ?>/admin/blog" class="medical-btn-secondary">
                                        Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>

                    <!-- Medical Blog Posts Grid -->
                    <?php if (empty($blogPosts)): ?>
                        <div class="medical-blog-card p-12 text-center">
                            <div class="max-w-md mx-auto">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Blog Posts Found</h3>
                                <p class="text-gray-600 mb-6">Get started by creating your first blog post to share medical insights and updates.</p>
                                <button onclick="openCreateModal()" class="medical-btn-primary">
                                    Create First Blog Post
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="medical-blog-grid grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                            <?php foreach ($blogPosts as $post): ?>
                                <div class="medical-blog-item">
                                    <!-- Post Header -->
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <span class="medical-blog-type-badge blog-status-<?= strtolower($post['status']) ?> mr-3">
                                                    <?= ucfirst($post['status']) ?>
                                                </span>
                                                <?php if ($post['author_name']): ?>
                                                    <span class="text-xs text-gray-500">by <?= htmlspecialchars($post['author_name']) ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <h3 class="text-xl font-bold text-redolence-navy mb-2">
                                                <?= htmlspecialchars($post['title']) ?>
                                            </h3>
                                            <?php if ($post['summary']): ?>
                                                <p class="text-gray-600 text-sm line-clamp-2 mb-2">
                                                    <?= htmlspecialchars($post['summary']) ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($post['image_url']): ?>
                                            <div class="ml-4 flex-shrink-0">
                                                <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>" 
                                                     alt="<?= htmlspecialchars($post['title']) ?>" 
                                                     class="w-20 h-20 object-cover rounded-lg">
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Post Details -->
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Publish Date</p>
                                            <p class="text-sm text-redolence-navy font-semibold">
                                                <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : 'Not Set' ?>
                                            </p>
                                        </div>
                                        <div>
                                            <p class="text-xs font-semibold text-redolence-green uppercase tracking-wide mb-1">Created</p>
                                            <p class="text-sm text-redolence-navy font-semibold">
                                                <?= date('M j, Y', strtotime($post['created_at'])) ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex gap-2 pt-4 border-t border-gray-200">
                                        <button onclick="editBlogPost('<?= $post['id'] ?>')" 
                                                class="flex-1 medical-btn-secondary text-sm px-3 py-2">
                                            Edit
                                        </button>
                                        <button onclick="deleteBlogPostConfirm('<?= $post['id'] ?>', '<?= htmlspecialchars(addslashes($post['title'])) ?>')" 
                                                class="flex-1 medical-btn-danger text-sm px-3 py-2">
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Medical Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="medical-blog-card p-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    Showing <span class="font-semibold text-redolence-navy"><?= $offset + 1 ?></span> to
                                    <span class="font-semibold text-redolence-navy"><?= min($offset + $limit, $totalBlogPosts) ?></span> of
                                    <span class="font-semibold text-redolence-navy"><?= $totalBlogPosts ?></span> results
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>"
                                           class="px-4 py-2 text-sm rounded-lg transition-colors <?= $i === $page ? 'medical-btn-primary' : 'medical-btn-secondary' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>"
                                           class="medical-btn-secondary text-sm px-4 py-2">
                                            Next
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Enhanced Create/Edit Blog Post Modal -->
<div id="blogPostModal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
    <div class="bg-white rounded-2xl shadow-2xl border-2 border-gray-100 w-full max-w-4xl mx-auto max-h-full overflow-y-auto">
        <div class="px-8 py-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 id="modalTitle" class="text-2xl font-bold text-redolence-navy">Add Blog Post</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <form id="blogPostForm" method="POST" enctype="multipart/form-data" class="p-8">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="postId">
            <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

            <div class="space-y-6">
                <div>
                    <label for="title" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Title *</label>
                    <input type="text" name="title" id="title" required class="medical-form-input w-full">
                </div>

                <div>
                    <label for="summary" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Summary</label>
                    <textarea name="summary" id="summary" rows="3" class="medical-form-input w-full resize-none"></textarea>
                </div>

                <div>
                    <label for="full_content" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Full Content *</label>
                    <textarea name="full_content" id="full_content" rows="10" required class="medical-form-input w-full resize-none"></textarea>
                    <p class="text-xs text-gray-500 mt-2">You can use basic HTML tags for formatting.</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="image_url_option" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Image</label>
                        <select name="image_url_option" id="image_url_option" class="medical-form-input w-full mb-3">
                            <option value="url">Enter Image URL</option>
                            <option value="upload">Upload Image</option>
                        </select>

                        <div id="image_url_field">
                            <input type="url" name="image_url_input" id="image_url_input" placeholder="https://example.com/image.jpg"
                                   class="medical-form-input w-full">
                        </div>
                        <div id="image_upload_field" class="hidden">
                            <input type="file" name="image_url" id="image_upload_input" accept="image/*"
                                   class="medical-form-input w-full">
                            <p class="text-xs text-gray-500 mt-2">Max 5MB. Recommended: JPG, PNG, WebP.</p>
                        </div>
                        <input type="hidden" name="existing_image_url" id="existing_image_url">
                        <div id="currentImagePreview" class="mt-3"></div>
                    </div>

                    <div>
                        <label for="publish_date" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Publish Date</label>
                        <input type="datetime-local" name="publish_date" id="publish_date" class="medical-form-input w-full">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="author_id" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Author</label>
                        <select name="author_id" id="author_id" class="medical-form-input w-full">
                            <option value="">Select Author (Optional)</option>
                            <?php
                            $staffMembers = $database->fetchAll("SELECT id, name FROM users WHERE role IN ('ADMIN', 'STAFF') AND is_active = 1 ORDER BY name ASC");
                            foreach ($staffMembers as $staff): ?>
                                <option value="<?= $staff['id'] ?>"><?= htmlspecialchars($staff['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Status *</label>
                        <select name="status" id="status" required class="medical-form-input w-full">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                            <option value="archived">Archived</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-4 mt-8 pt-6 border-t border-gray-200">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Save Blog Post
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[60] backdrop-blur-sm">
    <div class="bg-white rounded-2xl shadow-2xl border-2 border-red-200 w-full max-w-lg mx-4">
        <div class="p-8">
            <div class="flex items-center mb-6">
                <div class="flex-shrink-0 mr-4">
                    <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <h3 id="deleteModalTitle" class="text-xl font-bold text-redolence-navy">Delete Blog Post</h3>
            </div>
            <div id="deleteMessage" class="text-gray-600 mb-8"></div>
            <div class="flex gap-4">
                <button id="deleteCancel" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
                <button id="deleteConfirm" class="flex-1 medical-btn-danger">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Store form data from session if available
const formErrorData = <?= isset($_SESSION['form_data']) ? json_encode($_SESSION['form_data']) : 'null' ?>;
<?php unset($_SESSION['form_data']); ?>

function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Blog Post';
    document.getElementById('formAction').value = 'create';
    document.getElementById('blogPostForm').reset();
    document.getElementById('postId').value = '';
    document.getElementById('status').value = 'draft';
    document.getElementById('currentImagePreview').innerHTML = '';
    document.getElementById('existing_image_url').value = '';
    
    // Repopulate form if there was an error
    if (formErrorData && formErrorData.action === 'create') {
        for (const key in formErrorData) {
            const field = document.getElementById(key);
            if (field) {
                field.value = formErrorData[key];
            }
        }
    }
    
    // Reset image input type
    document.getElementById('image_url_option').value = 'url';
    toggleImageInput();

    document.getElementById('blogPostModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('blogPostModal').classList.add('hidden');
}

function editBlogPost(postId) {
    fetch(`<?= getBasePath() ?>/api/admin/blog.php?action=get&id=${postId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.post) {
                const post = data.post;
                document.getElementById('modalTitle').textContent = 'Edit Blog Post';
                document.getElementById('formAction').value = 'update';
                document.getElementById('postId').value = post.id;
                document.getElementById('title').value = post.title;
                document.getElementById('summary').value = post.summary || '';
                document.getElementById('full_content').value = post.full_content;
                
                // Handle image URL or uploaded file
                document.getElementById('existing_image_url').value = post.image_url || '';
                const imagePreview = document.getElementById('currentImagePreview');
                imagePreview.innerHTML = '';
                if (post.image_url) {
                    const imgTag = document.createElement('img');
                    imgTag.src = post.image_url.startsWith('http') ? post.image_url : `<?= getBasePath() ?>/uploads/blog/${post.image_url}`;
                    imgTag.alt = 'Current Image';
                    imgTag.className = 'w-32 h-32 object-cover rounded-lg border-2 border-gray-200';
                    imagePreview.appendChild(imgTag);

                    if (post.image_url.startsWith('http')) {
                        document.getElementById('image_url_option').value = 'url';
                        document.getElementById('image_url_input').value = post.image_url;
                    } else {
                        document.getElementById('image_url_option').value = 'upload';
                    }
                } else {
                    document.getElementById('image_url_option').value = 'url';
                }
                toggleImageInput();

                document.getElementById('publish_date').value = post.publish_date ? post.publish_date.replace(' ', 'T') : '';
                document.getElementById('author_id').value = post.author_id || '';
                document.getElementById('status').value = post.status;

                // Repopulate form if there was an error during update attempt
                if (formErrorData && formErrorData.action === 'update' && formErrorData.id === postId) {
                    for (const key in formErrorData) {
                        const field = document.getElementById(key);
                        if (field) {
                            field.value = formErrorData[key];
                        }
                    }
                }

                document.getElementById('blogPostModal').classList.remove('hidden');
            } else {
                alert('Error fetching blog post details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error fetching blog post:', error);
            alert('Failed to load blog post data.');
        });
}

function deleteBlogPostConfirm(postId, postTitle) {
    const deleteModal = document.getElementById('deleteModal');
    document.getElementById('deleteModalTitle').textContent = 'Delete Blog Post';
    document.getElementById('deleteMessage').innerHTML = `<p>Are you sure you want to delete the blog post titled "<strong>${postTitle}</strong>"?</p><p class="text-sm text-gray-500 mt-2">This action cannot be undone.</p>`;
    
    const confirmBtn = document.getElementById('deleteConfirm');
    confirmBtn.onclick = () => {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= getBasePath() ?>/admin/blog/';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';
        form.appendChild(actionInput);
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = postId;
        form.appendChild(idInput);

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= generateCsrfToken() ?>';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
        deleteModal.classList.add('hidden');
    };
    
    document.getElementById('deleteCancel').onclick = () => {
        deleteModal.classList.add('hidden');
    };
    
    deleteModal.classList.remove('hidden');
}

// Image input switcher
document.getElementById('image_url_option').addEventListener('change', toggleImageInput);

function toggleImageInput() {
    const option = document.getElementById('image_url_option').value;
    const urlField = document.getElementById('image_url_field');
    const uploadField = document.getElementById('image_upload_field');
    const urlInput = document.getElementById('image_url_input');
    const uploadInput = document.getElementById('image_upload_input');

    if (option === 'url') {
        urlField.classList.remove('hidden');
        uploadField.classList.add('hidden');
        uploadInput.value = '';
    } else {
        urlField.classList.add('hidden');
        uploadField.classList.remove('hidden');
        urlInput.value = '';
    }
}

// Initialize image input state on load
document.addEventListener('DOMContentLoaded', function() {
    toggleImageInput();
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        const deleteModal = document.getElementById('deleteModal');
        if (!deleteModal.classList.contains('hidden')) {
            deleteModal.classList.add('hidden');
        }
    }
});

// Close modal on backdrop click
document.getElementById('blogPostModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>